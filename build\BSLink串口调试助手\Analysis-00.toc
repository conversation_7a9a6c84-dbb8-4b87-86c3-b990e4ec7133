(['D:\\wenjian\\BS-Link\\BSmain.py'],
 ['D:\\wenjian\\BS-Link'],
 [],
 [('D:\\wenjian\\BS-Link\\.venv\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks',
   -1000),
  ('D:\\wenjian\\BS-Link\\.venv\\lib\\site-packages\\_pyinstaller_hooks_contrib',
   -1000)],
 {},
 [],
 [],
 False,
 {},
 0,
 [],
 [],
 '3.8.8 (tags/v3.8.8:024d805, Feb 19 2021, 13:18:16) [MSC v.1928 64 bit '
 '(AMD64)]',
 [('pyi_rth_pyqt5',
   'D:\\wenjian\\BS-Link\\.venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pyqt5.py',
   'PYSOURCE'),
  ('pyi_rth_pkgutil',
   'D:\\wenjian\\BS-Link\\.venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgutil.py',
   'PYSOURCE'),
  ('pyi_rth_inspect',
   'D:\\wenjian\\BS-Link\\.venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_inspect.py',
   'PYSOURCE'),
  ('BSmain', 'D:\\wenjian\\BS-Link\\BSmain.py', 'PYSOURCE')],
 [('zipfile', 'C:\\Program Files\\Python38\\lib\\zipfile.py', 'PYMODULE'),
  ('argparse', 'C:\\Program Files\\Python38\\lib\\argparse.py', 'PYMODULE'),
  ('textwrap', 'C:\\Program Files\\Python38\\lib\\textwrap.py', 'PYMODULE'),
  ('copy', 'C:\\Program Files\\Python38\\lib\\copy.py', 'PYMODULE'),
  ('gettext', 'C:\\Program Files\\Python38\\lib\\gettext.py', 'PYMODULE'),
  ('py_compile', 'C:\\Program Files\\Python38\\lib\\py_compile.py', 'PYMODULE'),
  ('importlib.machinery',
   'C:\\Program Files\\Python38\\lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('importlib',
   'C:\\Program Files\\Python38\\lib\\importlib\\__init__.py',
   'PYMODULE'),
  ('importlib._bootstrap',
   'C:\\Program Files\\Python38\\lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   'C:\\Program Files\\Python38\\lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.metadata',
   'C:\\Program Files\\Python38\\lib\\importlib\\metadata.py',
   'PYMODULE'),
  ('importlib.abc',
   'C:\\Program Files\\Python38\\lib\\importlib\\abc.py',
   'PYMODULE'),
  ('configparser',
   'C:\\Program Files\\Python38\\lib\\configparser.py',
   'PYMODULE'),
  ('pathlib', 'C:\\Program Files\\Python38\\lib\\pathlib.py', 'PYMODULE'),
  ('urllib.parse',
   'C:\\Program Files\\Python38\\lib\\urllib\\parse.py',
   'PYMODULE'),
  ('urllib',
   'C:\\Program Files\\Python38\\lib\\urllib\\__init__.py',
   'PYMODULE'),
  ('fnmatch', 'C:\\Program Files\\Python38\\lib\\fnmatch.py', 'PYMODULE'),
  ('email', 'C:\\Program Files\\Python38\\lib\\email\\__init__.py', 'PYMODULE'),
  ('email.parser',
   'C:\\Program Files\\Python38\\lib\\email\\parser.py',
   'PYMODULE'),
  ('email._policybase',
   'C:\\Program Files\\Python38\\lib\\email\\_policybase.py',
   'PYMODULE'),
  ('email.utils',
   'C:\\Program Files\\Python38\\lib\\email\\utils.py',
   'PYMODULE'),
  ('email._parseaddr',
   'C:\\Program Files\\Python38\\lib\\email\\_parseaddr.py',
   'PYMODULE'),
  ('calendar', 'C:\\Program Files\\Python38\\lib\\calendar.py', 'PYMODULE'),
  ('socket', 'C:\\Program Files\\Python38\\lib\\socket.py', 'PYMODULE'),
  ('selectors', 'C:\\Program Files\\Python38\\lib\\selectors.py', 'PYMODULE'),
  ('random', 'C:\\Program Files\\Python38\\lib\\random.py', 'PYMODULE'),
  ('hashlib', 'C:\\Program Files\\Python38\\lib\\hashlib.py', 'PYMODULE'),
  ('logging',
   'C:\\Program Files\\Python38\\lib\\logging\\__init__.py',
   'PYMODULE'),
  ('pickle', 'C:\\Program Files\\Python38\\lib\\pickle.py', 'PYMODULE'),
  ('pprint', 'C:\\Program Files\\Python38\\lib\\pprint.py', 'PYMODULE'),
  ('_compat_pickle',
   'C:\\Program Files\\Python38\\lib\\_compat_pickle.py',
   'PYMODULE'),
  ('string', 'C:\\Program Files\\Python38\\lib\\string.py', 'PYMODULE'),
  ('bisect', 'C:\\Program Files\\Python38\\lib\\bisect.py', 'PYMODULE'),
  ('email.feedparser',
   'C:\\Program Files\\Python38\\lib\\email\\feedparser.py',
   'PYMODULE'),
  ('email.message',
   'C:\\Program Files\\Python38\\lib\\email\\message.py',
   'PYMODULE'),
  ('email.policy',
   'C:\\Program Files\\Python38\\lib\\email\\policy.py',
   'PYMODULE'),
  ('email.contentmanager',
   'C:\\Program Files\\Python38\\lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.quoprimime',
   'C:\\Program Files\\Python38\\lib\\email\\quoprimime.py',
   'PYMODULE'),
  ('email.headerregistry',
   'C:\\Program Files\\Python38\\lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email.iterators',
   'C:\\Program Files\\Python38\\lib\\email\\iterators.py',
   'PYMODULE'),
  ('email.generator',
   'C:\\Program Files\\Python38\\lib\\email\\generator.py',
   'PYMODULE'),
  ('email._encoded_words',
   'C:\\Program Files\\Python38\\lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('base64', 'C:\\Program Files\\Python38\\lib\\base64.py', 'PYMODULE'),
  ('getopt', 'C:\\Program Files\\Python38\\lib\\getopt.py', 'PYMODULE'),
  ('quopri', 'C:\\Program Files\\Python38\\lib\\quopri.py', 'PYMODULE'),
  ('uu', 'C:\\Program Files\\Python38\\lib\\uu.py', 'PYMODULE'),
  ('optparse', 'C:\\Program Files\\Python38\\lib\\optparse.py', 'PYMODULE'),
  ('email._header_value_parser',
   'C:\\Program Files\\Python38\\lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('email.header',
   'C:\\Program Files\\Python38\\lib\\email\\header.py',
   'PYMODULE'),
  ('email.base64mime',
   'C:\\Program Files\\Python38\\lib\\email\\base64mime.py',
   'PYMODULE'),
  ('email.charset',
   'C:\\Program Files\\Python38\\lib\\email\\charset.py',
   'PYMODULE'),
  ('email.encoders',
   'C:\\Program Files\\Python38\\lib\\email\\encoders.py',
   'PYMODULE'),
  ('email.errors',
   'C:\\Program Files\\Python38\\lib\\email\\errors.py',
   'PYMODULE'),
  ('tokenize', 'C:\\Program Files\\Python38\\lib\\tokenize.py', 'PYMODULE'),
  ('token', 'C:\\Program Files\\Python38\\lib\\token.py', 'PYMODULE'),
  ('lzma', 'C:\\Program Files\\Python38\\lib\\lzma.py', 'PYMODULE'),
  ('_compression',
   'C:\\Program Files\\Python38\\lib\\_compression.py',
   'PYMODULE'),
  ('bz2', 'C:\\Program Files\\Python38\\lib\\bz2.py', 'PYMODULE'),
  ('contextlib', 'C:\\Program Files\\Python38\\lib\\contextlib.py', 'PYMODULE'),
  ('threading', 'C:\\Program Files\\Python38\\lib\\threading.py', 'PYMODULE'),
  ('_threading_local',
   'C:\\Program Files\\Python38\\lib\\_threading_local.py',
   'PYMODULE'),
  ('struct', 'C:\\Program Files\\Python38\\lib\\struct.py', 'PYMODULE'),
  ('shutil', 'C:\\Program Files\\Python38\\lib\\shutil.py', 'PYMODULE'),
  ('tarfile', 'C:\\Program Files\\Python38\\lib\\tarfile.py', 'PYMODULE'),
  ('gzip', 'C:\\Program Files\\Python38\\lib\\gzip.py', 'PYMODULE'),
  ('importlib.util',
   'C:\\Program Files\\Python38\\lib\\importlib\\util.py',
   'PYMODULE'),
  ('inspect', 'C:\\Program Files\\Python38\\lib\\inspect.py', 'PYMODULE'),
  ('ast', 'C:\\Program Files\\Python38\\lib\\ast.py', 'PYMODULE'),
  ('dis', 'C:\\Program Files\\Python38\\lib\\dis.py', 'PYMODULE'),
  ('opcode', 'C:\\Program Files\\Python38\\lib\\opcode.py', 'PYMODULE'),
  ('pkgutil', 'C:\\Program Files\\Python38\\lib\\pkgutil.py', 'PYMODULE'),
  ('zipimport', 'C:\\Program Files\\Python38\\lib\\zipimport.py', 'PYMODULE'),
  ('_pyi_rth_utils.qt',
   'D:\\wenjian\\BS-Link\\.venv\\lib\\site-packages\\PyInstaller\\fake-modules\\_pyi_rth_utils\\qt.py',
   'PYMODULE'),
  ('_pyi_rth_utils',
   'D:\\wenjian\\BS-Link\\.venv\\lib\\site-packages\\PyInstaller\\fake-modules\\_pyi_rth_utils\\__init__.py',
   'PYMODULE'),
  ('tracemalloc',
   'C:\\Program Files\\Python38\\lib\\tracemalloc.py',
   'PYMODULE'),
  ('_py_abc', 'C:\\Program Files\\Python38\\lib\\_py_abc.py', 'PYMODULE'),
  ('typing', 'C:\\Program Files\\Python38\\lib\\typing.py', 'PYMODULE'),
  ('stringprep', 'C:\\Program Files\\Python38\\lib\\stringprep.py', 'PYMODULE'),
  ('csv', 'C:\\Program Files\\Python38\\lib\\csv.py', 'PYMODULE'),
  ('datetime', 'C:\\Program Files\\Python38\\lib\\datetime.py', 'PYMODULE'),
  ('_strptime', 'C:\\Program Files\\Python38\\lib\\_strptime.py', 'PYMODULE'),
  ('subprocess', 'C:\\Program Files\\Python38\\lib\\subprocess.py', 'PYMODULE'),
  ('signal', 'C:\\Program Files\\Python38\\lib\\signal.py', 'PYMODULE'),
  ('BSLink_ui', 'D:\\wenjian\\BS-Link\\BSLink_ui.py', 'PYMODULE'),
  ('PyQt5',
   'D:\\wenjian\\BS-Link\\.venv\\lib\\site-packages\\PyQt5\\__init__.py',
   'PYMODULE'),
  ('serial.tools.list_ports',
   'D:\\wenjian\\BS-Link\\.venv\\lib\\site-packages\\serial\\tools\\list_ports.py',
   'PYMODULE'),
  ('serial.tools',
   'D:\\wenjian\\BS-Link\\.venv\\lib\\site-packages\\serial\\tools\\__init__.py',
   'PYMODULE'),
  ('serial.tools.list_ports_common',
   'D:\\wenjian\\BS-Link\\.venv\\lib\\site-packages\\serial\\tools\\list_ports_common.py',
   'PYMODULE'),
  ('glob', 'C:\\Program Files\\Python38\\lib\\glob.py', 'PYMODULE'),
  ('serial.tools.list_ports_posix',
   'D:\\wenjian\\BS-Link\\.venv\\lib\\site-packages\\serial\\tools\\list_ports_posix.py',
   'PYMODULE'),
  ('serial.tools.list_ports_osx',
   'D:\\wenjian\\BS-Link\\.venv\\lib\\site-packages\\serial\\tools\\list_ports_osx.py',
   'PYMODULE'),
  ('ctypes',
   'C:\\Program Files\\Python38\\lib\\ctypes\\__init__.py',
   'PYMODULE'),
  ('ctypes._endian',
   'C:\\Program Files\\Python38\\lib\\ctypes\\_endian.py',
   'PYMODULE'),
  ('serial.tools.list_ports_linux',
   'D:\\wenjian\\BS-Link\\.venv\\lib\\site-packages\\serial\\tools\\list_ports_linux.py',
   'PYMODULE'),
  ('serial.tools.list_ports_windows',
   'D:\\wenjian\\BS-Link\\.venv\\lib\\site-packages\\serial\\tools\\list_ports_windows.py',
   'PYMODULE'),
  ('serial.win32',
   'D:\\wenjian\\BS-Link\\.venv\\lib\\site-packages\\serial\\win32.py',
   'PYMODULE'),
  ('ctypes.wintypes',
   'C:\\Program Files\\Python38\\lib\\ctypes\\wintypes.py',
   'PYMODULE'),
  ('__future__', 'C:\\Program Files\\Python38\\lib\\__future__.py', 'PYMODULE'),
  ('serial',
   'D:\\wenjian\\BS-Link\\.venv\\lib\\site-packages\\serial\\__init__.py',
   'PYMODULE'),
  ('serial.serialjava',
   'D:\\wenjian\\BS-Link\\.venv\\lib\\site-packages\\serial\\serialjava.py',
   'PYMODULE'),
  ('serial.serialposix',
   'D:\\wenjian\\BS-Link\\.venv\\lib\\site-packages\\serial\\serialposix.py',
   'PYMODULE'),
  ('serial.serialwin32',
   'D:\\wenjian\\BS-Link\\.venv\\lib\\site-packages\\serial\\serialwin32.py',
   'PYMODULE'),
  ('serial.serialcli',
   'D:\\wenjian\\BS-Link\\.venv\\lib\\site-packages\\serial\\serialcli.py',
   'PYMODULE'),
  ('serial.serialutil',
   'D:\\wenjian\\BS-Link\\.venv\\lib\\site-packages\\serial\\serialutil.py',
   'PYMODULE')],
 [('python38.dll', 'C:\\Program Files\\Python38\\python38.dll', 'BINARY'),
  ('PyQt5\\Qt5\\plugins\\platforms\\qoffscreen.dll',
   'D:\\wenjian\\BS-Link\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\platforms\\qoffscreen.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qwbmp.dll',
   'D:\\wenjian\\BS-Link\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qwbmp.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qtga.dll',
   'D:\\wenjian\\BS-Link\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qtga.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\iconengines\\qsvgicon.dll',
   'D:\\wenjian\\BS-Link\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\iconengines\\qsvgicon.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\generic\\qtuiotouchplugin.dll',
   'D:\\wenjian\\BS-Link\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\generic\\qtuiotouchplugin.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qicns.dll',
   'D:\\wenjian\\BS-Link\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qicns.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\platforms\\qminimal.dll',
   'D:\\wenjian\\BS-Link\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\platforms\\qminimal.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qjpeg.dll',
   'D:\\wenjian\\BS-Link\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qjpeg.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qsvg.dll',
   'D:\\wenjian\\BS-Link\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qsvg.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qtiff.dll',
   'D:\\wenjian\\BS-Link\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qtiff.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\platforms\\qwebgl.dll',
   'D:\\wenjian\\BS-Link\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\platforms\\qwebgl.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\platformthemes\\qxdgdesktopportal.dll',
   'D:\\wenjian\\BS-Link\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\platformthemes\\qxdgdesktopportal.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qwebp.dll',
   'D:\\wenjian\\BS-Link\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qwebp.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qico.dll',
   'D:\\wenjian\\BS-Link\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qico.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\platforms\\qwindows.dll',
   'D:\\wenjian\\BS-Link\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\platforms\\qwindows.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qgif.dll',
   'D:\\wenjian\\BS-Link\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qgif.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\styles\\qwindowsvistastyle.dll',
   'D:\\wenjian\\BS-Link\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\plugins\\styles\\qwindowsvistastyle.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\libEGL.dll',
   'D:\\wenjian\\BS-Link\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\libEGL.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\d3dcompiler_47.dll',
   'D:\\wenjian\\BS-Link\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\d3dcompiler_47.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\opengl32sw.dll',
   'D:\\wenjian\\BS-Link\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\opengl32sw.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\libGLESv2.dll',
   'D:\\wenjian\\BS-Link\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\libGLESv2.dll',
   'BINARY'),
  ('unicodedata.pyd',
   'C:\\Program Files\\Python38\\DLLs\\unicodedata.pyd',
   'EXTENSION'),
  ('select.pyd', 'C:\\Program Files\\Python38\\DLLs\\select.pyd', 'EXTENSION'),
  ('_socket.pyd',
   'C:\\Program Files\\Python38\\DLLs\\_socket.pyd',
   'EXTENSION'),
  ('_hashlib.pyd',
   'C:\\Program Files\\Python38\\DLLs\\_hashlib.pyd',
   'EXTENSION'),
  ('_lzma.pyd', 'C:\\Program Files\\Python38\\DLLs\\_lzma.pyd', 'EXTENSION'),
  ('_bz2.pyd', 'C:\\Program Files\\Python38\\DLLs\\_bz2.pyd', 'EXTENSION'),
  ('PyQt5\\QtGui.pyd',
   'D:\\wenjian\\BS-Link\\.venv\\lib\\site-packages\\PyQt5\\QtGui.pyd',
   'EXTENSION'),
  ('PyQt5\\QtCore.pyd',
   'D:\\wenjian\\BS-Link\\.venv\\lib\\site-packages\\PyQt5\\QtCore.pyd',
   'EXTENSION'),
  ('PyQt5\\QtWidgets.pyd',
   'D:\\wenjian\\BS-Link\\.venv\\lib\\site-packages\\PyQt5\\QtWidgets.pyd',
   'EXTENSION'),
  ('PyQt5\\sip.cp38-win_amd64.pyd',
   'D:\\wenjian\\BS-Link\\.venv\\lib\\site-packages\\PyQt5\\sip.cp38-win_amd64.pyd',
   'EXTENSION'),
  ('_ctypes.pyd',
   'C:\\Program Files\\Python38\\DLLs\\_ctypes.pyd',
   'EXTENSION'),
  ('api-ms-win-crt-convert-l1-1-0.dll',
   'D:\\anda\\api-ms-win-crt-convert-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-string-l1-1-0.dll',
   'D:\\anda\\api-ms-win-crt-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-heap-l1-1-0.dll',
   'D:\\anda\\api-ms-win-crt-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-math-l1-1-0.dll',
   'D:\\anda\\api-ms-win-crt-math-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-environment-l1-1-0.dll',
   'D:\\anda\\api-ms-win-crt-environment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-process-l1-1-0.dll',
   'D:\\anda\\api-ms-win-crt-process-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-time-l1-1-0.dll',
   'D:\\anda\\api-ms-win-crt-time-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-stdio-l1-1-0.dll',
   'D:\\anda\\api-ms-win-crt-stdio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-filesystem-l1-1-0.dll',
   'D:\\anda\\api-ms-win-crt-filesystem-l1-1-0.dll',
   'BINARY'),
  ('VCRUNTIME140.dll',
   'C:\\Program Files\\Python38\\VCRUNTIME140.dll',
   'BINARY'),
  ('api-ms-win-crt-conio-l1-1-0.dll',
   'D:\\anda\\api-ms-win-crt-conio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-locale-l1-1-0.dll',
   'D:\\anda\\api-ms-win-crt-locale-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-runtime-l1-1-0.dll',
   'D:\\anda\\api-ms-win-crt-runtime-l1-1-0.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Gui.dll',
   'D:\\wenjian\\BS-Link\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Gui.dll',
   'BINARY'),
  ('api-ms-win-crt-utility-l1-1-0.dll',
   'D:\\anda\\api-ms-win-crt-utility-l1-1-0.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Core.dll',
   'D:\\wenjian\\BS-Link\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Core.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Svg.dll',
   'D:\\wenjian\\BS-Link\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Svg.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Network.dll',
   'D:\\wenjian\\BS-Link\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Network.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5WebSockets.dll',
   'D:\\wenjian\\BS-Link\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5WebSockets.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Quick.dll',
   'D:\\wenjian\\BS-Link\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Quick.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\MSVCP140.dll',
   'D:\\wenjian\\BS-Link\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\bin\\MSVCP140.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5DBus.dll',
   'D:\\wenjian\\BS-Link\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5DBus.dll',
   'BINARY'),
  ('VCRUNTIME140_1.dll',
   'C:\\Program Files\\Python38\\VCRUNTIME140_1.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Widgets.dll',
   'D:\\wenjian\\BS-Link\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Widgets.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\VCRUNTIME140.dll',
   'D:\\wenjian\\BS-Link\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\VCRUNTIME140.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\VCRUNTIME140_1.dll',
   'D:\\wenjian\\BS-Link\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\VCRUNTIME140_1.dll',
   'BINARY'),
  ('libcrypto-1_1.dll',
   'C:\\Program Files\\Python38\\DLLs\\libcrypto-1_1.dll',
   'BINARY'),
  ('python3.dll', 'C:\\Program Files\\Python38\\python3.dll', 'BINARY'),
  ('libffi-7.dll', 'C:\\Program Files\\Python38\\DLLs\\libffi-7.dll', 'BINARY'),
  ('ucrtbase.dll', 'D:\\anda\\ucrtbase.dll', 'BINARY'),
  ('PyQt5\\Qt5\\bin\\MSVCP140_1.dll',
   'D:\\wenjian\\BS-Link\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\bin\\MSVCP140_1.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5QmlModels.dll',
   'D:\\wenjian\\BS-Link\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5QmlModels.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Qml.dll',
   'D:\\wenjian\\BS-Link\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Qml.dll',
   'BINARY'),
  ('api-ms-win-core-rtlsupport-l1-1-0.dll',
   'D:\\anda\\api-ms-win-core-rtlsupport-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l2-1-0.dll',
   'D:\\anda\\api-ms-win-core-file-l2-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-datetime-l1-1-0.dll',
   'D:\\anda\\api-ms-win-core-datetime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-0.dll',
   'D:\\anda\\api-ms-win-core-processthreads-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-interlocked-l1-1-0.dll',
   'D:\\anda\\api-ms-win-core-interlocked-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-kernel32-legacy-l1-1-1.dll',
   'D:\\anda\\api-ms-win-core-kernel32-legacy-l1-1-1.dll',
   'BINARY'),
  ('api-ms-win-core-debug-l1-1-0.dll',
   'D:\\anda\\api-ms-win-core-debug-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-fibers-l1-1-1.dll',
   'D:\\anda\\api-ms-win-core-fibers-l1-1-1.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-1-0.dll',
   'D:\\anda\\api-ms-win-core-synch-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-fibers-l1-1-0.dll',
   'D:\\anda\\api-ms-win-core-fibers-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-sysinfo-l1-1-0.dll',
   'D:\\anda\\api-ms-win-core-sysinfo-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-util-l1-1-0.dll',
   'D:\\anda\\api-ms-win-core-util-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-namedpipe-l1-1-0.dll',
   'D:\\anda\\api-ms-win-core-namedpipe-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-timezone-l1-1-0.dll',
   'D:\\anda\\api-ms-win-core-timezone-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-sysinfo-l1-2-0.dll',
   'D:\\anda\\api-ms-win-core-sysinfo-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-errorhandling-l1-1-0.dll',
   'D:\\anda\\api-ms-win-core-errorhandling-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-memory-l1-1-0.dll',
   'D:\\anda\\api-ms-win-core-memory-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-1.dll',
   'D:\\anda\\api-ms-win-core-processthreads-l1-1-1.dll',
   'BINARY'),
  ('api-ms-win-core-localization-l1-2-0.dll',
   'D:\\anda\\api-ms-win-core-localization-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-1-0.dll',
   'D:\\anda\\api-ms-win-core-file-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processenvironment-l1-1-0.dll',
   'D:\\anda\\api-ms-win-core-processenvironment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-handle-l1-1-0.dll',
   'D:\\anda\\api-ms-win-core-handle-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-console-l1-1-0.dll',
   'D:\\anda\\api-ms-win-core-console-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-2-0.dll',
   'D:\\anda\\api-ms-win-core-synch-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-profile-l1-1-0.dll',
   'D:\\anda\\api-ms-win-core-profile-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-libraryloader-l1-1-0.dll',
   'D:\\anda\\api-ms-win-core-libraryloader-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-string-l1-1-0.dll',
   'D:\\anda\\api-ms-win-core-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-2-0.dll',
   'D:\\anda\\api-ms-win-core-file-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-heap-l1-1-0.dll',
   'D:\\anda\\api-ms-win-core-heap-l1-1-0.dll',
   'BINARY')],
 [],
 [],
 [('PyQt5\\Qt5\\translations\\qt_es.qm',
   'D:\\wenjian\\BS-Link\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_es.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_ja.qm',
   'D:\\wenjian\\BS-Link\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_ja.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_cs.qm',
   'D:\\wenjian\\BS-Link\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_cs.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_ko.qm',
   'D:\\wenjian\\BS-Link\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_ko.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_tr.qm',
   'D:\\wenjian\\BS-Link\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_tr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_ja.qm',
   'D:\\wenjian\\BS-Link\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_ja.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_lv.qm',
   'D:\\wenjian\\BS-Link\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_lv.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_en.qm',
   'D:\\wenjian\\BS-Link\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_en.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_zh_CN.qm',
   'D:\\wenjian\\BS-Link\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_zh_CN.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_ja.qm',
   'D:\\wenjian\\BS-Link\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_ja.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_zh_TW.qm',
   'D:\\wenjian\\BS-Link\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_zh_TW.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_ru.qm',
   'D:\\wenjian\\BS-Link\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_ru.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_hu.qm',
   'D:\\wenjian\\BS-Link\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_hu.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_fa.qm',
   'D:\\wenjian\\BS-Link\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_fa.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_sk.qm',
   'D:\\wenjian\\BS-Link\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_sk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_uk.qm',
   'D:\\wenjian\\BS-Link\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_uk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_fi.qm',
   'D:\\wenjian\\BS-Link\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_fi.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_da.qm',
   'D:\\wenjian\\BS-Link\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_da.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_ru.qm',
   'D:\\wenjian\\BS-Link\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_ru.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_zh_TW.qm',
   'D:\\wenjian\\BS-Link\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_zh_TW.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_pl.qm',
   'D:\\wenjian\\BS-Link\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_pl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_de.qm',
   'D:\\wenjian\\BS-Link\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_de.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_en.qm',
   'D:\\wenjian\\BS-Link\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_en.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_bg.qm',
   'D:\\wenjian\\BS-Link\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_bg.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_cs.qm',
   'D:\\wenjian\\BS-Link\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_cs.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_en.qm',
   'D:\\wenjian\\BS-Link\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_en.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_ca.qm',
   'D:\\wenjian\\BS-Link\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_ca.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_fr.qm',
   'D:\\wenjian\\BS-Link\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_fr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_de.qm',
   'D:\\wenjian\\BS-Link\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_de.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_zh_TW.qm',
   'D:\\wenjian\\BS-Link\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_zh_TW.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_fr.qm',
   'D:\\wenjian\\BS-Link\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_fr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_lt.qm',
   'D:\\wenjian\\BS-Link\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_lt.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_sl.qm',
   'D:\\wenjian\\BS-Link\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_sl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_gl.qm',
   'D:\\wenjian\\BS-Link\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_gl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_zh_CN.qm',
   'D:\\wenjian\\BS-Link\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_zh_CN.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_bg.qm',
   'D:\\wenjian\\BS-Link\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_bg.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_da.qm',
   'D:\\wenjian\\BS-Link\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_da.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_ca.qm',
   'D:\\wenjian\\BS-Link\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_ca.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_sk.qm',
   'D:\\wenjian\\BS-Link\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_sk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_gd.qm',
   'D:\\wenjian\\BS-Link\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_gd.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_gl.qm',
   'D:\\wenjian\\BS-Link\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_gl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_ar.qm',
   'D:\\wenjian\\BS-Link\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_ar.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_cs.qm',
   'D:\\wenjian\\BS-Link\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_cs.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_es.qm',
   'D:\\wenjian\\BS-Link\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_es.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_ru.qm',
   'D:\\wenjian\\BS-Link\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_ru.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_hu.qm',
   'D:\\wenjian\\BS-Link\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_hu.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_he.qm',
   'D:\\wenjian\\BS-Link\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_he.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_bg.qm',
   'D:\\wenjian\\BS-Link\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_bg.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_es.qm',
   'D:\\wenjian\\BS-Link\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_es.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_gd.qm',
   'D:\\wenjian\\BS-Link\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_gd.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_fr.qm',
   'D:\\wenjian\\BS-Link\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_fr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_pl.qm',
   'D:\\wenjian\\BS-Link\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_pl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_fi.qm',
   'D:\\wenjian\\BS-Link\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_fi.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_da.qm',
   'D:\\wenjian\\BS-Link\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_da.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_lv.qm',
   'D:\\wenjian\\BS-Link\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_lv.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_de.qm',
   'D:\\wenjian\\BS-Link\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_de.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_hu.qm',
   'D:\\wenjian\\BS-Link\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_hu.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_tr.qm',
   'D:\\wenjian\\BS-Link\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_tr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_ar.qm',
   'D:\\wenjian\\BS-Link\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_ar.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_ar.qm',
   'D:\\wenjian\\BS-Link\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_ar.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_uk.qm',
   'D:\\wenjian\\BS-Link\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_uk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_sl.qm',
   'D:\\wenjian\\BS-Link\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_sl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_pl.qm',
   'D:\\wenjian\\BS-Link\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_pl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_sk.qm',
   'D:\\wenjian\\BS-Link\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_sk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_tr.qm',
   'D:\\wenjian\\BS-Link\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_tr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_he.qm',
   'D:\\wenjian\\BS-Link\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_he.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_pt.qm',
   'D:\\wenjian\\BS-Link\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_pt.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_it.qm',
   'D:\\wenjian\\BS-Link\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_it.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_it.qm',
   'D:\\wenjian\\BS-Link\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_it.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_ca.qm',
   'D:\\wenjian\\BS-Link\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_ca.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_uk.qm',
   'D:\\wenjian\\BS-Link\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_uk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_it.qm',
   'D:\\wenjian\\BS-Link\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_it.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_ko.qm',
   'D:\\wenjian\\BS-Link\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_ko.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_ko.qm',
   'D:\\wenjian\\BS-Link\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_ko.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_sv.qm',
   'D:\\wenjian\\BS-Link\\.venv\\lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_sv.qm',
   'DATA'),
  ('base_library.zip',
   'D:\\wenjian\\BS-Link\\build\\BSLink串口调试助手\\base_library.zip',
   'DATA')])
