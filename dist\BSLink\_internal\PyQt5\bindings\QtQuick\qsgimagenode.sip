// qsgimagenode.sip generated by MetaSIP
//
// This file is part of the QtQuick Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%If (Qt_5_8_0 -)

class QSGImageNode : public QSGGeometryNode /NoDefaultCtors/
{
%TypeHeaderCode
#include <qsgimagenode.h>
%End

public:
    virtual ~QSGImageNode();
    virtual void setRect(const QRectF &rect) = 0;
    void setRect(qreal x, qreal y, qreal w, qreal h);
    virtual QRectF rect() const = 0;
    virtual void setSourceRect(const QRectF &r) = 0;
    void setSourceRect(qreal x, qreal y, qreal w, qreal h);
    virtual QRectF sourceRect() const = 0;
    virtual void setTexture(QSGTexture *texture /GetWrapper/) = 0;
%MethodCode
        sipCpp->setTexture(a0);
        
        if (sipCpp->ownsTexture())
            sipTransferTo(a0Wrapper, sipSelf);
%End

    virtual QSGTexture *texture() const = 0;
    virtual void setFiltering(QSGTexture::Filtering filtering) = 0;
    virtual QSGTexture::Filtering filtering() const = 0;
    virtual void setMipmapFiltering(QSGTexture::Filtering filtering) = 0;
    virtual QSGTexture::Filtering mipmapFiltering() const = 0;

    enum TextureCoordinatesTransformFlag
    {
        NoTransform,
        MirrorHorizontally,
        MirrorVertically,
    };

    typedef QFlags<QSGImageNode::TextureCoordinatesTransformFlag> TextureCoordinatesTransformMode;
    virtual void setTextureCoordinatesTransform(QSGImageNode::TextureCoordinatesTransformMode mode) = 0;
    virtual QSGImageNode::TextureCoordinatesTransformMode textureCoordinatesTransform() const = 0;
    virtual void setOwnsTexture(bool owns) = 0;
    virtual bool ownsTexture() const = 0;
    static void rebuildGeometry(QSGGeometry *g, QSGTexture *texture, const QRectF &rect, QRectF sourceRect, QSGImageNode::TextureCoordinatesTransformMode texCoordMode);
};

%End
%If (Qt_5_8_0 -)
QFlags<QSGImageNode::TextureCoordinatesTransformFlag> operator|(QSGImageNode::TextureCoordinatesTransformFlag f1, QFlags<QSGImageNode::TextureCoordinatesTransformFlag> f2);
%End
