// qxmlformatter.sip generated by MetaSIP
//
// This file is part of the QtXmlPatterns Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QXmlFormatter : public QXmlSerializer
{
%TypeHeaderCode
#include <qxmlformatter.h>
%End

public:
    QXmlFormatter(const QXmlQuery &query, QIODevice *outputDevice);
    virtual void characters(const QStringRef &value);
    virtual void comment(const QString &value);
    virtual void startElement(const QXmlName &name);
    virtual void endElement();
    virtual void attribute(const QXmlName &name, const QStringRef &value);
    virtual void processingInstruction(const QXmlName &name, const QString &value);
    virtual void atomicValue(const QVariant &value);
    virtual void startDocument();
    virtual void endDocument();
    virtual void startOfSequence();
    virtual void endOfSequence();
    int indentationDepth() const;
    void setIndentationDepth(int depth);
};
