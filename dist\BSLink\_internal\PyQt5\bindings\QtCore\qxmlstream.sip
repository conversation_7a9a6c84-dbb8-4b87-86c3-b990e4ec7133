// qxmlstream.sip generated by MetaSIP
//
// This file is part of the QtCore Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QXmlStreamAttribute
{
%TypeHeaderCode
#include <qxmlstream.h>
%End

public:
    QXmlStreamAttribute();
    QXmlStreamAttribute(const QString &qualifiedName, const QString &value);
    QXmlStreamAttribute(const QString &namespaceUri, const QString &name, const QString &value);
    QXmlStreamAttribute(const QXmlStreamAttribute &);
    ~QXmlStreamAttribute();
    QStringRef namespaceUri() const;
    QStringRef name() const;
    QStringRef qualifiedName() const;
    QStringRef prefix() const;
    QStringRef value() const;
    bool isDefault() const;
    bool operator==(const QXmlStreamAttribute &other) const;
    bool operator!=(const QXmlStreamAttribute &other) const;
};

class QXmlStreamAttributes
{
%TypeHeaderCode
#include <qxmlstream.h>
%End

public:
    QXmlStreamAttributes();
    QStringRef value(const QString &namespaceUri, const QString &name) const;
    QStringRef value(const QString &qualifiedName) const;
    void append(const QString &namespaceUri, const QString &name, const QString &value);
    void append(const QString &qualifiedName, const QString &value);
    void append(const QXmlStreamAttribute &attribute);
    bool hasAttribute(const QString &qualifiedName) const;
    bool hasAttribute(const QString &namespaceUri, const QString &name) const;
// Methods inherited from QVector<QXmlStreamAttribute> and Python special methods.
// Keep in sync with QPolygon and QPolygonF.

// This is picked up with "using" by QXmlStreamAttributes.
//void append(const QXmlStreamAttribute &value);

const QXmlStreamAttribute &at(int i) const;
void clear();
bool contains(const QXmlStreamAttribute &value) const;
int count(const QXmlStreamAttribute &value) const;
int count() const /__len__/;
void *data();

// Note the Qt return value is discarded as it would require handwritten code
// and seems pretty useless.
void fill(const QXmlStreamAttribute &value, int size = -1);

QXmlStreamAttribute &first();
int indexOf(const QXmlStreamAttribute &value, int from = 0) const;
void insert(int i, const QXmlStreamAttribute &value);
bool isEmpty() const;
QXmlStreamAttribute &last();
int lastIndexOf(const QXmlStreamAttribute &value, int from = -1) const;

// Note the Qt return type is QVector<QXmlStreamAttribute>.  We can't do the
// usual trick because there is no QXmlStreamAttributes ctor that takes a
// QVector<QXmlStreamAttribute> argument.  We could use handwritten code but we
// don't bother.
//QXmlStreamAttributes mid(int pos, int length = -1) const;

void prepend(const QXmlStreamAttribute &value);
void remove(int i);
void remove(int i, int count);
void replace(int i, const QXmlStreamAttribute &value);
int size() const;

// These are hidden by other implementations in QXmlStreamAttributes.
//QXmlStreamAttribute value(int i) const;
//QXmlStreamAttribute value(int i, const QXmlStreamAttribute &defaultValue) const;

bool operator!=(const QXmlStreamAttributes &other) const;

// Note the Qt return type is QVector<QXmlStreamAttribute>.  We can't do the
// usual trick because there is no QXmlStreamAttributes ctor that takes a
// QVector<QXmlStreamAttribute> argument.  We could use handwritten code but we
// don't bother.
//QXmlStreamAttributes operator+(const QXmlStreamAttributes &other) const;

QXmlStreamAttributes &operator+=(const QXmlStreamAttributes &other);
QXmlStreamAttributes &operator+=(const QXmlStreamAttribute &value);

bool operator==(const QXmlStreamAttributes &other) const;

QXmlStreamAttribute &operator[](int i);
%MethodCode
Py_ssize_t idx = sipConvertFromSequenceIndex(a0, sipCpp->count());

if (idx < 0)
    sipIsErr = 1;
else
    sipRes = &sipCpp->operator[]((int)idx);
%End

// Some additional Python special methods.

void __setitem__(int i, const QXmlStreamAttribute &value);
%MethodCode
int len;

len = sipCpp->count();

if ((a0 = (int)sipConvertFromSequenceIndex(a0, len)) < 0)
    sipIsErr = 1;
else
    (*sipCpp)[a0] = *a1;
%End

void __setitem__(SIP_PYSLICE slice, const QXmlStreamAttributes &list);
%MethodCode
Py_ssize_t start, stop, step, slicelength;

if (sipConvertFromSliceObject(a0, sipCpp->count(), &start, &stop, &step, &slicelength) < 0)
{
    sipIsErr = 1;
}
else
{
    int vlen = a1->count();

    if (vlen != slicelength)
    {
        sipBadLengthForSlice(vlen, slicelength);
        sipIsErr = 1;
    }
    else
    {
        QVector<QXmlStreamAttribute>::const_iterator it = a1->begin();

        for (Py_ssize_t i = 0; i < slicelength; ++i)
        {
            (*sipCpp)[start] = *it;
            start += step;
            ++it;
        }
    }
}
%End

void __delitem__(int i);
%MethodCode
if ((a0 = (int)sipConvertFromSequenceIndex(a0, sipCpp->count())) < 0)
    sipIsErr = 1;
else
    sipCpp->remove(a0);
%End

void __delitem__(SIP_PYSLICE slice);
%MethodCode
Py_ssize_t start, stop, step, slicelength;

if (sipConvertFromSliceObject(a0, sipCpp->count(), &start, &stop, &step, &slicelength) < 0)
{
    sipIsErr = 1;
}
else
{
    for (Py_ssize_t i = 0; i < slicelength; ++i)
    {
        sipCpp->remove(start);
        start += step - 1;
    }
}
%End

QXmlStreamAttributes operator[](SIP_PYSLICE slice);
%MethodCode
Py_ssize_t start, stop, step, slicelength;

if (sipConvertFromSliceObject(a0, sipCpp->count(), &start, &stop, &step, &slicelength) < 0)
{
    sipIsErr = 1;
}
else
{
    sipRes = new QXmlStreamAttributes();

    for (Py_ssize_t i = 0; i < slicelength; ++i)
    {
        (*sipRes) += (*sipCpp)[start];
        start += step;
    }
}
%End

int __contains__(const QXmlStreamAttribute &value);
%MethodCode
// It looks like you can't assign QBool to int.
sipRes = bool(sipCpp->contains(*a0));
%End
};

class QXmlStreamNamespaceDeclaration
{
%TypeHeaderCode
#include <qxmlstream.h>
%End

public:
    QXmlStreamNamespaceDeclaration();
    QXmlStreamNamespaceDeclaration(const QXmlStreamNamespaceDeclaration &);
    QXmlStreamNamespaceDeclaration(const QString &prefix, const QString &namespaceUri);
    ~QXmlStreamNamespaceDeclaration();
    QStringRef prefix() const;
    QStringRef namespaceUri() const;
    bool operator==(const QXmlStreamNamespaceDeclaration &other) const;
    bool operator!=(const QXmlStreamNamespaceDeclaration &other) const;
};

typedef QVector<QXmlStreamNamespaceDeclaration> QXmlStreamNamespaceDeclarations;

class QXmlStreamNotationDeclaration
{
%TypeHeaderCode
#include <qxmlstream.h>
%End

public:
    QXmlStreamNotationDeclaration();
    QXmlStreamNotationDeclaration(const QXmlStreamNotationDeclaration &);
    ~QXmlStreamNotationDeclaration();
    QStringRef name() const;
    QStringRef systemId() const;
    QStringRef publicId() const;
    bool operator==(const QXmlStreamNotationDeclaration &other) const;
    bool operator!=(const QXmlStreamNotationDeclaration &other) const;
};

typedef QVector<QXmlStreamNotationDeclaration> QXmlStreamNotationDeclarations;

class QXmlStreamEntityDeclaration
{
%TypeHeaderCode
#include <qxmlstream.h>
%End

public:
    QXmlStreamEntityDeclaration();
    QXmlStreamEntityDeclaration(const QXmlStreamEntityDeclaration &);
    ~QXmlStreamEntityDeclaration();
    QStringRef name() const;
    QStringRef notationName() const;
    QStringRef systemId() const;
    QStringRef publicId() const;
    QStringRef value() const;
    bool operator==(const QXmlStreamEntityDeclaration &other) const;
    bool operator!=(const QXmlStreamEntityDeclaration &other) const;
};

typedef QVector<QXmlStreamEntityDeclaration> QXmlStreamEntityDeclarations;

class QXmlStreamEntityResolver
{
%TypeHeaderCode
#include <qxmlstream.h>
%End

public:
    virtual ~QXmlStreamEntityResolver();
    virtual QString resolveUndeclaredEntity(const QString &name);
};

class QXmlStreamReader
{
%TypeHeaderCode
#include <qxmlstream.h>
%End

public:
    enum TokenType
    {
        NoToken,
        Invalid,
        StartDocument,
        EndDocument,
        StartElement,
        EndElement,
        Characters,
        Comment,
        DTD,
        EntityReference,
        ProcessingInstruction,
    };

    QXmlStreamReader();
    explicit QXmlStreamReader(QIODevice *device);
    explicit QXmlStreamReader(const QByteArray &data);
    explicit QXmlStreamReader(const QString &data);
    ~QXmlStreamReader();
    void setDevice(QIODevice *device);
    QIODevice *device() const;
    void addData(const QByteArray &data);
    void addData(const QString &data);
    void clear();
    bool atEnd() const;
    QXmlStreamReader::TokenType readNext();
    QXmlStreamReader::TokenType tokenType() const;
    QString tokenString() const;
    void setNamespaceProcessing(bool);
    bool namespaceProcessing() const;
    bool isStartDocument() const;
    bool isEndDocument() const;
    bool isStartElement() const;
    bool isEndElement() const;
    bool isCharacters() const;
    bool isWhitespace() const;
    bool isCDATA() const;
    bool isComment() const;
    bool isDTD() const;
    bool isEntityReference() const;
    bool isProcessingInstruction() const;
    bool isStandaloneDocument() const;
    QStringRef documentVersion() const;
    QStringRef documentEncoding() const;
    qint64 lineNumber() const;
    qint64 columnNumber() const;
    qint64 characterOffset() const;
    QXmlStreamAttributes attributes() const;

    enum ReadElementTextBehaviour
    {
        ErrorOnUnexpectedElement,
        IncludeChildElements,
        SkipChildElements,
    };

    QString readElementText(QXmlStreamReader::ReadElementTextBehaviour behaviour = QXmlStreamReader::ErrorOnUnexpectedElement);
    QStringRef name() const;
    QStringRef namespaceUri() const;
    QStringRef qualifiedName() const;
    QStringRef prefix() const;
    QStringRef processingInstructionTarget() const;
    QStringRef processingInstructionData() const;
    QStringRef text() const;
    QXmlStreamNamespaceDeclarations namespaceDeclarations() const;
    void addExtraNamespaceDeclaration(const QXmlStreamNamespaceDeclaration &extraNamespaceDeclaraction);
    void addExtraNamespaceDeclarations(const QXmlStreamNamespaceDeclarations &extraNamespaceDeclaractions);
    QXmlStreamNotationDeclarations notationDeclarations() const;
    QXmlStreamEntityDeclarations entityDeclarations() const;
    QStringRef dtdName() const;
    QStringRef dtdPublicId() const;
    QStringRef dtdSystemId() const;

    enum Error
    {
        NoError,
        UnexpectedElementError,
        CustomError,
        NotWellFormedError,
        PrematureEndOfDocumentError,
    };

    void raiseError(const QString &message = QString());
    QString errorString() const;
    QXmlStreamReader::Error error() const;
    bool hasError() const;
    void setEntityResolver(QXmlStreamEntityResolver *resolver /KeepReference/);
    QXmlStreamEntityResolver *entityResolver() const;
    bool readNextStartElement();
    void skipCurrentElement();
%If (Qt_5_15_0 -)
    int entityExpansionLimit() const;
%End
%If (Qt_5_15_0 -)
    void setEntityExpansionLimit(int limit);
%End

private:
    QXmlStreamReader(const QXmlStreamReader &);
};

class QXmlStreamWriter
{
%TypeHeaderCode
#include <qxmlstream.h>
%End

public:
    QXmlStreamWriter();
    explicit QXmlStreamWriter(QIODevice *device);
    explicit QXmlStreamWriter(QByteArray *array);
    ~QXmlStreamWriter();
    void setDevice(QIODevice *device);
    QIODevice *device() const;
    void setCodec(QTextCodec *codec /KeepReference/);
    void setCodec(const char *codecName);
    QTextCodec *codec() const;
    void setAutoFormatting(bool);
    bool autoFormatting() const;
    void setAutoFormattingIndent(int spaces);
    int autoFormattingIndent() const;
    void writeAttribute(const QString &qualifiedName, const QString &value);
    void writeAttribute(const QString &namespaceUri, const QString &name, const QString &value);
    void writeAttribute(const QXmlStreamAttribute &attribute);
    void writeAttributes(const QXmlStreamAttributes &attributes);
    void writeCDATA(const QString &text);
    void writeCharacters(const QString &text);
    void writeComment(const QString &text);
    void writeDTD(const QString &dtd);
    void writeEmptyElement(const QString &qualifiedName);
    void writeEmptyElement(const QString &namespaceUri, const QString &name);
    void writeTextElement(const QString &qualifiedName, const QString &text);
    void writeTextElement(const QString &namespaceUri, const QString &name, const QString &text);
    void writeEndDocument();
    void writeEndElement();
    void writeEntityReference(const QString &name);
    void writeNamespace(const QString &namespaceUri, const QString &prefix = QString());
    void writeDefaultNamespace(const QString &namespaceUri);
    void writeProcessingInstruction(const QString &target, const QString &data = QString());
    void writeStartDocument();
    void writeStartDocument(const QString &version);
    void writeStartDocument(const QString &version, bool standalone);
    void writeStartElement(const QString &qualifiedName);
    void writeStartElement(const QString &namespaceUri, const QString &name);
    void writeCurrentToken(const QXmlStreamReader &reader);
    bool hasError() const;

private:
    QXmlStreamWriter(const QXmlStreamWriter &);
};
