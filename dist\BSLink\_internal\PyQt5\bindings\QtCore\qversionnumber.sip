// qversionnumber.sip generated by MetaSIP
//
// This file is part of the QtCore Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%If (Qt_5_6_0 -)
QDataStream &operator<<(QDataStream &out, const QVersionNumber &version /Constrained/) /ReleaseGIL/;
%End
%If (Qt_5_6_0 -)
QDataStream &operator>>(QDataStream &in, QVersionNumber &version /Constrained/) /ReleaseGIL/;
%End
%If (Qt_5_6_0 -)

class QVersionNumber
{
%TypeHeaderCode
#include <qversionnumber.h>
%End

public:
    QVersionNumber();
    explicit QVersionNumber(const QVector<int> &seg);
    explicit QVersionNumber(int maj);
    QVersionNumber(int maj, int min);
    QVersionNumber(int maj, int min, int mic);
    bool isNull() const;
    bool isNormalized() const;
    int majorVersion() const;
    int minorVersion() const;
    int microVersion() const;
    QVersionNumber normalized() const;
    QVector<int> segments() const;
    int segmentAt(int index) const;
    int segmentCount() const;
    bool isPrefixOf(const QVersionNumber &other) const;
    static int compare(const QVersionNumber &v1, const QVersionNumber &v2);
    static QVersionNumber commonPrefix(const QVersionNumber &v1, const QVersionNumber &v2);
    QString toString() const;
    static QVersionNumber fromString(const QString &string, int *suffixIndex = 0);
    long __hash__() const;
%MethodCode
        sipRes = qHash(*sipCpp);
%End
};

%End
%If (Qt_5_6_0 -)
bool operator>(const QVersionNumber &lhs, const QVersionNumber &rhs);
%End
%If (Qt_5_6_0 -)
bool operator>=(const QVersionNumber &lhs, const QVersionNumber &rhs);
%End
%If (Qt_5_6_0 -)
bool operator<(const QVersionNumber &lhs, const QVersionNumber &rhs);
%End
%If (Qt_5_6_0 -)
bool operator<=(const QVersionNumber &lhs, const QVersionNumber &rhs);
%End
%If (Qt_5_6_0 -)
bool operator==(const QVersionNumber &lhs, const QVersionNumber &rhs);
%End
%If (Qt_5_6_0 -)
bool operator!=(const QVersionNumber &lhs, const QVersionNumber &rhs);
%End
