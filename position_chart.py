import pyqtgraph as pg
import numpy as np
import time
from collections import deque
from PyQt5 import QtWidgets, QtCore


class PositionChart:
    """位置曲线图模块，用于实时显示位置数据"""
    
    def __init__(self, parent=None):
        """初始化位置曲线图"""
        self.parent = parent
        
        # 数据存储设置
        self.keep_all_data = True  # 保留所有历史数据
        self.max_points = 500      # 仅当keep_all_data=False时生效
        
        # 采样间隔（由用户设置，默认100ms）
        self.interval_ms = 100
        
        # 平滑选项
        self.smooth_position = False  # 位置数据平滑开关，默认关闭以保持数据真实性
        
        # 初始化数据存储
        if self.keep_all_data:
            self.time_data = []      # 无限制列表
            self.position_data = []  # 无限制列表
            self.velocity_data = []  # 速度数据列表
            self.smoothed_velocity_data = []  # 平滑后的速度数据
        else:
            self.time_data = deque(maxlen=self.max_points)  # 限制大小队列
            self.position_data = deque(maxlen=self.max_points)
            self.velocity_data = deque(maxlen=self.max_points)
            self.smoothed_velocity_data = deque(maxlen=self.max_points)  # 平滑后的速度数据
            
        self.start_time = time.time()
        
        # 显示模式：0=位置曲线，1=速度曲线
        self.display_mode = 0
        
        # 速度平滑设置
        self.smooth_velocity = True  # 默认启用速度平滑
        self.smooth_window = 25      # 平滑窗口大小（增大）
        
        # 初始化图表组件
        self.plot_widget = None
        self.position_curve = None
        self.velocity_curve = None
        self.control_widget = None
        self.point_info_text = None  # 点信息文本标签
        self.vertical_line = None    # 竖线标记（替代点标记）
        self.selected_point_index = None  # 当前选中的点索引
        
        # 坐标信息自动消失的定时器
        self.info_timer = None
        self.info_display_time = 3000  # 3秒，单位：毫秒
        self.info_always_show = False  # 是否一直显示坐标信息
        
        # 曲线吸附设置
        self.snap_threshold = 0.2  # 吸附阈值，设置为数据范围的20%，用户更容易点击到曲线
        
        # 缓冲数据，减少绘图频率
        self.buffer_positions = []
        self.buffer_times = []
        self.buffer_velocities = []
        self.need_update = False
        self.is_destroyed = False
        
        # 智能视图调整设置
        self.auto_scale = True
        self.normal_range_mode = True  # 新增：正常范围模式
        self.normal_range_min = None   # 正常数据的最小值
        self.normal_range_max = None   # 正常数据的最大值
        self.velocity_normal_range_min = None  # 速度数据的正常最小值
        self.velocity_normal_range_max = None  # 速度数据的正常最大值
        self.abnormal_threshold = 1000000  # 判断异常的阈值
        self.velocity_abnormal_threshold = 10000  # 速度的异常阈值
        self.recent_points_count = 10  # 用于计算当前趋势的点数
        self.last_value = None         # 上一个数据点
        self.last_time = None          # 上一个时间点
        self.view_width = 10          # 默认视图窗口宽度为10秒
        
        # 创建界面组件 - 移到属性设置后面
        self.setup_chart()
        self.setup_controls()
        
        # 用于限制更新频率的计时器
        self.update_timer = QtCore.QTimer()
        self.update_timer.timeout.connect(self.refresh_chart)
        self.update_timer.start(100)  # 100ms刷新一次

    def setup_chart(self):
        """设置图表组件"""
        # 创建绘图控件
        self.plot_widget = pg.PlotWidget()
        
        # 设置绘图控件样式
        self.plot_widget.setBackground('w')  # 白色背景
        self.plot_widget.showGrid(x=True, y=True)  # 显示网格
        
        # 添加标签
        self.plot_widget.setLabel('left', '位置值 (μm)')
        self.plot_widget.setLabel('bottom', '时间 (s)')
        
        # 添加标题
        title_style = {'color': '#000', 'font-size': '14pt'}
        self.plot_widget.setTitle('位置数据曲线图', **title_style)
        
        # 创建位置曲线
        pos_pen = pg.mkPen(color=(50, 100, 200), width=2)
        self.position_curve = self.plot_widget.plot([], [], pen=pos_pen, name="位置", 
                                                   connect='all',  # 确保所有点都连接
                                                   antialias=True)  # 启用抗锯齿
        
        # 创建速度曲线（初始隐藏）
        vel_pen = pg.mkPen(color=(200, 50, 50), width=2)
        self.velocity_curve = self.plot_widget.plot([], [], pen=vel_pen, name="速度",
                                                   connect='all',  # 确保所有点都连接
                                                   antialias=True)  # 启用抗锯齿
        self.velocity_curve.hide()
        
        # 添加图例
        self.plot_widget.addLegend()
        
        # 添加点信息标签
        self.point_info_text = pg.TextItem(text="", color=(0, 0, 0), anchor=(0, 1))
        self.point_info_text.setParentItem(self.plot_widget.getPlotItem())
        self.point_info_text.setPos(10, 10)  # 左上角位置
        self.point_info_text.setZValue(100)  # 确保在最上层
        self.point_info_text.hide()  # 初始状态隐藏
        
        # 创建信息显示定时器
        self.info_timer = QtCore.QTimer()
        self.info_timer.setSingleShot(True)
        self.info_timer.timeout.connect(self.hide_point_info)
        
        # 添加点击事件处理
        # 通过场景(scene)的鼠标点击信号
        try:
            scene = self.plot_widget.scene()
            if scene is not None:
                # 使用SignalProxy防止信号过快触发
                self.click_proxy = pg.SignalProxy(
                    scene.sigMouseClicked, 
                    rateLimit=60, 
                    slot=self.handle_mouse_click
                )
        except Exception as e:
            print(f"添加鼠标点击事件处理器失败: {str(e)}")
            
        # 添加视图范围变化监听，限制最小时间间隔
        self.plot_widget.sigRangeChanged.connect(self.on_range_changed)
        
        # 添加竖线标记（替代点标记）
        # 使用黑色实线
        self.vertical_line = pg.InfiniteLine(angle=90, movable=False, pen=pg.mkPen('k', width=1.5))
        self.vertical_line.hide()  # 初始隐藏
        self.plot_widget.addItem(self.vertical_line)
        
    def hide_point_info(self):
        """隐藏点信息和竖线标记（仅在非持续显示模式下）"""
        # 只有在非持续显示模式下才隐藏
        if not self.info_always_show:
            if self.point_info_text:
                self.point_info_text.hide()
            if self.vertical_line:
                self.vertical_line.hide()

    def setup_controls(self):
        """设置控制按钮"""
        # 创建控制按钮布局
        self.control_widget = QtWidgets.QWidget()
        layout = QtWidgets.QHBoxLayout(self.control_widget)
        layout.setContentsMargins(0, 0, 0, 0)
        
        # 移除历史数据模式开关，默认始终保留所有数据
        
        # 添加自动缩放开关
        self.auto_scale_checkbox = QtWidgets.QCheckBox("自动缩放")
        self.auto_scale_checkbox.setChecked(self.auto_scale)
        self.auto_scale_checkbox.toggled.connect(self.toggle_auto_scale)
        layout.addWidget(self.auto_scale_checkbox)
        
        # 正常范围模式默认启用，移除UI控件
        
        # 添加曲线类型切换按钮
        self.curve_toggle_button = QtWidgets.QPushButton("速度曲线")
        self.curve_toggle_button.clicked.connect(self.toggle_curve_type)
        layout.addWidget(self.curve_toggle_button)
        
        # 添加坐标信息持续显示开关
        self.info_always_checkbox = QtWidgets.QCheckBox("坐标常显")
        self.info_always_checkbox.setChecked(self.info_always_show)
        self.info_always_checkbox.toggled.connect(self.toggle_info_always_show)
        layout.addWidget(self.info_always_checkbox)

        # 添加位置平滑开关
        self.smooth_position_checkbox = QtWidgets.QCheckBox("位置平滑")
        self.smooth_position_checkbox.setChecked(self.smooth_position)
        self.smooth_position_checkbox.toggled.connect(self.toggle_smooth_position)
        layout.addWidget(self.smooth_position_checkbox)

        # 添加阶梯优化开关
        self.step_optimize = False
        self.step_optimize_checkbox = QtWidgets.QCheckBox("阶梯优化")
        self.step_optimize_checkbox.setChecked(self.step_optimize)
        self.step_optimize_checkbox.toggled.connect(self.toggle_step_optimize)
        layout.addWidget(self.step_optimize_checkbox)
        
        # 添加视图宽度控制
        width_layout = QtWidgets.QHBoxLayout()
        width_layout.addWidget(QtWidgets.QLabel("宽度(秒):"))
        self.view_width_spinbox = QtWidgets.QSpinBox()
        self.view_width_spinbox.setRange(5, 300)  # 5秒到5分钟
        self.view_width_spinbox.setValue(self.view_width)
        self.view_width_spinbox.valueChanged.connect(self.set_view_width)
        width_layout.addWidget(self.view_width_spinbox)
        
        # 创建一个小的容器来容纳宽度控制组件
        width_container = QtWidgets.QWidget()
        width_container.setLayout(width_layout)
        layout.addWidget(width_container)
        
        # 添加清除图表按钮
        clear_button = QtWidgets.QPushButton("清除图表")
        clear_button.clicked.connect(self.clear)
        layout.addWidget(clear_button)
        
        # 添加弹性空间
        layout.addStretch()

    def toggle_curve_type(self):
        """切换显示位置曲线或速度曲线"""
        # 切换显示模式
        self.display_mode = 1 - self.display_mode  # 0变1，1变0
        
        if self.display_mode == 0:  # 位置曲线
            # 更新UI
            title_style = {'color': '#000', 'font-size': '14pt'}
            self.plot_widget.setTitle('位置数据曲线图', **title_style)
            self.plot_widget.setLabel('left', '位置值 (μm)')
            self.curve_toggle_button.setText("速度曲线")
            
            # 显示位置曲线，隐藏速度曲线
            self.position_curve.show()
            self.velocity_curve.hide()
        else:  # 速度曲线
            # 更新UI
            title_style = {'color': '#000', 'font-size': '14pt'}
            self.plot_widget.setTitle('速度数据曲线图', **title_style)
            self.plot_widget.setLabel('left', '速度值 (m/s)')
            self.curve_toggle_button.setText("位置曲线")
            
            # 显示速度曲线，隐藏位置曲线
            self.position_curve.hide()
            self.velocity_curve.show()
            
            # 如果速度数据少，确保计算一下
            if len(self.velocity_data) < len(self.position_data) - 1:
                self.calculate_velocity_data()
        
        # 刷新图表
        self.need_update = True
        self.refresh_chart()
        
        # 如果启用了自动缩放，重新调整Y轴范围
        if self.auto_scale:
            self.auto_scale_chart(force_full=True)

    def toggle_info_always_show(self, checked):
        """切换坐标信息持续显示"""
        self.info_always_show = checked
        if not checked and hasattr(self, 'point_info_text'):
            self.hide_point_info()
    
    def toggle_smooth_position(self, checked):
        """切换位置数据平滑"""
        self.smooth_position = checked
        # 注意：不立即应用平滑，避免修改历史数据
        # 平滑只会在后续新增数据时生效
        print(f"位置平滑已{'启用' if checked else '禁用'}，将在新数据点添加时生效")

    def toggle_step_optimize(self, checked):
        """切换阶梯优化模式"""
        self.step_optimize = checked
        self.need_update = True
        self.refresh_chart()
        print(f"阶梯优化已{'启用' if checked else '禁用'}")

    def optimize_step_data(self, times, positions):
        """优化阶梯状数据，移除冗余的水平段点"""
        if len(times) < 3 or not self.step_optimize:
            return times, positions

        optimized_times = [times[0]]  # 保留第一个点
        optimized_positions = [positions[0]]

        for i in range(1, len(times) - 1):
            # 检查当前点是否在水平段的中间
            prev_pos = positions[i-1]
            curr_pos = positions[i]
            next_pos = positions[i+1]

            # 如果当前点不在水平段中间，或者是位置变化的关键点，则保留
            if not (prev_pos == curr_pos == next_pos):
                optimized_times.append(times[i])
                optimized_positions.append(positions[i])

        # 保留最后一个点
        optimized_times.append(times[-1])
        optimized_positions.append(positions[-1])

        return optimized_times, optimized_positions

    def interpolate_position_data(self, times, positions):
        """对位置数据进行插值，增加视觉连续性而不修改原始数据"""
        if len(times) < 2 or not hasattr(self, 'interpolate_display') or not self.interpolate_display:
            return times, positions

        # 创建更密集的时间点进行插值
        time_min, time_max = min(times), max(times)
        # 插值密度：原采样间隔的1/4
        interpolate_interval = self.interval_ms / 1000.0 / 4

        # 生成插值时间点
        interpolated_times = []
        current_time = time_min
        while current_time <= time_max:
            interpolated_times.append(current_time)
            current_time += interpolate_interval

        # 使用线性插值计算对应的位置值
        interpolated_positions = np.interp(interpolated_times, times, positions)

        return interpolated_times, interpolated_positions.tolist()
    
    def on_range_changed(self):
        """当视图范围变化时，限制最小时间间隔"""
        if self.plot_widget is None:
            return
            
        # 获取当前X轴范围
        view_range = self.plot_widget.getViewBox().viewRange()
        x_range = view_range[0]
        current_time_span = x_range[1] - x_range[0]
        
        # 计算最小允许的时间跨度（至少2个采样间隔）
        min_time_span = self.interval_ms / 1000.0 * 2
        
        # 如果当前时间跨度小于最小允许值，强制调整
        if current_time_span < min_time_span:
            center_time = (x_range[0] + x_range[1]) / 2
            new_x_min = center_time - min_time_span / 2
            new_x_max = center_time + min_time_span / 2
            
            # 临时断开信号连接，避免递归调用
            self.plot_widget.sigRangeChanged.disconnect(self.on_range_changed)
            self.plot_widget.setXRange(new_x_min, new_x_max, padding=0)
            self.plot_widget.sigRangeChanged.connect(self.on_range_changed)

    def set_interval(self, interval_ms):
        """设置采样时间间隔（毫秒）"""
        self.interval_ms = interval_ms
        # 当间隔变化时，可能需要重新计算速度
        if len(self.position_data) > 1:
            self.calculate_velocity_data()
    
    def calculate_velocity_data(self):
        """根据位置数据计算速度数据（单位：米/秒，位置单位为微米），滑动线性回归斜率法"""
        if len(self.position_data) < 3:
            return
        if not isinstance(self.velocity_data, list):
            self.velocity_data.clear()
        else:
            self.velocity_data[:] = []
        N = 5
        dt = self.interval_ms / 1000.0
        self.velocity_data.append(0)  # 第一个点速度为0
        for i in range(1, len(self.position_data)):
            left = max(0, i-N)
            n = i - left
            xs = np.arange(n)
            ys = np.array(self.position_data[left:i])
            x_mean = xs.mean()
            y_mean = ys.mean()
            var_x = ((xs - x_mean) ** 2).sum()
            if var_x == 0:
                slope = 0
            else:
                slope = ((xs - x_mean) * (ys - y_mean)).sum() / var_x
            velocity = slope / 1e6 / dt
            self.velocity_data.append(velocity)
        self.smooth_velocity_data()

    def toggle_history_mode(self, checked):
        """切换历史数据保留模式"""
        if self.keep_all_data == checked:
            return  # 状态没有变化
            
        # 保存当前数据
        current_time_data = list(self.time_data)
        current_position_data = list(self.position_data)
        current_velocity_data = list(self.velocity_data) # 保存速度数据
        current_smoothed_velocity_data = list(self.smoothed_velocity_data) # 保存平滑速度数据
        
        # 更新模式
        self.keep_all_data = checked
        
        # 根据新模式创建适当的数据存储
        if self.keep_all_data:
            # 切换到无限制列表
            self.time_data = current_time_data
            self.position_data = current_position_data
            self.velocity_data = current_velocity_data # 恢复速度数据
            self.smoothed_velocity_data = current_smoothed_velocity_data # 恢复平滑速度数据
        else:
            # 切换到限制大小队列，只保留最近的max_points个点
            self.time_data = deque(current_time_data[-self.max_points:] if len(current_time_data) > self.max_points else current_time_data, maxlen=self.max_points)
            self.position_data = deque(current_position_data[-self.max_points:] if len(current_position_data) > self.max_points else current_position_data, maxlen=self.max_points)
            self.velocity_data = deque(current_velocity_data[-self.max_points:] if len(current_velocity_data) > self.max_points else current_velocity_data, maxlen=self.max_points)
            self.smoothed_velocity_data = deque(current_smoothed_velocity_data[-self.max_points:] if len(current_smoothed_velocity_data) > self.max_points else current_smoothed_velocity_data, maxlen=self.max_points)
        
        # 更新图表
        self.need_update = True
        self.refresh_chart()
    
    def toggle_auto_scale(self, checked):
        """切换自动缩放模式"""
        self.auto_scale = checked
        if checked and len(self.position_data) > 0:
            self.auto_scale_chart()
    
    def toggle_normal_range(self, checked):
        """切换正常范围模式"""
        self.normal_range_mode = checked
        if checked:
            # 重置正常范围
            self.reset_normal_range()
        elif len(self.position_data) > 0:
            # 如果关闭了正常范围模式但启用了自动缩放，则显示所有数据
            if self.auto_scale:
                self.auto_scale_chart(force_full=True)
    
    def reset_normal_range(self):
        """重置正常范围计算"""
        self.normal_range_min = None
        self.normal_range_max = None
        # 根据最近的数据计算正常范围
        if len(self.position_data) > 0:
            # 获取最近的20个点或所有点（如果少于20个）
            recent_data = list(self.position_data)[-min(20, len(self.position_data)):]
            if recent_data:
                self.normal_range_min = min(recent_data)
                self.normal_range_max = max(recent_data)
                # 确保有一个最小范围
                if self.normal_range_max - self.normal_range_min < 10:
                    avg = (self.normal_range_max + self.normal_range_min) / 2
                    self.normal_range_min = avg - 5
                    self.normal_range_max = avg + 5
    
    def add_data_point(self, position):
        """添加一个数据点，同时计算速度"""
        # 使用基于采样间隔的理论时间戳，而不是真实时间
        # 这确保时间轴严格按照用户设定的采样频率显示
        data_point_count = len(self.position_data)
        time_point = data_point_count * (self.interval_ms / 1000.0)
        
        # 添加位置数据点
        if self.keep_all_data:
            self.time_data.append(time_point)
            self.position_data.append(position)
        else:
            self.time_data.append(time_point)
            self.position_data.append(position)
        
        # 计算速度 - 使用固定的采样间隔
        if self.last_value is not None:
            # 使用设定的采样间隔计算速度
            time_interval_s = self.interval_ms / 1000.0
            velocity = (position - self.last_value) / 1000000 / time_interval_s
        else:
            velocity = 0  # 首个点速度为0
        
        # 添加速度数据点
        if self.keep_all_data:
            if len(self.velocity_data) < len(self.position_data) - 1:
                # 确保之前的点都有速度值
                self.calculate_velocity_data()
            else:
                self.velocity_data.append(velocity)
                # 如果启用了平滑，更新平滑速度数据
                if self.smooth_velocity:
                    self.smooth_velocity_data()
        else:
            self.velocity_data.append(velocity)
            # 如果启用了平滑，更新平滑速度数据
            if self.smooth_velocity:
                self.smooth_velocity_data()
        
        # 更新正常范围
        self.update_normal_range(position)
        self.update_velocity_normal_range(velocity)
        
        # 检测是否需要立即调整视图
        if self.auto_scale:
            # 始终滚动视图以跟随最新数据点
            if self.keep_all_data and len(self.time_data) > 20:
                self.auto_scale_chart(focus_normal=self.normal_range_mode)
            # 正常范围模式下立即调整
            elif self.normal_range_mode:
                # 根据当前显示模式决定调整哪个视图
                if self.display_mode == 0:
                    self.check_for_view_adjustment(position)
                else:
                    self.check_for_velocity_view_adjustment(velocity)
        
        # 缓冲数据点，减少更新频率
        self.buffer_positions.append(position)
        self.buffer_times.append(time_point)
        self.buffer_velocities.append(velocity)
        self.need_update = True
        
        # 保存当前值作为last_value
        self.last_value = position
        self.last_time = time_point
        
        # 可选：对位置数据进行轻微平滑处理（降低频率，避免过度平滑）
        if hasattr(self, 'smooth_position') and self.smooth_position and len(self.position_data) % 5 == 0:
            # 每5个点才进行一次平滑，减少累积效应
            self.smooth_position_data()

    def update_normal_range(self, new_value):
        """更新正常数据范围"""
        if not self.normal_range_mode:
            return
            
        # 首次初始化位置正常范围
        if self.normal_range_min is None or self.normal_range_max is None:
            self.normal_range_min = new_value
            self.normal_range_max = new_value
            return
        
        # 判断值是否异常 - 与当前正常范围相差过大
        value_range = self.normal_range_max - self.normal_range_min
        threshold = max(self.abnormal_threshold, value_range * 2)  # 动态阈值
        
        is_abnormal = abs(new_value - (self.normal_range_min + self.normal_range_max)/2) > threshold
        
        if not is_abnormal:
            # 正常值，扩展正常范围
            self.normal_range_min = min(self.normal_range_min, new_value)
            self.normal_range_max = max(self.normal_range_max, new_value)
    
    def update_velocity_normal_range(self, velocity_value):
        """更新速度数据的正常范围"""
        if not self.normal_range_mode:
            return
            
        # 首次初始化速度正常范围
        if self.velocity_normal_range_min is None or self.velocity_normal_range_max is None:
            self.velocity_normal_range_min = velocity_value
            self.velocity_normal_range_max = velocity_value
            return
        
        # 判断值是否异常 - 与当前正常范围相差过大
        value_range = self.velocity_normal_range_max - self.velocity_normal_range_min
        threshold = max(self.velocity_abnormal_threshold, value_range * 2)  # 动态阈值
        
        is_abnormal = abs(velocity_value - (self.velocity_normal_range_min + self.velocity_normal_range_max)/2) > threshold
        
        if not is_abnormal:
            # 正常值，扩展正常范围
            self.velocity_normal_range_min = min(self.velocity_normal_range_min, velocity_value)
            self.velocity_normal_range_max = max(self.velocity_normal_range_max, velocity_value)
    
    def check_for_view_adjustment(self, new_value):
        """检查是否需要立即调整视图"""
        if self.last_value is None:
            return
            
        # 判断是否从异常值回到正常范围
        if self.is_within_normal_range(new_value, 0) and not self.is_within_normal_range(self.last_value, 0):
            # 从异常值回到正常范围，立即调整视图
            self.auto_scale_chart(focus_normal=True)
    
    def check_for_velocity_view_adjustment(self, new_velocity):
        """检查速度是否需要立即调整视图"""
        if len(self.velocity_data) < 2:
            return
            
        last_velocity = self.velocity_data[-2] if len(self.velocity_data) >= 2 else 0
        
        # 判断是否从异常值回到正常范围
        if self.is_within_normal_range(new_velocity, 1) and not self.is_within_normal_range(last_velocity, 1):
            # 从异常值回到正常范围，立即调整视图
            self.auto_scale_chart(focus_normal=True)
    
    def is_within_normal_range(self, value, mode=None):
        """判断值是否在正常范围内
        
        参数：
            value: 要判断的值
            mode: 模式，0=位置模式，1=速度模式，None=使用当前显示模式
        """
        # 如果未指定模式，使用当前显示模式
        if mode is None:
            mode = self.display_mode
            
        if mode == 0:  # 位置模式
            if self.normal_range_min is None or self.normal_range_max is None:
                return True
            
            # 添加一点余量
            margin = (self.normal_range_max - self.normal_range_min) * 0.5
            min_with_margin = self.normal_range_min - margin
            max_with_margin = self.normal_range_max + margin
            
            return min_with_margin <= value <= max_with_margin
        else:  # 速度模式
            if self.velocity_normal_range_min is None or self.velocity_normal_range_max is None:
                return True
                
            # 添加一点余量
            margin = (self.velocity_normal_range_max - self.velocity_normal_range_min) * 0.5
            min_with_margin = self.velocity_normal_range_min - margin
            max_with_margin = self.velocity_normal_range_max + margin
            
            return min_with_margin <= value <= max_with_margin
    
    def refresh_chart(self):
        """刷新图表显示"""
        if self.is_destroyed:
            return
            
        if not self.need_update:
            return
            
        try:
            if self.display_mode == 0:  # 位置曲线模式
                # 更新位置曲线数据
                if self.position_curve and len(self.time_data) > 0:
                    # 获取当前数据
                    times = list(self.time_data)
                    positions = list(self.position_data)

                    # 如果启用阶梯优化，先优化数据
                    if hasattr(self, 'step_optimize') and self.step_optimize:
                        times, positions = self.optimize_step_data(times, positions)

                    # 如果启用插值显示，对数据进行插值处理
                    if hasattr(self, 'interpolate_display') and self.interpolate_display:
                        times, positions = self.interpolate_position_data(times, positions)

                    # 更新曲线，明确设置连接模式确保连续线条
                    self.position_curve.setData(times, positions, connect='all', antialias=True)
            else:  # 速度曲线模式
                # 更新速度曲线数据
                if self.velocity_curve and len(self.time_data) > 0:
                    # 获取当前数据
                    times = list(self.time_data)
                    
                    # 确保速度数据已计算并平滑
                    if len(self.velocity_data) != len(self.position_data):
                        self.calculate_velocity_data()
                    
                    # 始终使用平滑后的速度数据
                    if len(self.smoothed_velocity_data) > 0:
                        velocities = list(self.smoothed_velocity_data)
                    else:
                        # 如果平滑数据不可用，使用原始数据
                        velocities = list(self.velocity_data)
                    
                    # 确保时间和速度数据长度匹配
                    min_len = min(len(times), len(velocities))
                    times_for_velocity = times[:min_len]
                    velocities = velocities[:min_len]
                    
                    # 更新曲线，明确设置连接模式确保连续线条
                    self.velocity_curve.setData(times_for_velocity, velocities, connect='all', antialias=True)
            
            # 如果启用了自动缩放，调整视图以适应当前数据
            if self.auto_scale:
                # 正常范围模式下，使用focus_normal=True
                self.auto_scale_chart(focus_normal=self.normal_range_mode)
                
            # 清除缓冲区
            self.buffer_positions = []
            self.buffer_times = []
            self.buffer_velocities = []
            self.need_update = False
            
        except Exception as e:
            print(f"刷新图表时出错: {str(e)}")
    
    def set_view_width(self, width):
        """设置视图窗口宽度（秒）"""
        self.view_width = width
        # 立即更新图表
        self.need_update = True
        self.refresh_chart()

    def auto_scale_chart(self, focus_normal=False, force_full=False):
        """自动调整图表尺度以适应当前数据
        
        参数:
            focus_normal: 是否聚焦在正常数据范围
            force_full: 是否强制显示全部数据范围
        """
        if len(self.position_data) > 1 and self.plot_widget is not None:
            # 根据当前显示模式选择要显示的数据
            y_data = self.position_data if self.display_mode == 0 else self.velocity_data
            
            # 确保有足够的数据
            if len(y_data) < 1:
                return
                
            if focus_normal and not force_full:
                # 根据当前模式选择合适的正常范围
                if self.display_mode == 0:  # 位置模式
                    # 使用位置数据的正常范围
                    if self.normal_range_min is not None and self.normal_range_max is not None:
                        range_min = self.normal_range_min
                        range_max = self.normal_range_max
                        # 如果范围太小，确保至少有一定的显示范围
                        range_y = range_max - range_min
                        if range_y < 10:
                            range_y = 10
                        # 添加30%的余量
                        margin = range_y * 0.3
                        y_min = range_min - margin
                        y_max = range_max + margin
                        # 设置Y轴范围
                        self.plot_widget.setYRange(y_min, y_max)
                else:  # 速度模式
                    if self.velocity_normal_range_min is not None and self.velocity_normal_range_max is not None:
                        range_min = self.velocity_normal_range_min
                        range_max = self.velocity_normal_range_max
                        # 如果范围太小，确保至少有一定的显示范围
                        range_y = range_max - range_min
                        if range_y < 0.1:  # 速度数据范围较小，使用更小的最小范围
                            range_y = 0.1
                        # 添加30%的余量
                        margin = range_y * 0.3
                        y_min = range_min - margin
                        y_max = range_max + margin
                        # 设置Y轴范围
                        self.plot_widget.setYRange(y_min, y_max)
                    else:
                        # 如果没有正常范围数据，则使用全部数据
                        force_full = True
            else:
                # 如果没有正常范围数据，则使用全部数据
                force_full = True
            
            # 如果要显示全部数据范围或没有正常范围数据
            if force_full:
                # 使用全部数据范围
                min_y = min(y_data)
                max_y = max(y_data) if len(y_data) > 1 else min_y + 1
                range_y = max_y - min_y
                
                # 如果范围太小，确保至少有一定的显示范围
                if self.display_mode == 0:  # 位置模式
                    if range_y < 10:
                        range_y = 10
                else:  # 速度模式
                    if range_y < 0.1:
                        range_y = 0.1
                
                # 添加10%的余量
                margin = range_y * 0.1
                y_min = min_y - margin
                y_max = max_y + margin
                
                # 设置Y轴范围
                self.plot_widget.setYRange(y_min, y_max)
            
            # 设置X轴自动调整以显示所有数据
            min_x = min(self.time_data)
            max_x = max(self.time_data)
            range_x = max_x - min_x
            
            # 如果保留所有历史数据，确保视图始终跟随最新的数据点
            if self.keep_all_data and len(self.time_data) > 20:
                # 获取最近的数据点时间
                latest_time = max(self.time_data)
                # 使用用户设置的视图窗口宽度
                view_width = self.view_width
                # 设置X轴范围从最新时间减去视图宽度到最新时间加一点余量
                margin_x = view_width * 0.1
                x_min = latest_time - view_width
                x_max = latest_time + margin_x
                
                # 限制最小显示间隔不能小于实际采样间隔
                min_time_range = self.interval_ms / 1000.0 * 2  # 至少显示2个采样间隔
                if (x_max - x_min) < min_time_range:
                    center_time = (x_min + x_max) / 2
                    x_min = center_time - min_time_range / 2
                    x_max = center_time + min_time_range / 2
                
                # 设置X轴范围
                self.plot_widget.setXRange(x_min, x_max)
            else:
                # 添加10%的余量
                margin_x = range_x * 0.1
                x_min = min_x - margin_x
                x_max = max_x + margin_x
                
                # 限制最小显示间隔不能小于实际采样间隔
                min_time_range = self.interval_ms / 1000.0 * 2  # 至少显示2个采样间隔
                if (x_max - x_min) < min_time_range:
                    center_time = (x_min + x_max) / 2
                    x_min = center_time - min_time_range / 2
                    x_max = center_time + min_time_range / 2
                
                # 设置X轴范围
                self.plot_widget.setXRange(x_min, x_max)

    def handle_mouse_click(self, event):
        """处理鼠标点击事件，只在点击曲线上或非常接近曲线时才显示坐标信息"""
        try:
            # event[0]是SignalProxy传递的原始事件
            original_event = event[0]
            
            # 确保是单击事件
            if original_event.double():
                return
                
            # 获取鼠标点击位置
            view_box = self.plot_widget.plotItem.vb
            if view_box is None:
                return
                
            scene_pos = original_event.scenePos()
            if scene_pos is None:
                return
                
            # 将场景坐标转换为视图坐标
            view_pos = view_box.mapSceneToView(scene_pos)
            click_x, click_y = view_pos.x(), view_pos.y()
            
            # 检查点击是否在曲线上或接近曲线
            self.snap_to_point = None  # 重置吸附点
            if self.is_point_on_curve(click_x, click_y):
                # 如果有吸附点，使用吸附点的坐标
                if hasattr(self, 'snap_to_point') and self.snap_to_point:
                    x, y = self.snap_to_point
                else:
                    x, y = click_x, click_y
                # 显示坐标信息
                self.show_click_position(x, y, scene_pos)
        except Exception as e:
            print(f"处理鼠标点击错误: {str(e)}")

    def is_point_on_curve(self, x, y):
        """检查点击是否在曲线上或非常接近曲线，具有增强的吸附功能"""
        if len(self.time_data) < 2:
            return False
            
        # 将时间和位置/速度数据转换为数组
        times = np.array(self.time_data)
        
        # 根据当前显示模式选择要显示的数据
        if self.display_mode == 0:
            # 位置模式
            values = np.array(self.position_data)
        else:
            # 速度模式 - 根据是否启用平滑选择使用原始速度数据或平滑后的速度数据
            if self.smooth_velocity and len(self.smoothed_velocity_data) > 0:
                values = np.array(self.smoothed_velocity_data)
            else:
                values = np.array(self.velocity_data)
        
        # 确保数据长度匹配
        if len(values) != len(times):
            # 如果长度不匹配，使用最短的长度
            min_len = min(len(times), len(values))
            times = times[:min_len]
            values = values[:min_len]
            
        # 找出点击x坐标左右两侧最近的数据点
        if x < min(times) or x > max(times):
            return False  # 点击在时间范围外
            
        # 找到最接近的两个时间点
        left_idx = np.where(times <= x)[0][-1] if any(times <= x) else 0
        right_idx = np.where(times >= x)[0][0] if any(times >= x) else len(times) - 1
        
        # 智能数据点选择：优先选择最近的数据点
        # 获取当前视图范围，用于计算合理的吸附范围
        view_range = self.plot_widget.getViewBox().viewRange()
        x_view_range = view_range[0]
        y_view_range = view_range[1]
        
        # 基于视图范围计算吸附阈值
        x_view_span = x_view_range[1] - x_view_range[0]
        y_view_span = y_view_range[1] - y_view_range[0]
        
        # 设置相对于视图的吸附范围
        x_snap_range = x_view_span * 0.03  # 视图宽度的3%
        y_snap_range = y_view_span * 0.05  # 视图高度的5%
        
        # 找到吸附范围内的所有数据点
        candidate_points = []
        for i, (t, v) in enumerate(zip(times, values)):
            x_dist = abs(x - t)
            y_dist = abs(y - v)
            
            # 如果在吸附范围内，计算综合距离
            if x_dist <= x_snap_range and y_dist <= y_snap_range:
                # 归一化距离计算
                norm_x_dist = x_dist / x_snap_range
                norm_y_dist = y_dist / y_snap_range
                combined_dist = (norm_x_dist**2 + norm_y_dist**2)**0.5
                candidate_points.append((combined_dist, i, t, v))
        
        if candidate_points:
            # 选择距离最近的点
            candidate_points.sort(key=lambda x: x[0])
            _, closest_idx, closest_t, closest_v = candidate_points[0]
            
            # 吸附到最近的实际数据点
            self.snap_to_point = (closest_t, closest_v)
            return True
        
        return False

    def get_position_at_time(self, target_time):
        """根据给定时间点查找对应的位置值，使用线性插值"""
        if len(self.time_data) == 0 or len(self.position_data) == 0:
            return None
            
        # 将数据转换为数组便于处理
        times = list(self.time_data)
        positions = list(self.position_data)
        
        # 确保数据长度匹配
        min_len = min(len(times), len(positions))
        times = times[:min_len]
        positions = positions[:min_len]
        
        if len(times) == 0:
            return None
            
        # 如果时间点在数据范围之外，返回None
        if target_time < min(times) or target_time > max(times):
            return None
            
        # 查找最接近的数据点
        for i in range(len(times)):
            if abs(times[i] - target_time) < 0.001:  # 如果非常接近某个数据点
                return positions[i]
                
        # 查找插值所需的两个点
        left_idx = None
        right_idx = None
        
        for i in range(len(times) - 1):
            if times[i] <= target_time <= times[i + 1]:
                left_idx = i
                right_idx = i + 1
                break
                
        if left_idx is None or right_idx is None:
            # 如果没找到合适的区间，返回最接近的点
            closest_idx = min(range(len(times)), key=lambda i: abs(times[i] - target_time))
            return positions[closest_idx]
            
        # 线性插值计算位置值
        t1, p1 = times[left_idx], positions[left_idx]
        t2, p2 = times[right_idx], positions[right_idx]
        
        if t2 - t1 == 0:  # 避免除以零
            return p1
            
        # 线性插值公式
        interpolated_position = p1 + (p2 - p1) * (target_time - t1) / (t2 - t1)
        return interpolated_position

    def get_velocity_at_time(self, target_time):
        """根据给定时间点计算该时间点的实际速度（基于前后位置差）"""
        if len(self.time_data) < 2 or len(self.position_data) < 2:
            return None
            
        # 将数据转换为列表便于处理
        times = list(self.time_data)
        positions = list(self.position_data)
        
        # 确保数据长度匹配
        min_len = min(len(times), len(positions))
        times = times[:min_len]
        positions = positions[:min_len]
        
        if len(times) < 2:
            return None
            
        # 如果时间点在数据范围之外，返回None
        if target_time < min(times) or target_time > max(times):
            return None
            
        # 查找目标时间点前后的数据点来计算速度
        left_idx = None
        right_idx = None
        
        # 找到目标时间点两侧的数据点
        for i in range(len(times) - 1):
            if times[i] <= target_time <= times[i + 1]:
                left_idx = i
                right_idx = i + 1
                break
                
        if left_idx is None or right_idx is None:
            # 如果没找到合适的区间，使用最接近的两个点
            closest_idx = min(range(len(times)), key=lambda i: abs(times[i] - target_time))
            if closest_idx == 0:
                left_idx, right_idx = 0, 1
            elif closest_idx == len(times) - 1:
                left_idx, right_idx = len(times) - 2, len(times) - 1
            else:
                # 使用最接近点的前后两点
                left_idx, right_idx = closest_idx - 1, closest_idx + 1
                
        # 计算速度：位置差 / 时间差，并转换单位
        t1, p1 = times[left_idx], positions[left_idx]
        t2, p2 = times[right_idx], positions[right_idx]
        
        if t2 - t1 == 0:  # 避免除以零
            return None
            
        # 计算速度：(位置差(微米) / 1000000) / 时间差(秒) = 米/秒
        velocity = (p2 - p1) / 1000000 / (t2 - t1)
        return velocity

    def show_click_position(self, x, y, scene_pos=None):
        """显示点击位置的坐标信息，在鼠标附近，3秒后自动消失"""
        # 确保所有需要的组件都已初始化
        if not hasattr(self, 'point_info_text') or self.point_info_text is None:
            return
            
        if not hasattr(self, 'vertical_line') or self.vertical_line is None:
            return
        
        # 停止任何正在运行的定时器
        if hasattr(self, 'info_timer') and self.info_timer and self.info_timer.isActive():
            self.info_timer.stop()
            
        # 格式化时间和位置信息
        time_str = f"{x:.2f}秒"
        
        # 根据当前模式决定显示信息
        if self.display_mode == 0:
            # 位置模式：只显示位置值
            value_str = f"{int(y)} μm"
            info_text = f"时间: {time_str}\n位置: {value_str}"
        else:
            # 速度模式：重新计算该时间点的实际速度，并显示位置值
            
            # 查找对应时间点的位置值
            position_value = self.get_position_at_time(x)
            
            # 重新计算该时间点的实际速度
            actual_velocity = self.get_velocity_at_time(x)
            
            if actual_velocity is not None:
                velocity_str = f"{actual_velocity:.6f} m/s"
            else:
                velocity_str = f"{y:.6f} m/s"  # fallback到插值速度
                
            if position_value is not None:
                position_str = f"{int(position_value)} μm"
                info_text = f"时间: {time_str}\n速度: {velocity_str}\n位置: {position_str}"
            else:
                info_text = f"时间: {time_str}\n速度: {velocity_str}"
        
        # 更新文本标签
        self.point_info_text.setText(info_text)
        
        # 将信息显示在鼠标点击位置附近
        # 偏移一点距离，避免遮挡
        offset_x = 10  # 像素偏移
        offset_y = -20  # 像素偏移，向上一点
        
        # 如果有场景位置信息，直接设置文本项的场景位置
        if scene_pos is not None:
            scene_x = scene_pos.x() + offset_x
            scene_y = scene_pos.y() + offset_y
            self.point_info_text.setPos(scene_x, scene_y)
        else:
            # 否则使用数据坐标
            self.point_info_text.setPos(x + 0.5, y + 0.2)  # 数据坐标偏移
            
        # 显示文本（确保可见）
        self.point_info_text.show()
        
        # 设置并显示竖线标记在点击的X坐标位置
        self.vertical_line.setPos(x)
        self.vertical_line.show()
        
        # 只有在非持续显示模式下才启动定时器隐藏信息
        if not self.info_always_show:
            self.info_timer.start(self.info_display_time)

    def clear(self):
        """清除图表数据"""
        if self.keep_all_data:
            self.time_data = []
            self.position_data = []
            self.velocity_data = []
            self.smoothed_velocity_data = []
        else:
            self.time_data.clear()
            self.position_data.clear()
            self.velocity_data.clear()
            self.smoothed_velocity_data.clear()
            
        self.buffer_positions = []
        self.buffer_times = []
        self.buffer_velocities = []
        self.normal_range_min = None
        self.normal_range_max = None
        self.velocity_normal_range_min = None
        self.velocity_normal_range_max = None
        self.last_value = None
        self.last_time = None
        
        if self.position_curve:
            self.position_curve.setData([], [])
        if self.velocity_curve:
            self.velocity_curve.setData([], [])
        
        # 清除点信息和竖线
        if hasattr(self, 'point_info_text') and self.point_info_text:
            self.point_info_text.setText("")
            self.point_info_text.hide()
        if hasattr(self, 'vertical_line') and self.vertical_line:
            self.vertical_line.hide()
            
        self.need_update = True
    
    def get_widget(self):
        """返回完整的图表控件，包括控制按钮"""
        # 创建一个容器控件
        container = QtWidgets.QWidget()
        layout = QtWidgets.QVBoxLayout(container)
        layout.setContentsMargins(0, 0, 0, 0)
        
        # 添加绘图控件
        if self.plot_widget:
            layout.addWidget(self.plot_widget)
        
        # 添加控制按钮
        if self.control_widget:
            layout.addWidget(self.control_widget)
        
        return container
    
    def get_data(self):
        """获取当前的数据，用于导出"""
        return list(self.time_data), list(self.position_data)
    
    def __del__(self):
        """析构函数，确保释放资源"""
        self.is_destroyed = True
        try:
            if hasattr(self, 'update_timer') and self.update_timer.isActive():
                self.update_timer.stop()
        except RuntimeError:
            # 忽略C++对象已删除的错误
            pass 

    def find_closest_point(self, click_x, click_y):
        """找到最接近给定坐标的数据点"""
        if len(self.time_data) == 0:
            return None
            
        # 将时间数据和位置数据转换为数组以便计算
        times = np.array(self.time_data)
        positions = np.array(self.position_data)
        
        # 计算每个点到鼠标点击位置的距离
        # 使用归一化距离，考虑到x轴和y轴的比例可能不同
        x_range = np.max(times) - np.min(times) if len(times) > 1 else 1
        y_range = np.max(positions) - np.min(positions) if len(positions) > 1 else 1
        
        # 避免除以零
        x_range = max(x_range, 0.001)
        y_range = max(y_range, 0.001)
        
        # 计算归一化距离
        distances = np.sqrt(((times - click_x) / x_range) ** 2 + 
                            ((positions - click_y) / y_range) ** 2)
        
        # 找到最小距离的点
        closest_index = np.argmin(distances)
        
        # 检查最小距离是否在合理范围内（避免点击离曲线太远的地方）
        if distances[closest_index] < 0.1:  # 可根据需要调整阈值
            return closest_index
        else:
            return None

    def show_point_info(self, index, time_value, position_value):
        """显示数据点的详细信息"""
        # 确保所有需要的组件都已初始化
        if not hasattr(self, 'point_info_text') or self.point_info_text is None:
            return
            
        if not hasattr(self, 'vertical_line') or self.vertical_line is None:
            return
            
        # 保存当前选中的点索引
        self.selected_point_index = index
        
        # 格式化时间和位置/速度信息
        time_str = f"{time_value:.2f}秒"
        
        # 根据当前模式决定显示信息
        if self.display_mode == 0:
            # 位置模式：只显示位置值
            position_str = f"{position_value:.0f} μm"
            info_text = f"时间: {time_str}\n位置: {position_str}"
        else:
            # 速度模式：同时显示速度值和位置值
            if index < len(self.velocity_data):
                if self.smooth_velocity and len(self.smoothed_velocity_data) > index:
                    velocity_value = self.smoothed_velocity_data[index]
                else:
                    velocity_value = self.velocity_data[index]
                velocity_str = f"{velocity_value:.6f} m/s"
                
                # 获取对应的位置值
                if index < len(self.position_data):
                    position_str = f"{self.position_data[index]:.0f} μm"
                    info_text = f"时间: {time_str}\n速度: {velocity_str}\n位置: {position_str}"
                else:
                    info_text = f"时间: {time_str}\n速度: {velocity_str}"
            else:
                info_text = f"时间: {time_str}\n速度: N/A"
        
        # 更新文本标签
        self.point_info_text.setText(info_text)
        
        # 显示文本（确保可见）
        self.point_info_text.show()
        
        # 高亮显示选中的点
        self.vertical_line.setPos(time_value)
        self.vertical_line.show() 
    
    def toggle_smooth_velocity(self, checked):
        """切换速度平滑模式"""
        self.smooth_velocity = checked
        # 如果开启平滑，重新计算平滑速度
        if checked and len(self.velocity_data) > 0:
            self.smooth_velocity_data()
            self.need_update = True
            self.refresh_chart()
    
    def smooth_velocity_data(self):
        """使用强平滑算法处理速度数据，生成平滑连续的速度曲线"""
        if len(self.velocity_data) < 2:
            return
            
        # 清空现有平滑速度数据
        if not isinstance(self.smoothed_velocity_data, list):
            self.smoothed_velocity_data.clear()
        else:
            self.smoothed_velocity_data[:] = []
            
        # 获取速度数据列表
        velocity_list = list(self.velocity_data)
        
        # 如果数据点太少，简单拷贝
        if len(velocity_list) <= 3:
            if isinstance(self.smoothed_velocity_data, list):
                self.smoothed_velocity_data.extend(velocity_list)
            else:
                for v in velocity_list:
                    self.smoothed_velocity_data.append(v)
            return
        
        # 超强平滑处理 - 使用三级平滑
        
        # 第一级：去除异常值和极端噪声
        filtered_data = []
        
        # 计算统计量，用于异常值检测
        abs_values = [abs(v) for v in velocity_list]
        mean_value = sum(abs_values) / len(abs_values) if abs_values else 0
        
        # 中位数滤波，减少离群点影响
        window_size = min(15, len(velocity_list))
        half_window = window_size // 2
        
        for i in range(len(velocity_list)):
            # 获取窗口数据
            start = max(0, i - half_window)
            end = min(len(velocity_list), i + half_window + 1)
            window = velocity_list[start:end]
            
            # 排序找中位数
            sorted_window = sorted(window)
            median = sorted_window[len(sorted_window) // 2]
            
            # 使用中位数替代原始值
            filtered_data.append(median)
        
        # 第二级：长窗口移动平均平滑 - 使用更大的窗口
        smoothed_data = []
        window_size = min(31, len(filtered_data))  # 使用更大的窗口
        half_window = window_size // 2
        
        for i in range(len(filtered_data)):
            # 计算窗口范围
            start = max(0, i - half_window)
            end = min(len(filtered_data), i + half_window + 1)
            window_values = filtered_data[start:end]
            
            # 加权平均，中心点权重最高
            weighted_sum = 0
            total_weight = 0
            
            for j, val in enumerate(window_values):
                # 距离中心越近权重越大，使用二次函数增强中心权重
                dist = abs(j - (end - start) // 2)
                weight = 1.0 / (1.0 + dist * dist * 0.2)
                weighted_sum += val * weight
                total_weight += weight
                
            avg = weighted_sum / total_weight if total_weight > 0 else sum(window_values) / len(window_values)
            smoothed_data.append(avg)
            
        # 第三级：趋势保留平滑 - 避免过度平滑导致失去趋势
        trend_preserved_data = []
        window_size = min(11, len(smoothed_data))
        half_window = window_size // 2
        
        for i in range(len(smoothed_data)):
            # 如果是窗口边缘区域，直接使用第二级平滑结果
            if i < half_window or i >= len(smoothed_data) - half_window:
                trend_preserved_data.append(smoothed_data[i])
                continue
                
            # 计算窗口范围
            window_values = smoothed_data[i-half_window:i+half_window+1]
            
            # 计算窗口内的线性趋势
            x = list(range(len(window_values)))
            y = window_values
            
            # 简化的线性拟合，计算斜率和截距
            n = len(x)
            sum_x = sum(x)
            sum_y = sum(y)
            sum_xy = sum(x[j] * y[j] for j in range(n))
            sum_xx = sum(x[j] * x[j] for j in range(n))
            
            # 计算斜率和截距
            slope = (n * sum_xy - sum_x * sum_y) / (n * sum_xx - sum_x * sum_x) if n * sum_xx - sum_x * sum_x != 0 else 0
            intercept = (sum_y - slope * sum_x) / n
            
            # 使用线性拟合值和平滑值的加权平均，保留趋势同时平滑噪声
            fitted_value = intercept + slope * half_window  # 中心点的拟合值
            smoothed_value = smoothed_data[i]
            
            # 如果斜率较大，增加拟合值的权重；如果斜率较小，增加平滑值的权重
            slope_weight = min(1.0, abs(slope) * 10)  # 斜率权重，最大为1
            trend_value = fitted_value * slope_weight + smoothed_value * (1 - slope_weight)
            
            trend_preserved_data.append(trend_value)
        
        # 将最终平滑结果添加到smoothed_velocity_data中
        if isinstance(self.smoothed_velocity_data, list):
            self.smoothed_velocity_data.extend(trend_preserved_data)
        else:
            for v in trend_preserved_data:
                self.smoothed_velocity_data.append(v) 
    
    def smooth_position_data(self):
        """对位置数据进行轻微平滑处理，减少阶梯效应，但保持数据真实性"""
        if len(self.position_data) < 3:
            return

        # 只对最新的几个点进行轻微平滑，避免累积效应
        # 只平滑最后3个点，且平滑强度很小
        smooth_count = min(3, len(self.position_data))
        start_index = len(self.position_data) - smooth_count

        # 使用很小的平滑窗口和较低的平滑强度
        for i in range(start_index, len(self.position_data)):
            if i == 0 or i == len(self.position_data) - 1:
                continue  # 跳过第一个和最后一个点

            # 获取前后点
            prev_val = self.position_data[i-1] if i > 0 else self.position_data[i]
            curr_val = self.position_data[i]
            next_val = self.position_data[i+1] if i < len(self.position_data)-1 else self.position_data[i]

            # 轻微平滑：只使用10%的平滑强度
            smoothed_val = curr_val * 0.9 + (prev_val + next_val) * 0.05

            # 限制平滑幅度，避免过度修改
            max_change = abs(curr_val) * 0.01  # 最大只能改变1%
            change = smoothed_val - curr_val
            if abs(change) > max_change:
                change = max_change if change > 0 else -max_change

            self.position_data[i] = curr_val + change