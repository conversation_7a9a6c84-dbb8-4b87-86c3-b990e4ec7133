// qmediametadata.sip generated by MetaSIP
//
// This file is part of the QtMultimedia Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


namespace QMediaMetaData
{
%TypeHeaderCode
#include <qmediametadata.h>
%End

    const QString Title;
    const QString SubTitle;
    const QString Author;
    const QString Comment;
    const QString Description;
    const QString Category;
    const QString Genre;
    const QString Year;
    const QString Date;
    const QString UserRating;
    const QString Keywords;
    const QString Language;
    const QString Publisher;
    const QString Copyright;
    const QString ParentalRating;
    const QString RatingOrganization;
    const QString Size;
    const QString MediaType;
    const QString Duration;
    const QString AudioBitRate;
    const QString AudioCodec;
    const QString AverageLevel;
    const QString ChannelCount;
    const QString PeakValue;
    const QString SampleRate;
    const QString AlbumTitle;
    const QString AlbumArtist;
    const QString ContributingArtist;
    const QString Composer;
    const QString Conductor;
    const QString Lyrics;
    const QString Mood;
    const QString TrackNumber;
    const QString TrackCount;
    const QString CoverArtUrlSmall;
    const QString CoverArtUrlLarge;
    const QString Resolution;
    const QString PixelAspectRatio;
    const QString VideoFrameRate;
    const QString VideoBitRate;
    const QString VideoCodec;
    const QString PosterUrl;
    const QString ChapterNumber;
    const QString Director;
    const QString LeadPerformer;
    const QString Writer;
    const QString CameraManufacturer;
    const QString CameraModel;
    const QString Event;
    const QString Subject;
    const QString Orientation;
    const QString ExposureTime;
    const QString FNumber;
    const QString ExposureProgram;
    const QString ISOSpeedRatings;
    const QString ExposureBiasValue;
    const QString DateTimeOriginal;
    const QString DateTimeDigitized;
    const QString SubjectDistance;
    const QString MeteringMode;
    const QString LightSource;
    const QString Flash;
    const QString FocalLength;
    const QString ExposureMode;
    const QString WhiteBalance;
    const QString DigitalZoomRatio;
    const QString FocalLengthIn35mmFilm;
    const QString SceneCaptureType;
    const QString GainControl;
    const QString Contrast;
    const QString Saturation;
    const QString Sharpness;
    const QString DeviceSettingDescription;
    const QString GPSLatitude;
    const QString GPSLongitude;
    const QString GPSAltitude;
    const QString GPSTimeStamp;
    const QString GPSSatellites;
    const QString GPSStatus;
    const QString GPSDOP;
    const QString GPSSpeed;
    const QString GPSTrack;
    const QString GPSTrackRef;
    const QString GPSImgDirection;
    const QString GPSImgDirectionRef;
    const QString GPSMapDatum;
    const QString GPSProcessingMethod;
    const QString GPSAreaInformation;
    const QString PosterImage;
    const QString CoverArtImage;
    const QString ThumbnailImage;
};
