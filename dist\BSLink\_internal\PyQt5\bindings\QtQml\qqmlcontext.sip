// qqmlcontext.sip generated by MetaSIP
//
// This file is part of the QtQml Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QQmlContext : public QObject
{
%TypeHeaderCode
#include <qqmlcontext.h>
%End

public:
    QQmlContext(QQmlEngine *engine, QObject *parent /TransferThis/ = 0);
    QQmlContext(QQmlContext *parentContext, QObject *parent /TransferThis/ = 0);
    virtual ~QQmlContext();
    bool isValid() const;
    QQmlEngine *engine() const;
    QQmlContext *parentContext() const;
    QObject *contextObject() const;
    void setContextObject(QObject *);
    QVariant contextProperty(const QString &) const;
    void setContextProperty(const QString &, QObject *);
    void setContextProperty(const QString &, const QVariant &);
    QString nameForObject(QObject *) const;
    QUrl resolvedUrl(const QUrl &);
    void setBaseUrl(const QUrl &);
    QUrl baseUrl() const;
%If (Qt_5_11_0 -)

    struct PropertyPair
    {
%TypeHeaderCode
#include <qqmlcontext.h>
%End

        QString name;
        QVariant value;
    };

%End
%If (Qt_5_11_0 -)
    void setContextProperties(const QVector<QQmlContext::PropertyPair> &properties);
%End
};
