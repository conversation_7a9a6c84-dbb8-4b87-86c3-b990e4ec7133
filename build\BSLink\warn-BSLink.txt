
This file lists modules PyInstaller was not able to find. This does not
necessarily mean this module is required for running your program. Python and
Python 3rd-party packages include a lot of conditional or optional modules. For
example the module 'ntpath' only exists on Windows, whereas the module
'posixpath' only exists on Posix systems.

Types if import:
* top-level: imported at the top-level - look at these first
* conditional: imported within an if-statement
* delayed: imported within a function
* optional: imported within a try-except-statement

IMPORTANT: Do NOT post this list to the issue-tracker. Use it as a basis for
            tracking down the missing module yourself. Thanks!

missing module named org - imported by copy (optional)
missing module named _frozen_importlib_external - imported by importlib._bootstrap (delayed), importlib (optional), importlib.abc (optional), zipimport (top-level)
excluded module named _frozen_importlib - imported by importlib (optional), importlib.abc (optional), zipimport (top-level)
missing module named grp - imported by shutil (optional), tarfile (optional), pathlib (delayed)
missing module named pwd - imported by shutil (optional), tarfile (optional), pathlib (delayed, conditional, optional), posixpath (delayed, conditional), netrc (delayed, conditional), getpass (delayed)
missing module named 'org.python' - imported by pickle (optional), xml.sax (delayed, conditional)
missing module named posix - imported by os (conditional, optional), shutil (conditional)
missing module named resource - imported by posix (top-level)
missing module named pyimod02_importers - imported by D:\wenjian\BS-Link\.venv\Lib\site-packages\PyInstaller\hooks\rthooks\pyi_rth_pkgutil.py (delayed)
missing module named urlparse - imported by serial.rfc2217 (optional), serial.urlhandler.protocol_alt (optional), serial.urlhandler.protocol_cp2110 (optional), serial.urlhandler.protocol_loop (optional), serial.urlhandler.protocol_socket (optional), serial.urlhandler.protocol_spy (optional)
missing module named Queue - imported by serial.rfc2217 (optional), serial.urlhandler.protocol_cp2110 (optional), serial.urlhandler.protocol_loop (optional)
missing module named hid - imported by serial.urlhandler.protocol_cp2110 (top-level)
missing module named fcntl - imported by serial.serialposix (top-level), serial.tools.miniterm (conditional)
missing module named termios - imported by serial.serialposix (top-level), serial.tools.miniterm (conditional), getpass (optional)
missing module named vms_lib - imported by platform (delayed, conditional, optional)
missing module named 'java.lang' - imported by platform (delayed, optional), xml.sax._exceptions (conditional)
missing module named java - imported by platform (delayed)
missing module named _winreg - imported by platform (delayed, optional)
missing module named _posixsubprocess - imported by subprocess (optional)
missing module named 'System.IO' - imported by serial.serialcli (top-level)
missing module named System - imported by serial.serialcli (top-level)
missing module named _scproxy - imported by urllib.request (conditional)
missing module named PyQt5.uic.port_v2 - imported by PyQt5.uic.properties (conditional), D:\wenjian\BS-Link\BSmain.py (top-level)
missing module named 'PyQt5.uic.port_v2.as_string' - imported by PyQt5.uic.Compiler.qtproxies (conditional), PyQt5.uic.Compiler.qobjectcreator (conditional)
missing module named 'PyQt5.uic.port_v2.proxy_base' - imported by PyQt5.uic.Compiler.qtproxies (conditional)
missing module named 'PyQt5.uic.port_v2.string_io' - imported by PyQt5.uic (delayed, conditional)
missing module named pandas - imported by D:\wenjian\BS-Link\BSmain.py (delayed, optional)
