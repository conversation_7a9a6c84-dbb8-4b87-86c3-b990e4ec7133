#pyuic5 -o BSLink_ui.py BSLink.ui
#pyinstaller -F -w -i D:\wenjian\BS-Link\tupiao.ico --name "BSLink调试助手" BSmain.py
#pyinstaller --clean BSLink调试助手.spec
#打包用   pyinstaller --clean BSLink调试助手.spec

import sys
import serial
import serial.tools.list_ports
from PyQt5 import QtWidgets, QtCore, QtGui
from PyQt5.QtCore import QTimer
from BSLink_ui import Ui_Form
import os
import time
import re
# 导入位置曲线图模块
try:
    import position_chart
    HAS_POSITION_CHART = True
except ImportError:
    print("警告: 未找到position_chart模块或缺少pyqtgraph依赖，位置曲线图功能将被禁用")
    HAS_POSITION_CHART = False

# 在创建QApplication之前设置DPI缩放
QtWidgets.QApplication.setAttribute(QtCore.Qt.AA_EnableHighDpiScaling, True)
QtWidgets.QApplication.setAttribute(QtCore.Qt.AA_UseHighDpiPixmaps, True)  

class BSLink(QtWidgets.QWidget, Ui_Form):
    def __init__(self):
        super(BSLink, self).__init__()
        self.setupUi(self)
        
        # 设置窗口和任务栏图标
        try:
            # 获取应用程序路径
            if getattr(sys, 'frozen', False):
                # 如果是打包后的exe
                application_path = sys._MEIPASS
            else:
                # 如果是python脚本
                application_path = os.path.dirname(os.path.abspath(__file__))
            
            icon_path = os.path.join(application_path, 'tupiao.ico')
            icon = QtGui.QIcon(icon_path)
            self.setWindowIcon(icon)
            
            # 设置Windows任务栏图标
            if sys.platform == 'win32':
                import ctypes
                myappid = 'com.aegmeasure.bslink.2.0'
                ctypes.windll.shell32.SetCurrentProcessExplicitAppUserModelID(myappid)
        except Exception as e:
            print(f"设置图标错误: {str(e)}")
        
        self.setWindowTitle("BSLink")
        
        # 创建公司信息标签
        self.company_label = QtWidgets.QLabel(self)
        self.company_label.setAlignment(QtCore.Qt.AlignCenter)  # 居中对齐
        self.company_label.setStyleSheet("""
            QLabel {
                color: #666666;
                background-color: #f5f5f5;
                padding: 2px;
                margin: 2px;
                border-top: 1px solid #dddddd;
                font-size: 9pt;
            }
        """)
        
        # 设置公司信息文本
        self.company_label.setText(" 宁波埃格测控技术有限公司 | 磁传感器、磁栅数显表、磁尺、角度表直线电机| www.chinaibb.com")
        
        # 将标签添加到主布局的底部
        self.gridLayout.addWidget(self.company_label, self.gridLayout.rowCount(), 0, 1, -1)
        
        # 初始化属性
        self.serial = None
        self.last_command_type = None
        self.current_device_station_id = 1
        self.parameter_buffer = ""
        self.last_receive_time = 0
        self.text_display_buffer = ""
        self.last_text_time = 0
        
        # 确保UI控件已正确初始化
        self.lineEdit_4 = self.findChild(QtWidgets.QLineEdit, "lineEdit_4")  # BSL输入框
        self.lineEdit_7 = self.findChild(QtWidgets.QLineEdit, "lineEdit_7")  # BSR输入框
        
        # 设置默认字体
        default_font = QtGui.QFont("Microsoft YaHei", 9)
        self.setFont(default_font)
        
        # 专门为textBrowser和textBrowser_2设置等宽字体
        mono_font = QtGui.QFont("Consolas", 9)
        self.textBrowser.setFont(mono_font)
        self.textBrowser_2.setFont(mono_font)
        
        # 创建数据接收缓冲区
        self.receive_buffer = bytearray()
        self.modbus_buffer = bytearray()  # 专门用于Modbus数据
        self.last_receive_time = 0  # 新增: 记录上次接收数据的时间
        
        # 设置串口对象
        self.serial = serial.Serial()
        self.serial.timeout = 0.1  # 设置读取超时
        
        # 设置按钮连接
        self.pushButton_Open.clicked.connect(self.open_serial)
        self.pushButton_6.clicked.connect(self.send_data)
        self.pushButton_ClearReceive.clicked.connect(self.clear_receive)
        self.pushButton_7.clicked.connect(self.calibrate)
        self.pushButton.clicked.connect(self.read_position)
         # 添加BSB按钮连接
        self.pushButton_10.clicked.connect(self.write_bsb_value)
        # 添加导出数据按钮连接
        self.pushButton_2.clicked.connect(self.export_position_data)
        
        # 添加定时读取复选框连接
        self.checkBox.toggled.connect(self.toggle_timed_position_reading)
        
        # 添加参数页面按钮连接
        # 站号行
        self.pushButton_3.clicked.connect(self.read_device_station_id)  # 读站号
        self.pushButton_11.clicked.connect(self.write_station_id)       # 写站号
        
        # 波特率行 
        self.pushButton_5.clicked.connect(self.read_device_baudrate)    # 读波特率
        self.pushButton_13.clicked.connect(self.write_baudrate)         # 写波特率
        
        # 协议行
        self.pushButton_4.clicked.connect(self.read_device_protocol)    # 读协议
        self.pushButton_12.clicked.connect(self.write_protocol)         # 写协议
        
        # 创建定时读取位置的定时器
        self.position_timer = QTimer(self)
        self.position_timer.timeout.connect(self.read_position)
        
        # 存储位置数据
        self.position_data_list = []
        
        # 添加时间记录相关变量
        self.position_read_start_time = None  # 开始读取位置的基准时间
        self.position_read_count = 0  # 读取位置的次数计数
        self.actual_interval_ms = 100  # 实际设置的时间间隔
        
        # 设置错误显示控件的初始状态
        self.set_position_status_normal()
        
        # 初始化其他组件
        self.init_combobox()
        self.setup_send_features()
        
        # 移除固定大小的窗口标志，使用普通窗口标志
        self.setWindowFlags(QtCore.Qt.Window)
        
        # 设置布局为可调整大小
        for layout in self.findChildren(QtWidgets.QLayout):
            layout.setSizeConstraint(QtWidgets.QLayout.SetDefaultConstraint)
        
        # 设置统一样式
        self.setStyle(QtWidgets.QStyleFactory.create("Fusion"))
        
        # 定时器，用于定时检查串口接收缓冲区
        self.timer = QTimer(self)
        self.timer.timeout.connect(self.receive_data)
        self.timer.start(10)  # 每10ms检查一次
        
        # 添加串口自动扫描定时器
        self.port_scan_timer = QTimer(self)
        self.port_scan_timer.timeout.connect(self.auto_scan_ports)
        self.port_scan_timer.start(1000)  # 每1秒检查一次串口列表
        
        # 保存当前已知的串口列表
        self.known_ports = []
        
        # 编码设置
        self.encoding_list = ["utf-8", "gbk", "ascii", "iso-8859-1"]
        self.current_encoding = "utf-8"
        self.auto_detect_encoding = True
        
        # 添加HEX显示设置
        self.hex_display = False
        self.add_hex_display_control()
        
        # 设置参数缓冲区清理定时器
        self.param_buffer_timer = QTimer(self)
        self.param_buffer_timer.timeout.connect(self.process_parameter_buffer)
        self.param_buffer_timer.start(300)  # 每300ms处理一次缓冲区
        
        # 添加文本显示缓冲区
        self.param_buffer = ""
        self.text_display_buffer = ""
        self.last_text_time = 0
        
        # 设置文本显示缓冲区处理定时器
        self.text_buffer_timer = QTimer(self)
        self.text_buffer_timer.timeout.connect(self.process_text_buffer)
        self.text_buffer_timer.start(100)  # 每100ms处理一次文本缓冲区
        
        # 添加命令类型标记
        self.last_command_type = None
        self.is_reading_station_id = False
        
        # 添加初始值按钮连接
        self.pushButton_16.clicked.connect(self.write_initial_value_zero)    # 置零
        self.pushButton_8.clicked.connect(self.write_initial_value_restore)  # 恢复
        
        # 添加BSL/BSR按钮连接
        self.pushButton_9.clicked.connect(self.write_bsl_value)    # BSL写入按钮
        self.pushButton_14.clicked.connect(self.write_bsr_value)    # BSR写入按钮
        
        # 初始化自动发送定时器
        self.auto_send_timer = QTimer(self)
        self.auto_send_timer.timeout.connect(self.send_data)
        
        # 设置自动发送控件的默认值和信号连接
        self.lineEdit_2.setText("100")  # 默认1000ms
        self.checkBox_2.stateChanged.connect(self.toggle_auto_send_checkbox)
        
        # 设置lineEdit_2只接受数字输入
        self.lineEdit_2.setValidator(QtGui.QIntValidator(100, 60000))
        self.lineEdit_2.textChanged.connect(self.update_auto_send_interval)

        # 在__init__方法中添加按钮连接
        # 在初始化中约第60行左右，与其他按钮连接一起添加:
        #self.pushButton_17.clicked.connect(self.read_modbus_data)

        # 初始化位置曲线图
        self.init_position_chart()

    def init_combobox(self):
        """初始化各个下拉选择框"""
        # 串口号
        self.find_ports()
        
        # 波特率选项
        baud_list = [ "4800", "9600", "19200", "57600", "115200","256000"]
        self.comboBox_Badu.addItems(baud_list)
        self.comboBox_Badu.setCurrentText("115200")  # 默认波特率
        
        # 数据位选项
        data_list = ["5", "6", "7", "8"]
        self.comboBox_Data.addItems(data_list)
        self.comboBox_Data.setCurrentText("8")  # 默认数据位
        
        # 校验位选项
        check_list = ["None", "Odd", "Even", "Mark", "Space"]
        self.comboBox_Check.addItems(check_list)
        
        # 停止位选项
        stop_list = ["1", "1.5", "2"]
        self.comboBox_Stop.addItems(stop_list)
        
        # 协议选项 - 添加0和1两个选项
        protocol_list = ["0", "1"]
        self.comboBox.clear()  # 清除任何现有项
        self.comboBox.addItems(protocol_list)
        self.comboBox.setCurrentIndex(0)  # 默认选择第一个选项(0)
        
        # 站号选项 - 添加1-32范围（当前站号设置）
        station_list = [str(i) for i in range(1, 33)]
        self.comboBox_2.clear()
        self.comboBox_2.addItems(station_list)
        self.comboBox_2.setCurrentIndex(0)  # 默认选择1
        
        # 波特率选项 - 与串口波特率选项相同
        self.comboBox_3.clear()
        self.comboBox_3.addItems(baud_list)
        self.comboBox_3.setCurrentText("115200")  # 默认波特率

        # 在init_combobox方法中添加comboBox_4的初始化
        # 设置comboBox_4的范围为10-500
        self.comboBox_4.clear()
        for i in range(10, 501):
            self.comboBox_4.addItem(str(i))
        # 默认选择中间值
        self.comboBox_4.setCurrentIndex(90)  # 选择255作为默认值

    def get_accurate_timestamp(self):
        """获取准确的时间戳"""
        from datetime import datetime, timedelta
        
        if self.position_read_start_time is not None and self.position_read_count > 0:
            # 使用基准时间 + (计数-1) * 间隔 来计算理论时间
            theoretical_time = self.position_read_start_time + timedelta(
                milliseconds=(self.position_read_count - 1) * self.actual_interval_ms
            )
            return theoretical_time.strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]
        else:
            # 如果不是定时读取，使用当前时间
            return datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]

    def find_ports(self):
        """手动查找可用串口"""
        ports = [port.device for port in list(serial.tools.list_ports.comports())]
        self.known_ports = ports.copy()  # 更新已知串口列表
        
        # 更新下拉框
        self.comboBox_Com.clear()
        for port in ports:
            self.comboBox_Com.addItem(port)
        
        if ports:
            self.comboBox_Com.setCurrentIndex(0)
        else:
            self.show_message_on_text_browser("未找到任何串口设备")

    def open_serial(self):
        """打开或关闭串口"""
        if not self.serial.isOpen():
            # 获取串口号
            port = self.comboBox_Com.currentText()
            if not port:
                self.show_message_on_text_browser("错误: 未找到可用串口")
                return
            
            # 获取波特率
            baud_rate = int(self.comboBox_Badu.currentText())
            
            # 获取数据位
            data_bits = int(self.comboBox_Data.currentText())
            
            # 获取校验位
            parity_dict = {
                "None": serial.PARITY_NONE,
                "Odd": serial.PARITY_ODD,
                "Even": serial.PARITY_EVEN,
                "Mark": serial.PARITY_MARK,
                "Space": serial.PARITY_SPACE
            }
            parity = parity_dict[self.comboBox_Check.currentText()]
            
            # 获取停止位
            stop_dict = {
                "1": serial.STOPBITS_ONE,
                "1.5": serial.STOPBITS_ONE_POINT_FIVE,
                "2": serial.STOPBITS_TWO
            }
            stop_bits = stop_dict[self.comboBox_Stop.currentText()]
            
            try:
                # 配置串口
                self.serial.port = port
                self.serial.baudrate = baud_rate
                self.serial.bytesize = data_bits
                self.serial.parity = parity
                self.serial.stopbits = stop_bits
                self.serial.timeout = 0.1  # 超时设置
                
                # 打开串口
                self.serial.open()
                
                if self.serial.isOpen():
                    self.pushButton_Open.setText("关闭串口")
                    self.comboBox_Com.setEnabled(False)
                    self.comboBox_Badu.setEnabled(False)
                    self.comboBox_Data.setEnabled(False)
                    self.comboBox_Check.setEnabled(False)
                    self.comboBox_Stop.setEnabled(False)
                    self.show_message_on_text_browser(f"成功打开串口 {port}")
            except Exception as e:
                self.show_message_on_text_browser(f"错误: 无法打开串口 - {str(e)}")
        else:
            # 关闭串口
            self.serial.close()
            self.pushButton_Open.setText("打开串口")
            self.comboBox_Com.setEnabled(True)
            self.comboBox_Badu.setEnabled(True)
            self.comboBox_Data.setEnabled(True)
            self.comboBox_Check.setEnabled(True)
            self.comboBox_Stop.setEnabled(True)
            self.show_message_on_text_browser("串口已关闭")
    
    def setup_send_features(self):
        """设置发送相关功能"""
        # 为发送按钮添加快捷键Ctrl+Enter发送
        self.shortcut = QtWidgets.QShortcut(QtGui.QKeySequence("Ctrl+Return"), self)
        self.shortcut.activated.connect(self.send_data)
    
    def toggle_hex_send(self, checked):
        """切换十六进制发送模式"""
        if checked:
            self.textEdit.setPlaceholderText("请输入十六进制数据 (例如: 01 02 03)")
            # 如果当前有文本，尝试转换为HEX格式显示
            current_text = self.textEdit.toPlainText()
            if current_text:
                try:
                    # 将当前文本转换为bytes
                    text_bytes = current_text.encode(self.current_encoding)
                    # 转换为HEX格式
                    hex_text = ' '.join([f"{b:02X}" for b in text_bytes])
                    self.textEdit.setPlainText(hex_text)
                except Exception:
                    # 如果转换失败，清空文本框
                    self.textEdit.clear()
        else:
            self.textEdit.setPlaceholderText("")
            # 如果当前有HEX文本，尝试转换回普通文本
            hex_text = self.textEdit.toPlainText()
            if hex_text:
                try:
                    # 移除空格并转换为bytes
                    hex_bytes = bytes.fromhex(hex_text.replace(" ", ""))
                    # 转换为文本
                    text = hex_bytes.decode(self.current_encoding)
                    self.textEdit.setPlainText(text)
                except Exception:
                    # 如果转换失败，清空文本框
                    self.textEdit.clear()

    def toggle_auto_send(self, checked):
        """切换自动发送模式"""
        if checked:
            self.auto_send_timer.start(self.auto_send_interval)
        else:
            self.auto_send_timer.stop()

    def set_auto_send_interval(self):
        """设置自动发送的时间间隔"""
        interval, ok = QtWidgets.QInputDialog.getInt(
            self, "设置时间间隔", "请输入自动发送的时间间隔(毫秒):",
            self.auto_send_interval, 100, 60000, 100
        )
        if ok:
            self.auto_send_interval = interval
            if self.action_auto_send.isChecked():
                self.auto_send_timer.start(self.auto_send_interval)

    def add_hex_display_control(self):
        """添加十六进制显示控制"""
        # 创建一个水平布局，用于放置复选框
        hex_layout = QtWidgets.QHBoxLayout()
        
        # 创建HEX显示复选框
        self.checkbox_hex_display = QtWidgets.QCheckBox("HEX显示", self)
        self.checkbox_hex_display.setChecked(self.hex_display)
        self.checkbox_hex_display.toggled.connect(self.toggle_hex_display)
        
        # 创建HEX发送复选框
        self.checkbox_hex_send = QtWidgets.QCheckBox("HEX发送", self)
        self.checkbox_hex_send.setChecked(False)  # 默认不勾选
        self.checkbox_hex_send.toggled.connect(self.toggle_hex_send)
        
        # 将两个复选框添加到布局
        hex_layout.addWidget(self.checkbox_hex_display)
        hex_layout.addWidget(self.checkbox_hex_send)
        hex_layout.addStretch()
        
        # 设置文本浏览器的属性
        self.textBrowser.setLineWrapMode(QtWidgets.QTextBrowser.NoWrap)  # 关闭自动换行
        self.textBrowser.setHorizontalScrollBarPolicy(QtCore.Qt.ScrollBarAlwaysOn)  # 始终显示水平滚动条
        self.textBrowser.setVerticalScrollBarPolicy(QtCore.Qt.ScrollBarAsNeeded)  # 需要时显示垂直滚动条
        
        # 设置文本浏览器的大小策略
        sizePolicy = QtWidgets.QSizePolicy(
            QtWidgets.QSizePolicy.Expanding,
            QtWidgets.QSizePolicy.Expanding
        )
        sizePolicy.setHorizontalStretch(1)
        sizePolicy.setVerticalStretch(1)
        self.textBrowser.setSizePolicy(sizePolicy)
        
        # 在textBrowser前添加hex_layout
        index = self.verticalLayout_3.indexOf(self.textBrowser)
        self.verticalLayout_3.insertLayout(index, hex_layout)

    def toggle_hex_display(self, checked):
        """切换十六进制显示模式"""
        self.hex_display = checked
        # 如果切换为HEX显示，可以提供反馈信息
        if checked:
            self.show_message_on_text_browser("已切换为HEX显示模式")
        else:
            self.show_message_on_text_browser("已切换为文本显示模式")

    def send_data(self):
        """发送数据"""
        if self.serial.isOpen():
            # 获取要发送的数据
            input_text = self.textEdit.toPlainText()
            if not input_text:
                self.show_message_on_text_browser("错误: 没有要发送的数据")
                return
            
            try:
                # 根据是否使用十六进制发送处理数据
                if self.checkbox_hex_send.isChecked():
                    # 处理十六进制输入
                    hex_str = input_text.replace(" ", "").replace("\n", "")
                    try:
                        # 验证输入的十六进制字符串
                        if not all(c in '0123456789ABCDEFabcdef' for c in hex_str):
                            raise ValueError("包含非法的十六进制字符")
                        if len(hex_str) % 2 != 0:
                            raise ValueError("十六进制字符串长度必须为偶数")
                        
                        send_bytes = bytes.fromhex(hex_str)
                    except ValueError as e:
                        self.show_message_on_text_browser(f"错误: {str(e)}")
                        return
                    
                    # 显示发送的十六进制数据
                    hex_display = ' '.join([f"{b:02X}" for b in send_bytes])
                    self.show_message_on_text_browser(f"发送 (HEX): {hex_display}")
                else:
                    # 使用当前编码发送文本
                    send_bytes = input_text.encode(self.current_encoding)
                    self.show_message_on_text_browser(f"发送: {input_text}")
                
                # 发送数据
                self.serial.write(send_bytes)
                
            except Exception as e:
                self.show_message_on_text_browser(f"发送错误: {str(e)}")
        else:
            self.show_message_on_text_browser("错误: 串口未打开")

    def receive_data(self):
        """接收串口数据并处理"""
        try:
            if not self.serial or not self.serial.is_open:
                return
                
            if self.serial.in_waiting:
                # 添加适当的延迟，确保当前批次数据完整接收
                time.sleep(0.02)
                
                # 读取数据
                received_data = self.serial.read(self.serial.in_waiting)
                
                if not received_data:
                    return
                    
                # 记录接收时间
                current_time = QtCore.QTime.currentTime().msecsSinceStartOfDay()
                self.last_receive_time = current_time
                
                # 如果接收的是文本数据
                try:
                    # 解码接收的数据
                    text_data = received_data.decode('utf-8', errors='ignore')
                    
                    # 确保累积到参数缓冲区
                    #self.text_display_buffer += text_data
                    
                    # 添加到文本显示缓冲区，但添加前先检查是否重复
                    # 这里添加一个重复检测
                    # if not hasattr(self, 'last_received_text') or self.last_received_text != text_data:
                    #     self.text_display_buffer += text_data
                    #     self.last_received_text = text_data
                    
                    # 检查ID信息
                    if 'ID=' in text_data:
                        id_match = re.search(r'ID=(\d+)', text_data)
                        if id_match:
                            first_station_id = int(id_match.group(1))
                            self.current_device_station_id = first_station_id
                            self.show_message_on_text_browser(f"[自动检测] 检测到设备当前站号: {first_station_id}")
                    
                    # 记录当前时间
                    current_time = QtCore.QTime.currentTime().msecsSinceStartOfDay()
                    
                    # 显示HEX格式数据
                    hex_str = ' '.join([f"{b:02X}" for b in received_data])
                    
                    # 检查是否是modbus数据读取响应
                    if hasattr(self, 'last_command_type') and self.last_command_type == 'modbus_data':
                        # 检查响应格式是否正确（站号 + 03 + 04 + 数据）
                        if len(received_data) >= 9 and received_data[1] == 0x03 and received_data[2] == 0x04:
                            self.process_modbus_data_response(received_data)
                            # 清除命令类型标记
                            self.last_command_type = None
                            return  # 关键：处理完立即返回，不继续执行后面的位置数据处理逻辑
                    
                    # 检查是否是协议读取响应
                    if self.last_command_type == 'read_protocol':
                        # 检查响应格式是否正确（站号 + 03 + 04 + 数据）
                        if len(received_data) >= 9 and received_data[1] == 0x03 and received_data[2] == 0x04:
                            # 检查是哪种协议响应
                            if received_data[3] == 0x00 and received_data[4] == 0x00 and received_data[5] == 0x00 and received_data[6] == 0x00:
                                # 协议0
                                self.comboBox.setCurrentIndex(0)
                                self.show_message_on_text_browser("读取到协议: 0")
                            elif received_data[3] == 0x00 and received_data[4] == 0x01 and received_data[5] == 0x00 and received_data[6] == 0x00:
                                # 协议1
                                self.comboBox.setCurrentIndex(1)
                                self.show_message_on_text_browser("读取到协议: 1")
                            else:
                                self.show_message_on_text_browser("收到未知的协议响应")
                            
                            # 清除命令类型标记
                            self.last_command_type = None
                            return  # 在这里添加return，处理完协议响应后直接返回
                    
                    # 检查是否是站号读取响应
                    elif self.last_command_type == 'read_station_id':
                        # 检查响应格式是否正确（站号 + 03 + 02 + 数据）
                        if len(received_data) >= 5 and received_data[1] == 0x03 and received_data[2] == 0x02:
                            # 从响应中提取站号（第5个字节）
                            station_id = received_data[6]
                            # 更新当前设备站号变量
                            self.current_device_station_id = station_id
                            # 更新UI显示（comboBox_2的索引从0开始，站号从1开始）
                            self.comboBox_2.setCurrentIndex(station_id - 1)
                            self.show_message_on_text_browser(f"读取到站号: {station_id}")
                            # 清除命令类型标记
                            self.last_command_type = None
                            return
                    # 错误响应检测 (通用Modbus错误格式：站号 + 功能码+0x80 + 错误码 + CRC)
                    if len(received_data) >= 5 and (received_data[1] & 0x80) == 0x80:
                        # 这是一个错误响应
                        error_code = received_data[2] if len(received_data) > 2 else 0
                        
                        # 但我们只在位置相关命令时才作为位置错误处理
                        if hasattr(self, 'last_command_type') and self.last_command_type == 'read_position':
                            self.set_position_status_error(error_code, f"设备返回错误代码: {error_code}")
                        #else:
                            # 其他命令的错误只记录不改变界面
                            #self.show_message_on_text_browser(f"命令执行错误: 代码={error_code}")
                        
                        # 命令已处理，重置命令类型
                        self.last_command_type = None
                        return                   
                    # 根据显示模式决定如何显示接收数据
                    if self.checkbox_hex_display.isChecked():
                        # HEX显示模式下只显示HEX格式
                        self.show_message_on_text_browser(f"{hex_str}")
                    else:
                        # 文本显示模式（保留原有逻辑）
                        # 定义编码优先级
                        encodings = [
                            ('GBK', 'gbk'),      # 中文优先
                            ('UTF-8', 'utf-8'),   # 通用编码
                            ('ASCII', 'ascii'),   # ASCII编码
                            ('GB2312', 'gb2312')  # 简体中文
                        ]
                        
                        # 尝试按优先级解码
                        decoded_text = None
                        used_encoding = None
                        
                        for encoding_name, encoding in encodings:
                            try:
                                # 尝试解码
                                test_decoded = received_data.decode(encoding, errors='ignore')
                                # 检查解码结果是否有效（不全是空格或控制字符）
                                filtered_text = ''.join(char for char in test_decoded 
                                                      if char == '\n' or char == '\r' 
                                                      or (32 <= ord(char) <= 126) 
                                                      or (ord(char) > 256))
                                
                                # 检查解码质量（有效字符比例）
                                if filtered_text.strip() and len(filtered_text) >= len(test_decoded) * 0.5:
                                    decoded_text = filtered_text
                                    used_encoding = encoding_name
                                    break
                            except Exception:
                                continue
                        
                        # 显示结果
                        if decoded_text and used_encoding:
                            # 不再直接显示，而是添加到文本缓冲区（新增代码）
                            self.text_display_buffer += decoded_text
                            self.last_text_time = current_time
                            
                            # 更新参数缓冲区（保留原有代码）
                            if "ID=" in decoded_text or "Baud=" in decoded_text or "Proc=" in decoded_text:
                                self.parameter_buffer += decoded_text
                                self.last_receive_time = current_time
                        else:
                            # 如果无法解码，显示HEX格式
                            self.show_message_on_text_browser(hex_str)
                            # 尝试从HEX数据中解析参数信息
                            try:
                                ascii_text = bytes([b for b in received_data if 32 <= b <= 126]).decode('ascii')
                                if "ID=" in ascii_text or "Baud=" in ascii_text or "Proc=" in ascii_text:
                                    self.parameter_buffer += ascii_text
                                    self.last_receive_time = current_time
                            except Exception:
                                pass
                    
                    # 无论是什么显示模式，都尝试解析Modbus数据
                    try:
                        # 检查是否是Modbus响应
                        if len(received_data) >= 3:
                            # 检查最近一次发送的命令类型
                            if hasattr(self, 'last_command_type'):
                                # 如果是参数相关的命令，不要当作位置数据处理
                                if self.last_command_type in ['read_baudrate', 'write_baudrate', 
                                                            'read_station_id', 'write_station_id',
                                                            'read_protocol', 'write_protocol']:
                                    return
                            
                            # 检查是否是异常响应（功能码最高位为1，表示出错）
                            #if received_data[1] & 0x80 == 0x80 and len(received_data) >= 5:
                            if received_data[1] & 0x80 == 0x80 :   
                                # 这是一个异常响应，提取错误代码
                                error_code = received_data[2]
                                error_message = self.get_error_message(error_code)
                                self.show_message_on_text_browser(f"接收到错误响应: 错误代码={error_code}, 说明={error_message}")
                                
                                # 在右侧错误框中显示错误信息
                                self.set_position_status_error(error_code, error_message)
                                
                                # 在位置区域显示错误信息
                                self.textBrowser_2.clear()
                                self.textBrowser_2.append(f"错误: {error_message}\nHEX: {hex_str}")
                                
                                # 切换到位置标签页
                                self.tabWidget.setCurrentIndex(2)
                                
                                # 记录错误信息
                                timestamp = self.get_accurate_timestamp()
                                self.position_data_list.append([timestamp, f"错误({error_code})", hex_str])
                                
                                # 强制更新UI
                                QtWidgets.QApplication.processEvents()
                                return
                            
                            # 检查是否是正常的位置读取响应（01 03 04...）
                            elif received_data[1] == 0x03 and received_data[2] == 0x04 and len(received_data) >= 9:
                                # 提取位置数据（第4-7字节）
                                position_data = received_data[3:7]
                                
                                # 检查position_data[0]的最高位是否为1
                                if position_data[0] & 0x80:
                                    # 这是错误位置信息
                                    reordered_data = bytes([
                                        position_data[0],  
                                        position_data[1],                            
                                        position_data[2],  
                                        position_data[3]   
                                    ])
                                    
                                    position = int.from_bytes(reordered_data, byteorder='big', signed=True)
                                    hex_str_position = ' '.join([f"{b:02X}" for b in reordered_data])
                                    
                                    # 显示错误位置数据
                                    display_text = f"HEX: {hex_str_position}\n当前位置: {position}\n状态: 错误位置"
                                    
                                    # 更新位置显示
                                    self.textBrowser_2.clear()
                                    self.textBrowser_2.append(display_text)
                                    
                                    # 设置错误状态
                                    self.set_position_status_error(0, "位置数据错误")
                                    
                                    # 切换到位置标签页
                                    self.tabWidget.setCurrentIndex(2)
                                else:
                                    # 正常位置数据，重新排列字节顺序
                                    reordered_data = bytes([
                                        position_data[0],  # 00
                                        position_data[1],  # F1                             
                                        position_data[2],  # B1
                                        position_data[3]   # 30
                                    ])
                                    
                                    # 先转换为无符号整数
                                    raw_position = int.from_bytes(reordered_data, byteorder='big', signed=False)
                                    
                                    # 根据规则判断正负并计算实际位置值
                                    if raw_position >= 0x60000000:
                                        # 负数情况: 实际值 = 0x80000000 - 原始值
                                        position = -(0x80000000 - raw_position)
                                    else:
                                        # 正数情况: 直接使用原始值
                                        position = raw_position
                                    
                                    # 显示重排序后的十六进制数据和转换后的十进制值
                                    hex_str_position_original = ' '.join([f"{b:02X}" for b in position_data])
                                    hex_str_position_reordered = ' '.join([f"{b:02X}" for b in reordered_data])
                                    display_text = f"HEX: {hex_str_position_reordered}\n当前位置: {position}"
                                    
                                    # 更新位置曲线图
                                    if HAS_POSITION_CHART and hasattr(self, 'position_chart'):
                                        try:
                                            self.position_chart.add_data_point(position)
                                        except Exception as e:
                                            print(f"更新位置曲线图错误: {str(e)}")
                                    
                                    # 更新位置显示
                                    self.textBrowser_2.clear()
                                    self.textBrowser_2.append(display_text)
                                    
                                    # 设置为正常状态
                                    self.set_position_status_normal()
                                    
                                    # 切换到位置标签页
                                    self.tabWidget.setCurrentIndex(2)
                                
                                # 存储位置数据用于导出
                                timestamp = self.get_accurate_timestamp()
                                orig_hex = ' '.join([f"{b:02X}" for b in received_data])
                                self.position_data_list.append([timestamp, position, orig_hex])
                                
                                # 强制更新UI
                                self.textBrowser_2.update()
                                QtWidgets.QApplication.processEvents()
                                
                                # 注释：位置曲线图更新已在上面的代码中处理，避免重复调用
                    except Exception as e:
                        print(f"位置数据解析错误: {str(e)}")
                        
                except Exception as e:
                    self.show_message_on_text_browser(f"我错误: {str(e)}")
                # 在receive_data方法中，添加对协议响应的处理（在检查站号读取响应的代码块后面添加）
                # 检查是否是协议读取响应
                if self.last_command_type == 'read_protocol':
                    # 检查响应格式是否正确（站号 + 03 + 04 + 数据）
                    if len(received_data) >= 9 and received_data[1] == 0x03 and received_data[2] == 0x04:
                        # 检查是哪种协议响应
                        if received_data[3] == 0x00 and received_data[4] == 0x00 and received_data[5] == 0x00 and received_data[6] == 0x00:
                            # 协议0
                            self.comboBox.setCurrentIndex(0)
                            self.show_message_on_text_browser("读取到协议: 0")
                        elif received_data[3] == 0x00 and received_data[4] == 0x01 and received_data[5] == 0x00 and received_data[6] == 0x00:
                            # 协议1
                            self.comboBox.setCurrentIndex(1)
                            self.show_message_on_text_browser("读取到协议: 1")
                        else:
                            self.show_message_on_text_browser("收到未知的协议响应")
                        
                        # 清除命令类型标记
                        self.last_command_type = None
                        return
                    
            if not self.serial.isOpen():
                return
                     
                
        except Exception as e:
            self.show_message_on_text_browser(f"接收数据时出错: {str(e)}")  
                    # 添加命令类型检查，防止误判
            if hasattr(self, 'last_command_type') and self.last_command_type == 'read_position':
                     # 只有当发送了读取位置命令时才处理位置数据
                    self._process_position_data(received_data)
            else:
                        # 其他数据按一般数据处理
                        self._process_general_data(received_data)

    def _process_position_data(self, data):
        """处理位置数据，加强数据格式验证"""
        try:
            # 严格的位置数据格式验证
            is_valid_position_data = False
            
            # 验证格式：站号(1字节) + 功能码0x03(1字节) + 字节数0x04(1字节) + 数据(4字节) + CRC(2字节)
            if len(data) == 9 and data[1] == 0x03 and data[2] == 0x04:
                # 验证这是从正确的寄存器读取的位置数据
                # 通常位置数据来自特定寄存器，可以增加这方面的验证
                is_valid_position_data = True
            
            if is_valid_position_data:
                # 这是一个有效的位置响应
                # 继续处理位置数据...
                
                # 只有当命令类型是read_position时才切换页面
                if hasattr(self, 'last_command_type') and self.last_command_type == 'read_position':
                    self.tabWidget.setCurrentIndex(2)  # 切换到位置页面
            else:
                # 数据不符合位置响应格式，作为普通数据处理
                self._process_general_data(data)
                
        except Exception as e:
            self.show_message_on_text_browser(f"处理位置数据出错: {str(e)}")

    def _process_general_data(self, data):
        """处理一般串口数据"""
        # 将数据显示在接收区域，不跳转页面
        if self.hex_display:
            hex_str = ' '.join([f'{b:02X}' for b in data])
            self.show_message_on_text_browser(hex_str)
        else:
            try:
                text = data.decode('utf-8', errors='replace')
                self.show_message_on_text_browser(text)
            except Exception:
                # 如果无法解码为文本，显示十六进制
                hex_str = ' '.join([f'{b:02X}' for b in data])
                self.show_message_on_text_browser(f"[HEX] {hex_str}")
        
        # 尝试解析参数，但不改变页面
        self.parameter_buffer += data.decode('utf-8', errors='replace')
        self.process_parameter_buffer()
    

    def safe_close_serial(self):
        """安全关闭串口，确保资源被正确释放"""
        try:
            if hasattr(self, 'serial') and self.serial.isOpen():
                # 停止相关操作
                if hasattr(self, 'timer'):
                    self.timer.stop()
                    
                # 关闭串口
                self.serial.close()
                
                # 重置UI
                self.pushButton_Open.setText("打开串口")
                self.comboBox_Com.setEnabled(True)
                self.comboBox_Badu.setEnabled(True)
                self.comboBox_Data.setEnabled(True)
                self.comboBox_Check.setEnabled(True)
                self.comboBox_Stop.setEnabled(True)
        except Exception as e:
            self.show_message_on_text_browser(f"关闭串口时出错: {str(e)}")
        finally:
            # 确保计时器重新启动以继续监控串口
            if hasattr(self, 'timer'):
                self.timer.start(10)

    def try_reconnect_serial(self):
        """尝试重新连接之前的串口"""
        if hasattr(self, 'serial') and not self.serial.isOpen():
            port = self.serial.port
            if port:
                try:
                    # 重新创建串口对象以避免任何残留状态
                    self.serial = serial.Serial()
                    self.serial.port = port
                    self.serial.baudrate = self.serial.baudrate
                    self.serial.bytesize = self.serial.bytesize
                    self.serial.parity = self.serial.parity
                    self.serial.stopbits = self.serial.stopbits
                    self.serial.timeout = 0.1
                    
                    # 尝试打开
                    self.serial.open()
                     
                    if self.serial.isOpen():
                        self.pushButton_Open.setText("关闭串口")
                        self.comboBox_Com.setEnabled(False)
                        self.comboBox_Badu.setEnabled(False)
                        self.comboBox_Data.setEnabled(False)
                        self.comboBox_Check.setEnabled(False)
                        self.comboBox_Stop.setEnabled(False)
                        self.show_message_on_text_browser(f"已重新连接串口 {port}")
                except Exception as e:
                    self.show_message_on_text_browser(f"重新连接失败: {str(e)}")

    def clear_receive(self):
        """清除接收区内容和位置数据"""
        self.textBrowser.clear()  # 清除显示区域
        self.textBrowser_7.clear()
        # 添加清除位置数据列表的代码
        if hasattr(self, 'position_data_list'):
            self.position_data_list = []
            #self.show_message_on_text_browser("位置数据已清除")
    
        # 清除位置曲线图
        if HAS_POSITION_CHART and hasattr(self, 'position_chart'):
            self.position_chart.clear()
    
    def show_message_on_text_browser(self, message):
        """在文本浏览器中显示消息"""
        try:
            cursor = self.textBrowser.textCursor()
            cursor.movePosition(QtGui.QTextCursor.End)
            
            # 如果不是第一行，先插入换行符
            if not self.textBrowser.toPlainText().strip() == "":
                cursor.insertText('\n')
            
            # 插入消息
            cursor.insertText(message)
            
            # 滚动到最新内容
            self.textBrowser.setTextCursor(cursor)
            self.textBrowser.ensureCursorVisible()
            
            # 强制更新UI
            QtWidgets.QApplication.processEvents()
        except Exception as e:
            print(f"显示消息错误: {str(e)}")

    def closeEvent(self, event):
        """关闭窗口时确保串口已关闭"""
        # 停止所有计时器
        if hasattr(self, 'timer'):
            self.timer.stop()
        if hasattr(self, 'port_scan_timer'):
            self.port_scan_timer.stop()
        if hasattr(self, 'auto_send_timer'):
            self.auto_send_timer.stop()
        
        # 确保串口已关闭
        self.safe_close_serial()
        
        # 添加一点延迟确保关闭完成
        QtCore.QThread.msleep(100)
        
        event.accept()

    def auto_scan_ports(self):
        """自动扫描串口列表，检测变化"""
        ports = [port.device for port in list(serial.tools.list_ports.comports())]
        
        # 检查是否有变化
        if set(ports) != set(self.known_ports):
            # 保存当前选择的端口
            current_port = self.comboBox_Com.currentText()
            
            # 更新串口下拉框
            self.comboBox_Com.clear()
            for port in ports:
                self.comboBox_Com.addItem(port)
            
            # 如果之前选择的端口仍然存在，恢复选择
            if current_port and current_port in ports:
                self.comboBox_Com.setCurrentText(current_port)
            elif ports:
                self.comboBox_Com.setCurrentIndex(0)
            
            # 获取新增或移除的端口信息 (仅用于内部逻辑)
            added_ports = [p for p in ports if p not in self.known_ports]
            removed_ports = [p for p in self.known_ports if p not in ports]
            
            # 移除新增串口通知
            # if added_ports:
            #     self.show_message_on_text_browser(f"发现新串口: {', '.join(added_ports)}")
            
            # 移除串口断开通知，但保留对当前使用串口的断开警告
            if removed_ports:
                # 移除一般断开通知
                # self.show_message_on_text_browser(f"串口已断开: {', '.join(removed_ports)}")
                
                # 保留对当前使用串口断开的重要警告
                if self.serial.isOpen() and self.serial.port in removed_ports:
                    self.show_message_on_text_browser(f"警告: 当前使用的串口 {self.serial.port} 已断开连接")
            
            # 更新已知串口列表
            self.known_ports = ports.copy()

    def try_parse_parameters(self, text):
        """尝试从文本中解析参数信息"""
        try:
            # 检查文本中是否包含关键参数
            if "ID=" not in text and "Baud=" not in text and "Proc=" not in text:
                return False
                
            # 解析站号参数
            id_match = re.search(r'ID=(\d+)', text)
            if id_match:
                station_id = int(id_match.group(1))
                self.current_device_station_id = station_id
                self.comboBox_2.setCurrentIndex(station_id - 1)
                #self.show_message_on_text_browser(f"检测到ID值: {station_id}")
                
            # 解析波特率参数
            baud_match = re.search(r'Baud=(\d+)', text)
            if baud_match:
                baud_rate = int(baud_match.group(1))
                # 查找匹配的波特率索引
                baud_index = -1
                for i in range(self.comboBox_3.count()):
                    if int(self.comboBox_3.itemText(i)) == baud_rate:
                        baud_index = i
                        break
                        
                if baud_index >= 0:
                    self.comboBox_3.setCurrentIndex(baud_index)
                    #self.show_message_on_text_browser(f"检测到波特率: {baud_rate}")
                
            # 解析协议参数
            proc_match = re.search(r'Proc=(\d+)', text)
            if proc_match:
                protocol = int(proc_match.group(1))
                if protocol >= 0 and protocol < self.comboBox.count():
                    self.comboBox.setCurrentIndex(protocol)
                    #self.show_message_on_text_browser(f"检测到协议: {protocol}")
                    
            # 解析左限值参数
            bsl_match = re.search(r'BSL=(\d+)', text)
            if bsl_match:
                bsl_value = int(bsl_match.group(1))
                self.lineEdit_4.setText(str(bsl_value))
                #self.show_message_on_text_browser(f"检测到BSL值: {bsl_value}")
                
            # 解析右限值参数
            bsr_match = re.search(r'BSR=(\d+)', text)
            if bsr_match:
                bsr_value = int(bsr_match.group(1))
                self.lineEdit_7.setText(str(bsr_value))
                
            # 解析BSB参数
            bsb_match = re.search(r'BSB=(\d+)', text)
            if bsb_match:
                bsb_value = int(bsb_match.group(1))
                self.lineEdit_5.setText(str(bsb_value))
                
            # 解析Z值参数并填入lineEdit
            z_match = re.search(r'Z=(\d+)', text)
            if z_match:
                z_value = int(z_match.group(1))
                self.lineEdit.setText(str(z_value))
                #self.show_message_on_text_browser(f"检测到Z值: {z_value}")
                
            return True
        except Exception as e:
            self.show_message_on_text_browser(f"参数解析错误: {str(e)}")
            return False

    def calibrate(self):
        """校准功能"""
        if not self.serial.isOpen():
            self.show_message_on_text_browser("错误: 请先打开串口")
            return
        
        try:
            # 清空textBrowser_7，准备接收新的校准结果
            self.textBrowser_7.clear()
            
            # 获取当前实际站号（已存储的）
            if hasattr(self, 'current_device_station_id'):
                current_station_id = self.current_device_station_id
            
            # 获取comboBox_4中选择的校准值
            calibration_value = int(self.comboBox_4.currentText())
            
            # 将校准值转换为两字节的十六进制数据（大端序）
            high_byte = (calibration_value >> 8) & 0xFF  # 高字节
            low_byte = calibration_value & 0xFF  # 低字节
            
            # 构建Modbus-RTU校准指令（不含CRC）
            command_without_crc = bytes([
                current_station_id,  # 站号（动态）
                0x10,        # 功能码：写多个寄存器
                0x20, 0x10,  # 起始地址：0x2010
                0x00, 0x01,  # 寄存器数量：1个
                0x02,        # 字节数：2个字节
                high_byte, low_byte  # 数据：来自comboBox_4的值
            ])
            
            # 计算CRC
            crc = self.calculate_crc(command_without_crc)
            
            # 构建完整的指令（包含CRC）
            calibrate_command = command_without_crc + crc
            
            # 发送校准指令
            self.serial.write(calibrate_command)
            
            # 显示发送的指令（HEX格式）
            hex_display = ' '.join([f"{b:02X}" for b in calibrate_command])
            self.show_message_on_text_browser(f"校准: {hex_display}，校准值: {calibration_value}")
            
            # 等待并读取响应（Modbus-RTU响应通常需要等待一段时间）
            QtCore.QTimer.singleShot(100, self.read_calibrate_response)
            
        except Exception as e:
            self.show_message_on_text_browser(f"校准错误: {str(e)}")

    def read_calibrate_response(self):
        """读取校准命令的响应"""
        try:
            if self.serial.in_waiting:
                # 读取响应数据
                response = self.serial.read(self.serial.in_waiting)
                if response:
                    # 显示响应数据（HEX格式）
                    hex_response = ' '.join([f"{b:02X}" for b in response])
                    self.show_message_on_text_browser(f"收到校准响应: {hex_response}")
                    
                    # 获取当前站号
                    station_id = int(self.comboBox_2.currentText())
                    
                    # 构建预期的响应（不含CRC）
                    expected_without_crc = bytes([
                        station_id,  # 站号（动态）
                        0x10,        # 功能码
                        0x20, 0x10,  # 起始地址
                        0x00, 0x01   # 寄存器数量
                    ])
                    
                    # 计算CRC
                    expected_crc = self.calculate_crc(expected_without_crc)
                    
                    # 完整的预期响应
                    expected_response = expected_without_crc + expected_crc
                    
                    if response == expected_response:
                        self.show_message_on_text_browser("校准成功")
                    else:
                        self.show_message_on_text_browser("警告: 校准响应与预期不符")
                else:
                    self.show_message_on_text_browser("警告: 未收到校准响应")
            
        except Exception as e:
            self.show_message_on_text_browser(f"读取校准响应错误: {str(e)}")

    def calculate_crc(self, data):
        """计算Modbus CRC-16校验码"""
        crc = 0xFFFF
        for byte in data:
            crc ^= byte
            for _ in range(8):
                if crc & 0x0001:
                    crc = (crc >> 1) ^ 0xA001
                else:
                    crc = crc >> 1
        # 返回CRC的低字节和高字节
        return bytes([crc & 0xFF, crc >> 8])

    def read_position(self):
        """读取位置功能"""
        if not self.serial.isOpen():
            self.show_message_on_text_browser("错误: 请先打开串口")
            return
        
        try:
            # 如果是定时读取，增加计数
            if self.position_read_start_time is not None:
                self.position_read_count += 1
            
            # 设置当前命令类型
            self.last_command_type = 'read_position'
            
            # 获取当前站号
            station_id = self.get_current_station_id()
            if station_id is None:
                self.show_message_on_text_browser("错误: 无法获取设备站号")
                return
            
            # 构建命令
            cmd = bytes([station_id, 0x03, 0x00, 0x0C, 0x00, 0x02])
            crc = self.calculate_crc(cmd)
            cmd += crc
            
            # 发送并记录
            self.serial.write(cmd)
            hex_str = ' '.join([f'{b:02X}' for b in cmd])
            self.show_message_on_text_browser(f"发送读取位置命令: {hex_str}")
        except Exception as e:
            self.show_message_on_text_browser(f"读取位置错误: {str(e)}")

    def update_position_display(self, text):
        """更新位置显示"""
        try:
            # 更新位置显示
            self.textBrowser_2.clear()
            self.textBrowser_2.append(text)
            
            # 切换到位置标签页
            self.tabWidget.setCurrentIndex(2)
            
            # 强制更新UI
            self.textBrowser_2.update()
            QtWidgets.QApplication.processEvents()
        except Exception as e:
            self.show_message_on_text_browser(f"更新显示错误: {str(e)}")

    def toggle_timed_position_reading(self, checked):
        """切换定时读取位置功能"""
        if checked:
            # 获取用户设置的时间间隔
            try:
                interval = int(self.lineEdit_3.text())
                if interval < 10:  # 最小间隔10ms
                    interval = 10
                    self.lineEdit_3.setText("10")
                
                # 记录开始时间和重置计数
                from datetime import datetime
                self.position_read_start_time = datetime.now()
                self.position_read_count = 0
                self.actual_interval_ms = interval
                
                # 开始定时器
                self.position_timer.start(interval)
                
                # 设置位置图表的时间间隔
                if HAS_POSITION_CHART and hasattr(self, 'position_chart'):
                    self.position_chart.set_interval(interval)
                
                self.show_message_on_text_browser(f"已开启定时读取位置，间隔: {interval}ms")
            except ValueError:
                # 默认使用100ms
                self.lineEdit_3.setText("100")
                from datetime import datetime
                self.position_read_start_time = datetime.now()
                self.position_read_count = 0
                self.actual_interval_ms = 100
                self.position_timer.start(100)
                
                # 设置位置图表的默认时间间隔
                if HAS_POSITION_CHART and hasattr(self, 'position_chart'):
                    self.position_chart.set_interval(100)
                
                self.show_message_on_text_browser("开启定时读取，默认间隔: 100ms")
        else:
            # 停止定时器
            self.position_timer.stop()
            self.position_read_start_time = None
            self.position_read_count = 0
            self.show_message_on_text_browser("已停止定时读取位置")

    def export_position_data(self):
        """导出位置数据到Excel文件"""
        # 检查是否有数据可导出
        has_list_data = hasattr(self, 'position_data_list') and self.position_data_list
        has_chart_data = HAS_POSITION_CHART and hasattr(self, 'position_chart')
        
        if not has_list_data and not has_chart_data:
            self.show_message_on_text_browser("没有可导出的位置数据")
            return
        
        try:
            # 尝试导入pandas，用于创建Excel文件
            import pandas as pd
            from datetime import datetime
            
            # 获取保存文件路径
            file_name = f"位置数据_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
            file_path, _ = QtWidgets.QFileDialog.getSaveFileName(
                self, "保存位置数据", file_name, "Excel文件 (*.xlsx)"
            )
            
            if not file_path:
                return  # 用户取消了保存
            
            # 创建Excel写入器
            with pd.ExcelWriter(file_path) as writer:
                # 导出列表数据
                if has_list_data:
                    df_list = pd.DataFrame(self.position_data_list, columns=["时间", "位置值", "HEX数据"])
                    df_list.to_excel(writer, sheet_name="位置数据列表", index=False)
                
                # 导出图表数据
                if has_chart_data:
                    times, positions = self.position_chart.get_data()
                    if times and positions:
                        df_chart = pd.DataFrame({
                            "时间(秒)": times,
                            "位置值": positions
                        })
                        df_chart.to_excel(writer, sheet_name="位置曲线数据", index=False)
            
            self.show_message_on_text_browser(f"位置数据已导出到: {file_path}")
        except ImportError:
            # 如果没有pandas，尝试使用csv模块
            import csv
            from datetime import datetime
            
            file_name = f"位置数据_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
            file_path, _ = QtWidgets.QFileDialog.getSaveFileName(
                self, "保存位置数据", file_name, "CSV文件 (*.csv)"
            )
            
            if not file_path:
                return  # 用户取消了保存
            
            with open(file_path, 'w', newline='') as file:
                writer = csv.writer(file)
                writer.writerow(["时间", "位置值", "HEX数据"])
                for row in self.position_data_list:
                    writer.writerow(row)
            
            self.show_message_on_text_browser(f"位置数据已导出到CSV文件: {file_path}")
        except Exception as e:
            self.show_message_on_text_browser(f"导出数据时出错: {str(e)}")

    def set_position_status_normal(self):
        """设置位置状态为正常"""
        # 设置绿色背景
        self.lineEdit_6.setStyleSheet("background-color:rgb(224, 247, 197);")  # 浅绿色背景
        self.lineEdit_6.setText("")  # 清空错误信息

    #def set_position_status_error(self, error_code, message):
       # """设置位置状态为错误"""
        # 设置红色背景
        #self.lineEdit_6.setStyleSheet("background-color: #ffcccc;")  # 浅红色背景
        #self.lineEdit_6.setText(f"错误代码: {error_code} - {message}")

    def get_error_message(self, error_code):
        """根据错误代码获取错误信息"""
        error_messages = {
            1: "功能码错误",
            2: "地址错误",
            3: "数据值错误",
            4: "设备故障",
            5: "确认延时",
            6: "设备忙",
            7: "操作失败",
            8: "存储错误",
            10: "网关路径不可用",
            11: "网关目标设备响应失败"
        }
        return error_messages.get(error_code, f"未知错误({error_code})")

    def write_station_id(self):
        """写入新的站号到设备"""
        if not self.serial.isOpen():
            self.show_message_on_text_browser("错误: 请先打开串口")
            return
        
        try:
            # 获取当前实际站号（已存储的）
            if hasattr(self, 'current_device_station_id'):
                current_station_id = self.current_device_station_id
         
            
            # 从comboBox_2获取新站号（用户已选择）
            new_station_id = int(self.comboBox_2.currentText())
            
            self.show_message_on_text_browser(f"站号 {current_station_id} 修改: {new_station_id}")
            
            # 构建Modbus-RTU写站号指令（不含CRC）
            command_without_crc = bytes([
                current_station_id,  # 当前从机号（用于通信）
                0x10,                # 功能码：写多个寄存器
                0x20, 0x05,          # 起始地址：0x2005
                0x00, 0x02,          # 寄存器数量：2个
                0x04,                # 字节数：4个字节
                0x00, new_station_id,# 写数据第1、2字节：高字节00，低字节为新站号
                0x00, 0x00           # 写数据第3、4字节：全为0
            ])
            
            # 计算CRC
            crc = self.calculate_crc(command_without_crc)
            
            # 构建完整的指令（包含CRC）
            write_command = command_without_crc + crc
            
            # 发送写站号指令
            self.serial.write(write_command)
            
            # 显示发送的指令（HEX格式）
            hex_display = ' '.join([f"{b:02X}" for b in write_command])
            self.show_message_on_text_browser(f"写站号: {hex_display}")
            
            # 更新当前设备站号的存储值
            self.current_device_station_id = new_station_id
            
            # 等待并读取响应（Modbus-RTU响应通常需要等待一段时间）
            QtCore.QTimer.singleShot(100, lambda: self.read_write_station_response(new_station_id))
            
        except Exception as e:
            self.show_message_on_text_browser(f"写站号错误: {str(e)}")

    def read_write_station_response(self, new_station_id):
        """读取写站号命令的响应"""
        try:
            # 等待接收数据
            time.sleep(0.1)
            
            if self.serial.in_waiting:
                # 读取响应数据
                response = self.serial.read(self.serial.in_waiting)
                
                # 显示接收到的数据（HEX格式）
                hex_display = ' '.join([f"{b:02X}" for b in response])
                self.show_message_on_text_browser(f"接收到写站号响应: {hex_display}")
                
                # 检查是否为正常响应（非异常）
                if len(response) >= 3 and response[1] == 0x10 and not (response[1] & 0x80):
                    # 写入成功，立即更新当前站号变量
                    self.current_device_station_id = new_station_id
                    self.show_message_on_text_browser(f"站号已成功修改为: {new_station_id}")
                    self.show_message_on_text_browser(f"当前通信站号已更新为: {new_station_id}")
                    
                    # 更新界面显示
                    self.update_station_id_display(new_station_id)
                else:
                    # 可能是异常响应
                    self.show_message_on_text_browser("写入站号失败，请检查设备连接和命令格式")
           
            
        except Exception as e:
            self.show_message_on_text_browser(f"处理写站号响应错误: {str(e)}")
        finally:
            # 清除命令类型标记
            self.last_command_type = None

    def read_device_station_id(self):
        """读取设备站号"""
        try:
            if not self.serial.isOpen():
                self.show_message_on_text_browser("串口未打开")
                return
                
            # 使用当前保存的设备站号发送命令
            current_id = self.current_device_station_id
            
            # 命令格式: [站号] [功能码03] [寄存器高字节] [寄存器低字节] [数据长度高字节] [数据长度低字节]
            command = bytearray([current_id, 0x03, 0x20, 0x05, 0x00, 0x01])
            
            # 计算CRC校验并添加到命令末尾
            crc = self.calculate_crc(command)
            command.extend(crc)
            
            # 发送命令
            self.serial.write(command)
            
            # 设置命令类型为站号读取
            self.last_command_type = 'read_station_id'
            
            # 显示发送的命令
            command_hex = ' '.join([f"{b:02X}" for b in command])
            self.show_message_on_text_browser(f"读站号: {command_hex}")
            
            # 延迟处理响应
            QtCore.QTimer.singleShot(200, self.read_station_id_response)
        except Exception as e:
            self.show_message_on_text_browser(f"读取站号错误: {str(e)}")

    def read_position(self):
        """读取当前位置数据"""
        try:
            if not self.serial.isOpen():
                self.show_message_on_text_browser("错误: 请先打开串口")
                return
            
            # 获取当前使用的站号
            if hasattr(self, 'current_device_station_id'):
                current_station_id = self.current_device_station_id
            else:
                current_station_id = int(self.comboBox_2.currentText())
            
            # 设置命令类型标记
            self.last_command_type = 'read_position'
            
            # 构建读取位置的Modbus命令
            command = bytearray([
                current_station_id,  # 站号（使用当前站号）
                0x03,                # 功能码：读取保持寄存器
                0x10, 0x00,          # 起始地址：0x1000
                0x00, 0x02            # 寄存器数量：2个
            ])
            
            # 计算CRC
            crc = self.calculate_crc(command)
            command.extend(crc)
            
            # 发送命令
            self.serial.write(command)
            
            # 显示发送的命令
            hex_cmd = ' '.join([f"{b:02X}" for b in command])
            self.show_message_on_text_browser(f"读位置: {hex_cmd}")
            
        except Exception as e:
            self.show_message_on_text_browser(f"读取位置错误: {str(e)}")
            # 出错时清除命令类型
            self.last_command_type = None

    def process_station_id_response(self, response):
        """处理站号响应的具体逻辑"""
        try:
            # 显示接收到的数据用于调试
            hex_display = ' '.join([f"{b:02X}" for b in response])
            self.show_message_on_text_browser(f"处理站号响应: {hex_display}")
            
            # 检查响应格式是否正确
            if len(response) >= 5 and response[1] == 0x03 and response[2] == 0x02:
                # 站号值在第4个字节位置
                station_id = response[4]  # 取第二个数据字节为站号
                
                # 更新当前设备站号变量
                self.current_device_station_id = station_id
                
                # 更新UI显示
                self.update_station_id_display(station_id)
                
                # 显示提示信息
                self.show_message_on_text_browser(f"读取站号成功: {station_id}")
                return True
            else:
                self.show_message_on_text_browser(f"站号响应格式不正确: {hex_display}")
                return False
                
        except Exception as e:
            self.show_message_on_text_browser(f"解析站号数据错误: {str(e)}")
            return False
        finally:
            # 清除命令类型标记
            self.last_command_type = None

    def update_station_id_display(self, station_id):
        """更新站号显示"""
        try:
            # 更新站号显示
            self.comboBox_2.setCurrentIndex(station_id - 1)
            self.show_message_on_text_browser(f"已更新站号为: {station_id}")
        except Exception as e:
            self.show_message_on_text_browser(f"更新站号显示错误: {str(e)}")

    def read_device_baudrate(self):
        """读取设备当前波特率"""
        if not self.serial.isOpen():
            self.show_message_on_text_browser("错误: 请先打开串口")
            return
        
        try:
            # 获取当前实际站号
            if hasattr(self, 'current_device_station_id') and self.current_device_station_id is not None:
                current_station_id = self.current_device_station_id
            else:
                self.show_message_on_text_browser("错误: 未检测到设备当前站号，请等待设备发送ID信息")
                return
            
            # 构建读波特率命令（不含CRC）
            command_without_crc = bytes([
                current_station_id,  # 当前从机号（使用动态检测到的值）
                0x03,               # 功能码：读寄存器
                0x20, 0x06,         # 寄存器地址：0x2006
                0x00, 0x02          # 寄存器数量：1个
            ])
            
            # 计算CRC
            crc = self.calculate_crc(command_without_crc)
            
            # 构建完整的指令（包含CRC）
            read_command = command_without_crc + crc
            
            # 发送读波特率指令
            self.serial.write(read_command)
            
            # 显示发送的指令（HEX格式）
            hex_display = ' '.join([f"{b:02X}" for b in read_command])
            self.show_message_on_text_browser(f"读取波特率: {hex_display}")
            
            # 设置命令类型标记
            self.last_command_type = 'read_baudrate'
            
            # 等待并读取响应
            QtCore.QTimer.singleShot(100, self.read_baudrate_response)
            
        except Exception as e:
            self.show_message_on_text_browser(f"读取波特率错误: {str(e)}")

    def read_baudrate_response(self):
        """处理读波特率响应"""
        try:
            # 等待接收数据
            time.sleep(0.1)
            
            # 获取当前使用的站号
            if hasattr(self, 'current_device_station_id'):
                current_station_id = self.current_device_station_id
            else:
                current_station_id = int(self.comboBox_2.currentText())
            
            # 先检查串口缓冲区
            if self.serial.in_waiting:
                # 读取响应数据
                response = self.serial.read(self.serial.in_waiting)
                
                # 显示接收到的数据（HEX格式）
                hex_display = ' '.join([f"{b:02X}" for b in response])
                self.show_message_on_text_browser(f"接收到波特率响应: {hex_display}")
                
                # 处理响应数据
                self.process_baudrate_response(response)
            else:
                # 检查文本浏览器中最近显示的内容，看是否包含波特率响应
                text_browser_content = self.textBrowser.toPlainText()
                last_lines = text_browser_content.strip().split('\n')[-10:]  # 获取最后10行
                
                # 寻找符合模式的行："01 03 04 C2 00 00 01"或类似格式
                for line in last_lines:
                    # 检查是否包含"03 04"这样的特征序列，表明这可能是读取波特率的响应
                    if f"{current_station_id:02X} 03 04" in line:
                        # 提取响应数据
                        parts = line.split()
                        try:
                            # 尝试从文本中提取响应数据的字节
                            station_id_index = parts.index(f"{current_station_id:02X}")
                            if len(parts) >= station_id_index + 7:  # 站号+03+04+4字节数据+CRC(至少)
                                # 构建响应数据
                                response_bytes = bytes([
                                    int(parts[station_id_index], 16),     # 站号
                                    int(parts[station_id_index + 1], 16), # 03
                                    int(parts[station_id_index + 2], 16), # 04
                                    int(parts[station_id_index + 3], 16), # 数据1
                                    int(parts[station_id_index + 4], 16), # 数据2
                                    int(parts[station_id_index + 5], 16), # 数据3
                                    int(parts[station_id_index + 6], 16)  # 数据4
                                ])
                                #self.show_message_on_text_browser("检测到已接收的波特率响应数据")
                                self.process_baudrate_response(response_bytes)
                                return
                        except (ValueError, IndexError) as e:
                            continue
                
                # 如果没有找到有效响应
                self.show_message_on_text_browser("未收到波特率响应数据")
                
        except Exception as e:
            self.show_message_on_text_browser(f"处理波特率响应错误: {str(e)}")

    def process_baudrate_response(self, response):
        """解析波特率响应数据"""
        try:
            # 检查响应格式是否正确
            if len(response) >= 7 and response[0] == int(self.comboBox_2.currentText()) and response[1] == 0x03 and response[2] == 0x04:
                # 提取波特率数据(4个字节)
                baud_bytes = response[3:7]
                
                # 处理波特率数据并显示
                self.process_baudrate_from_hex(baud_bytes)
            else:
                self.show_message_on_text_browser("波特率响应数据格式错误")
                
        except Exception as e:
            self.show_message_on_text_browser(f"解析波特率响应错误: {str(e)}")
        finally:
            # 清除命令类型标记
            if hasattr(self, 'last_command_type'):
                self.last_command_type = None

    def process_baudrate_from_hex(self, baud_bytes):
        """从十六进制数据解析波特率并更新UI"""
        try:
            # 原始波特率字节
            hex_str_original = ' '.join([f"{b:02X}" for b in baud_bytes])
            #self.show_message_on_text_browser(f"波特率原始HEX值: {hex_str_original}")
            
            # 重新排序波特率字节（与位置数据使用相同的规则: 2,3,0,1）
            reordered_baud_bytes = bytes([
                baud_bytes[0],  # 第三个字节
                baud_bytes[1],  # 第四个字节
                baud_bytes[2],  # 第一个字节
                baud_bytes[3]   # 第二个字节
            ])
            
            # 将重新排序后的字节转换为整数值（使用大端格式）
            baud_value = int.from_bytes(reordered_baud_bytes, byteorder='big', signed=False)
            
            # 显示HEX格式的重排序波特率数据
            hex_str_reordered = ' '.join([f"{b:02X}" for b in reordered_baud_bytes])
            #self.show_message_on_text_browser(f"波特率HEX值(重排序): {hex_str_reordered}")
            
            # 波特率映射表（与get_baudrate_hex_value保持一致）
            baud_map = {
                115200: 0x0001C200,
                9600: 0x00002580,
                19200: 0x00004B00,
                4800: 0x000012C0,
                57600: 0x0000E100,
                256000: 0x0003E800
            }
            
            # 查找匹配的波特率
            actual_baud = None
            for baud, value in baud_map.items():
                if value == baud_value:
                    actual_baud = baud
                    break
            
            if actual_baud is None:
                # 如果没有精确匹配，选择界面上显示的波特率
                current_ui_baud = int(self.comboBox_3.currentText())
                self.show_message_on_text_browser(f"设备返回的波特率值(0x{baud_value:08X})在映射表中未找到")
                self.show_message_on_text_browser(f"使用当前界面显示的波特率：{current_ui_baud}")
                
                # 不再显示不支持的消息，而是直接使用当前显示的波特率
                actual_baud = current_ui_baud
            
            # 更新UI显示
            index = self.comboBox_3.findText(str(actual_baud))
            if index >= 0:
                self.comboBox_3.setCurrentIndex(index)
                self.show_message_on_text_browser(f"读取波特率成功: {actual_baud}")
            else:
                # 如果界面下拉框中没有这个波特率值，仍然显示信息但不报错
                self.show_message_on_text_browser(f"波特率 {actual_baud} 不在下拉框预设列表中")
                
        except Exception as e:
            self.show_message_on_text_browser(f"处理波特率数据错误: {str(e)}")

    def write_baudrate(self):
        """写入波特率到设备"""
        try:
            # 获取要设置的波特率
            baudrate_text = self.comboBox_3.currentText()
            if not baudrate_text:
                self.show_message_on_text_browser("请选择波特率")
                return
                
            new_baudrate = int(baudrate_text)
            self.show_message_on_text_browser(f"准备将波特率修改为: {new_baudrate}")
            
            # 获取设备站号
            station_id = self.get_current_station_id()
            
            # 获取波特率对应的字节列表
            baud_bytes = self.get_baudrate_hex_value(new_baudrate)
            if not baud_bytes:
                return
                
            # 波特率寄存器地址 0x2006
            register_address = [0x20, 0x06]
            
            # 寄存器数量 0x0002 (4字节数据需要2个寄存器)
            register_count = [0x00, 0x02]
            
            # 字节数 0x04 (4字节数据)
            byte_count = 0x04
            
            # 记录命令类型
            self.last_command_type = 'write_baudrate'
            
            # 构建完整的写入命令（不分段发送）
            command = [
                station_id,        # 站号
                0x10,              # 功能码：写多个寄存器
                register_address[0], register_address[1],  # 寄存器起始地址
                register_count[0], register_count[1],      # 寄存器数量
                byte_count,        # 字节数
                baud_bytes[0], baud_bytes[1], baud_bytes[2], baud_bytes[3]  # 波特率值（4字节）
            ]
            
            # 计算CRC
            crc = self.calculate_crc(command)
            command.extend(crc)
            
            # 发送前记录完整命令（用于调试）
            hex_command = ' '.join([f"{b:02X}" for b in command])
            self.show_message_on_text_browser(f"波特率修改: {hex_command}")
            
            # 发送命令
            self.serial.write(bytes(command))
            
            # 等待并读取响应
            QtCore.QTimer.singleShot(200, lambda: self.read_write_baudrate_response(new_baudrate))
            
        except Exception as e:
            self.show_message_on_text_browser(f"写波特率错误: {str(e)}")

    def get_baudrate_hex_value(self, baudrate):
        """获取波特率对应的字节列表值（4字节）"""
        # 波特率映射表 - 使用一致的格式（整数值拆分为4个字节）
        baudrate_map = {
            115200: [0x00, 0x01, 0xC2, 0x00],
            9600: [0x00, 0x00, 0x25, 0x80],
            19200: [0x00, 0x00, 0x4B, 0x00],
            4800: [0x00, 0x00, 0x12, 0xC0],
            57600: [0x00, 0x00, 0xE1, 0x00],
            256000: [0x00, 0x03, 0xE8, 0x00]
        }
        
        if baudrate in baudrate_map:
            # 添加详细日志
            hex_str = ' '.join([f"{b:02X}" for b in baudrate_map[baudrate]])
            self.show_message_on_text_browser(f"波特率 {baudrate}")
            return baudrate_map[baudrate]
        else:
            self.show_message_on_text_browser(f"错误: 不支持的波特率 {baudrate}")
            return None

    def read_write_baudrate_response(self, new_baudrate):
        """处理写入波特率的响应"""
        try:
            # 等待接收数据
            time.sleep(0.1)
            
            if self.serial.in_waiting:
                # 读取响应数据
                response = self.serial.read(self.serial.in_waiting)
                
                # 显示接收到的数据（HEX格式）
                hex_display = ' '.join([f"{b:02X}" for b in response])
                self.show_message_on_text_browser(f"接收到波特率写入响应: {hex_display}")
                
                # 检查响应是否正确
                if len(response) >= 6 and response[1] == 0x10:
                    self.show_message_on_text_browser(f"波特率已成功设置为 {new_baudrate} bps")
                    self.show_message_on_text_browser("重要提示: 请重启设备以使新的波特率设置生效！")
                    
                    # 弹出提示框
                    QtWidgets.QMessageBox.information(
                        self, 
                        "设置成功",
                        f"波特率已成功设置为 {new_baudrate} bps\n请重启设备以使新的波特率设置生效！"
                    )
                else:
                    self.show_message_on_text_browser("波特率设置响应数据错误")
            #else:
                #self.show_message_on_text_browser("未收到波特率设置响应")
                
        except Exception as e:
            self.show_message_on_text_browser(f"处理波特率设置响应错误: {str(e)}")

    def read_device_protocol(self):
        """读取设备当前的协议设置"""
        try:
            if not self.serial.isOpen():
                self.show_message_on_text_browser("串口未打开")
                return
                
            # 获取当前设备站号
            if hasattr(self, 'current_device_station_id'):
                current_station_id = self.current_device_station_id
            else:
                # 如果没有保存的设备站号，则使用界面当前值
                current_station_id = int(self.comboBox_2.currentText())
                
            # 构建读协议命令（不含CRC）
            command_without_crc = bytes([
                current_station_id,  # 使用当前站号（动态）
                0x03,               # 功能码
                0x20, 0x0A,        # 寄存器地址
                0x00, 0x02         # 寄存器数量
            ])
            
            # 计算CRC
            crc = self.calculate_crc(command_without_crc)
            
            # 构建完整命令（包含CRC）
            command = command_without_crc + crc
            
            # 发送命令
            self.serial.write(command)
            
            # 设置命令类型标记
            self.last_command_type = 'read_protocol'
            
            # 显示发送的命令
            hex_command = ' '.join([f"{b:02X}" for b in command])
            self.show_message_on_text_browser(f"读协议: {hex_command}")
            
        except Exception as e:
            self.show_message_on_text_browser(f"读取协议错误: {str(e)}")

    def write_protocol(self):
        """写入新的协议设置到设备"""
        try:
            if not self.serial.isOpen():
                self.show_message_on_text_browser("错误: 请先打开串口")
                return
            
            # 获取当前设备站号
            if hasattr(self, 'current_device_station_id') and self.current_device_station_id is not None:
                current_station_id = self.current_device_station_id
            else:
                self.show_message_on_text_browser("错误: 未检测到设备当前站号，请等待设备发送ID信息")
                return
            
            # 获取当前选择的协议值
            selected_protocol = int(self.comboBox.currentText())
            
            # 构建写协议命令（不含CRC）
            command_without_crc = bytes([
                current_station_id,  # 使用当前站号
                0x10,               # 功能码：写多个寄存器
                0x20, 0x0A,        # 起始地址：0x200A
                0x00, 0x01,        # 寄存器数量：1个
                0x02,              # 字节数：2个字节
                0x00,              # 高字节固定为0x00
                0x01 if selected_protocol == 1 else 0x00  # 低字节：协议值
            ])
            
            # 计算CRC
            crc = self.calculate_crc(command_without_crc)
            
            # 构建完整命令
            command = command_without_crc + crc
            
            # 发送命令
            self.serial.write(command)
            
            # 设置命令类型标记
            self.last_command_type = 'write_protocol'
            
            # 显示发送的命令（HEX格式）
            hex_display = ' '.join([f"{b:02X}" for b in command])
            self.show_message_on_text_browser(f"写协议: {hex_display}")
            
            # 等待并处理响应
            QtCore.QTimer.singleShot(100, self.read_write_protocol_response)
            
        except Exception as e:
            self.show_message_on_text_browser(f"写入协议错误: {str(e)}")

    def read_write_protocol_response(self):
        """处理写入协议的响应"""
        try:
            # 等待接收数据
            time.sleep(0.2)
            
            if self.serial.in_waiting:
                # 读取响应数据
                response = self.serial.read(self.serial.in_waiting)
                
                # 显示接收到的数据（HEX格式）
                hex_display = ' '.join([f"{b:02X}" for b in response])
                self.show_message_on_text_browser(f"接收到写入协议响应: {hex_display}")
                
                # 检查响应是否正确（站号 + 0x10 + 寄存器地址 + 寄存器数量）
                if len(response) >= 6 and response[1] == 0x10:
                    protocol_value = int(self.comboBox.currentText())
                    self.show_message_on_text_browser(f"协议已成功设置为: {protocol_value}")
                else:
                    self.show_message_on_text_browser("写入协议响应数据错误")
            #else:
                #self.show_message_on_text_browser("未收到写入协议响应")
            
        except Exception as e:
            self.show_message_on_text_browser(f"处理写入协议响应错误: {str(e)}")
        finally:
            # 清除命令类型标记
            self.last_command_type = None

    def process_parameter_buffer(self):
        """处理参数缓冲区中的数据"""
        if not self.parameter_buffer:
            return
        
        # 添加调试信息：显示当前参数缓冲区内容
        self.show_message_on_text_browser(self.parameter_buffer)
        
        # 检查自上次接收数据以来是否已经过去了足够的时间
        current_time = QtCore.QTime.currentTime().msecsSinceStartOfDay()
        time_elapsed = current_time - self.last_receive_time
        
        # 添加调试信息：显示时间间隔
        #self.show_message_on_text_browser(f"[调试] 距离上次接收数据时间: {time_elapsed}ms")
        
        # 增加等待时间到500ms，确保能收到完整的数据
        if time_elapsed > 400:  # 增加等待时间
            # 尝试解析缓冲区中的参数
            if self.try_parse_parameters(self.parameter_buffer):
                #self.show_message_on_text_browser("[调试] 参数解析成功")
            # 清空缓冲区
             self.parameter_buffer = ""
            #self.show_message_on_text_browser("[调试] 参数缓冲区已清空")
        elif all(param in self.parameter_buffer for param in ["ID=", "Baud=", "Proc=", "BSL=", "BSR=", "Z="]):
            # 如果所有参数都已收到，立即解析
            if self.try_parse_parameters(self.parameter_buffer):
                #self.show_message_on_text_browser("[调试] 所有参数已收到并解析成功")
            # 清空缓冲区
             self.parameter_buffer = ""
            #   self.show_message_on_text_browser("[调试] 参数缓冲区已清空")

    def process_text_buffer(self):
        """处理文本显示缓冲区"""
        if not self.text_display_buffer:
            return
        
        # 添加调试信息：显示当前文本缓冲区内容
        self.show_message_on_text_browser(self.text_display_buffer)
        
        current_time = QtCore.QTime.currentTime().msecsSinceStartOfDay()
        time_elapsed = current_time - self.last_text_time
        
        # 添加调试信息：显示时间间隔
        #self.show_message_on_text_browser(f"[调试] 距离上次文本显示时间: {time_elapsed}ms")
        
        if time_elapsed > 80 or '\n' in self.text_display_buffer or len(self.text_display_buffer) > 100:
            # 检查缓冲区中是否有特定的字符串，可能表示一条完整消息
            complete_markers = ["CAL Finish", "Left", "Right", "Forward Movement"]
            
            # 判断是否可以显示缓冲区内容
            should_display = True
            
            # 检查缓冲区是否可能截断了一个单词
            last_word = self.text_display_buffer.split()[-1] if self.text_display_buffer.split() else ""
            if len(last_word) <= 3 and not any(marker in self.text_display_buffer for marker in complete_markers):
                # 如果最后一个词很短，并且没有发现完整消息标记，可能是截断的单词
                if time_elapsed < 150:  # 等待更长时间（150ms）
                    should_display = False
                    #self.show_message_on_text_browser("[调试] 等待更多数据，可能存在截断的单词")
            
            #if should_display:
                # 显示缓冲区中的文本
                #self.show_message_on_text_browser(self.text_display_buffer)
                
        if self.text_display_buffer:
            try:
                # 检查text_display_buffer的类型并相应处理
                if isinstance(self.text_display_buffer, bytes):
                    # 如果是字节类型，则解码
                    text = self.text_display_buffer.decode('utf-8')
                else:
                    # 如果已经是字符串类型，直接使用
                    text = self.text_display_buffer
                    
                # 添加到文本浏览器
                #self.textBrowser.append(text)
                
                # 检查是否包含"CAL Finish!!!"
                if "......Cal Finished......!" in text:
                    # 获取textBrowser的完整内容
                    full_text = self.textBrowser.toPlainText()
                    
                    # 按行分割
                    lines = full_text.split('\n')
                    
                    # 从后往前查找最新的CAL Finish!!!的位置
                    cal_finish_index = -1
                    for i in range(len(lines) - 1, -1, -1):  # 从最后一行开始往前查找
                        if "......Cal Finished......!" in lines[i]:
                            cal_finish_index = i
                            break
                    
                    # 如果找到了CAL Finish!!!并且下一行存在
                    if cal_finish_index != -1 and cal_finish_index + 1 < len(lines):
                        # 提取下一行（参数信息）
                        param_line = lines[cal_finish_index + 1].strip()
                        
                        # 验证提取的行是否包含参数信息
                        if "P=" in param_line and "BSL=" in param_line and "BSR=" in param_line and "BSB=" in param_line:
                            # 显示在textBrowser_7中
                            self.textBrowser_7.clear()
                            self.textBrowser_7.append(param_line)
                
                # 清空缓冲区
                self.text_display_buffer = b'' if isinstance(self.text_display_buffer, bytes) else ''
                
            except Exception as e:
                self.show_message_on_text_browser(f"处理文本显示错误: {str(e)}")

    def read_station_id_response(self):
        """处理读站号响应"""
        try:
            # 等待接收数据
            time.sleep(0.1)
            
            # 检查串口是否有数据
            if self.serial.in_waiting:
                # 读取响应数据
                response = self.serial.read(self.serial.in_waiting)
                
                # 显示接收到的数据（HEX格式）
                hex_display = ' '.join([f"{b:02X}" for b in response])
                self.show_message_on_text_browser(f"接收到站号响应: {hex_display}")
                
                # 处理响应数据
                if self.process_station_id_response(response):
                    return  # 处理成功
            
            # 如果没有数据或处理失败
            #self.show_message_on_text_browser("未收到有效的站号响应数据")
        except Exception as e:
            self.show_message_on_text_browser(f"处理站号响应错误: {str(e)}")
        finally:
            # 确保清除命令类型标记
            if hasattr(self, 'last_command_type'):
                self.last_command_type = None

    def write_initial_value_zero(self):
        """写入取消置零"""
        try:
            if not self.serial.isOpen():
                self.show_message_on_text_browser("错误: 请先打开串口")
                return
            
            # 获取当前设备站号
            if hasattr(self, 'current_device_station_id'):
                current_station_id = self.current_device_station_id
            else:
                # 如果没有保存的设备站号，则使用界面当前值
                current_station_id = int(self.comboBox_2.currentText())
            
            # 构建置零命令（不含CRC）
            command_without_crc = bytes([
                current_station_id,  # 使用当前站号（动态）
                0x10,               # 功能码
                0x20, 0x07,        # 寄存器地址
                0x00, 0x01,        # 寄存器数量
                0x02,              # 字节数
                0x00, 0x00         # 数据：置零
            ])
            
            # 计算CRC
            crc = self.calculate_crc(command_without_crc)
            
            # 构建完整命令（包含CRC）
            command = command_without_crc + crc
            
            # 发送指令
            self.serial.write(command)
            
            # 设置命令类型标记
            self.last_command_type = 'write_initial_value'
            
            # 显示发送的指令
            hex_command = ' '.join([f"{b:02X}" for b in command])
            self.show_message_on_text_browser(f"取消: {hex_command}")
            
            # 等待并处理响应
            QtCore.QTimer.singleShot(100, self.read_write_initial_value_response)
            
            # 延迟200ms后自动读取Modbus数据
            QtCore.QTimer.singleShot(200, self.read_modbus_data)
            
        except Exception as e:
            self.show_message_on_text_browser(f"写入初始值置零错误: {str(e)}")

    def write_initial_value_restore(self):
        """写入置零"""
        try:
            if not self.serial.isOpen():
                self.show_message_on_text_browser("错误: 请先打开串口")
                return
            
            # 获取当前设备站号
            if hasattr(self, 'current_device_station_id'):
                current_station_id = self.current_device_station_id
            else:
                # 如果没有保存的设备站号，则使用界面当前值
                current_station_id = int(self.comboBox_2.currentText())
            
            # 构建恢复命令（不含CRC）
            command_without_crc = bytes([
                current_station_id,  # 使用当前站号（动态）
                0x10,               # 功能码
                0x20, 0x07,        # 寄存器地址
                0x00, 0x01,        # 寄存器数量
                0x02,              # 字节数
                0x00, 0x01         # 数据：恢复
            ])
            
            # 计算CRC
            crc = self.calculate_crc(command_without_crc)
            
            # 构建完整命令（包含CRC）
            command = command_without_crc + crc
            
            # 发送指令
            self.serial.write(command)
            
            # 设置命令类型标记
            self.last_command_type = 'write_initial_value'
            
            # 显示发送的指令
            hex_command = ' '.join([f"{b:02X}" for b in command])
            self.show_message_on_text_browser(f"置零: {hex_command}")
            
            # 等待并处理响应
            QtCore.QTimer.singleShot(100, self.read_write_initial_value_response)
            # 延迟200ms后自动读取Modbus数据
            QtCore.QTimer.singleShot(200, self.read_modbus_data)            
        except Exception as e:
            self.show_message_on_text_browser(f"写入初始值恢复错误: {str(e)}")

    def read_write_initial_value_response(self):
        """处理写入初始值的响应"""
        try:
            # 等待接收数据
            time.sleep(0.1)
            
            if self.serial.in_waiting:
                # 读取响应数据
                response = self.serial.read(self.serial.in_waiting)
                
                # 显示接收到的数据（HEX格式）
                hex_display = ' '.join([f"{b:02X}" for b in response])
                self.show_message_on_text_browser(f"接收到初始值设置响应: {hex_display}")
                
                # 检查响应是否正确（站号 + 0x10 + 寄存器地址 + 寄存器数量）
                if len(response) >= 6 and response[1] == 0x10:
                    self.show_message_on_text_browser("初始值设置成功")
                else:
                    self.show_message_on_text_browser("初始值设置响应数据错误")
            #else:
                #self.show_message_on_text_browser("未收到初始值设置响应")
                
        except Exception as e:
            self.show_message_on_text_browser(f"处理初始值设置响应错误: {str(e)}")
        finally:
            # 清除命令类型标记
            self.last_command_type = None

    def write_bsl_value(self):
        """写入BSL参数值到设备"""
        try:
            if not self.serial.isOpen():
                self.show_message_on_text_browser("错误: 请先打开串口")
                return
            
            # 获取当前设备站号
            station_id = int(self.comboBox_2.currentText())
            
            # 获取用户输入的BSL值（十进制）
            try:
                bsl_value = int(self.lineEdit_4.text())
                if bsl_value < 0 or bsl_value > 65535:
                    self.show_message_on_text_browser("错误: BSL值必须在0-65535范围内")
                    return
            except ValueError:
                self.show_message_on_text_browser("错误: BSL值必须为有效的数字")
                return
            
            # 将BSL值转换为两个字节的十六进制
            bsl_high = (bsl_value >> 8) & 0xFF
            bsl_low = bsl_value & 0xFF
            
            # 构建指令（不含CRC）
            command_without_crc = bytes([
                station_id,  # 站号（动态）
                0x10,        # 功能码
                0x20, 0x08,  # 寄存器地址
                0x00, 0x01,  # 寄存器数量
                0x02,        # 数据字节数
                bsl_high, bsl_low  # BSL值（两个字节）
            ])
            
            # 计算CRC
            crc = self.calculate_crc(command_without_crc)
            
            # 构建完整命令（包含CRC）
            command = command_without_crc + crc
            
            # 发送指令
            self.serial.write(command)
            
            # 设置命令类型标记
            self.last_command_type = 'write_bsl'
            
            # 显示发送的指令
            hex_command = ' '.join([f"{b:02X}" for b in command])
            self.show_message_on_text_browser(f"BSL: {hex_command}")
            
            # 等待并处理响应
            QtCore.QTimer.singleShot(100, self.read_write_bs_response)
            
        except Exception as e:
            self.show_message_on_text_browser(f"写入BSL错误: {str(e)}")

    def write_bsr_value(self):
        """写入BSR参数值到设备"""
        try:
            if not self.serial.isOpen():
                self.show_message_on_text_browser("错误: 请先打开串口")
                return
            
            # 获取当前设备站号
            station_id = int(self.comboBox_2.currentText())
            
            # 获取用户输入的BSR值（十进制）
            try:
                bsr_value = int(self.lineEdit_7.text())
                if bsr_value < 0 or bsr_value > 65535:
                    self.show_message_on_text_browser("错误: BSR值必须在0-65535范围内")
                    return
            except ValueError:
                self.show_message_on_text_browser("错误: BSR值必须为有效的数字")
                return
            
            # 将BSR值转换为两个字节的十六进制
            bsr_high = (bsr_value >> 8) & 0xFF
            bsr_low = bsr_value & 0xFF
            
            # 构建指令（不含CRC）
            command_without_crc = bytes([
                station_id,  # 站号（动态）
                0x10,        # 功能码
                0x20, 0x09,  # 寄存器地址（注意与BSL不同，这里是09）
                0x00, 0x01,  # 寄存器数量
                0x02,        # 数据字节数
                bsr_high, bsr_low  # BSR值（两个字节）
            ])
            
            # 计算CRC
            crc = self.calculate_crc(command_without_crc)
            
            # 构建完整命令（包含CRC）
            command = command_without_crc + crc
            
            # 发送指令
            self.serial.write(command)
            
            # 设置命令类型标记
            self.last_command_type = 'write_bsr'
            
            # 显示发送的指令
            hex_command = ' '.join([f"{b:02X}" for b in command])
            self.show_message_on_text_browser(f"BSR: {hex_command}")
            
            # 等待并处理响应
            QtCore.QTimer.singleShot(100, self.read_write_bs_response)
            
        except Exception as e:
            self.show_message_on_text_browser(f"写入BSR错误: {str(e)}")

    def read_write_bs_response(self):
        """处理写入BSL/BSR/BSB的响应"""
        try:
            # 等待接收数据
            time.sleep(0.1)
            
            if self.serial.in_waiting:
                # 读取响应数据
                response = self.serial.read(self.serial.in_waiting)
                
                # 显示接收到的数据（HEX格式）
                hex_display = ' '.join([f"{b:02X}" for b in response])
                
                # # 根据命令类型确定参数名和值
                # if self.last_command_type == 'write_bsl':
                #     self.show_message_on_text_browser(f"接收到BSL写入响应: {hex_display}")
                #     param_name = "BSL"
                #     param_value = self.lineEdit_4.text()
                # elif self.last_command_type == 'write_bsr':
                #     self.show_message_on_text_browser(f"接收到BSR写入响应: {hex_display}")
                #     param_name = "BSR"
                # else:  # 'write_bsb'
                #     self.show_message_on_text_browser(f"接收到BSB写入响应: {hex_display}")
                #     param_name = "BSB"
                #     param_value = self.lineEdit_5.text()
                
                # 检查响应是否正确（站号 + 0x10 + 寄存器地址 + 寄存器数量）
                # if len(response) >= 6 and response[1] == 0x10:
                #     self.show_message_on_text_browser(f"{param_name}参数已成功设置为: {param_value}")
                # else:
                #     self.show_message_on_text_browser(f"{param_name}参数设置响应数据错误")
                
        except Exception as e:
            param_name = "BSL" if self.last_command_type == 'write_bsl' else "BSR" if self.last_command_type == 'write_bsr' else "BSB"
            self.show_message_on_text_browser(f"处理{param_name}写入响应错误: {str(e)}")
        finally:
            # 清除命令类型标记
            self.last_command_type = None

    def write_bsb_value(self):
        """写入BSB参数值到设备"""
        try:
            if not self.serial.isOpen():
                self.show_message_on_text_browser("错误: 请先打开串口")
                return
            
            # 获取当前设备站号
            station_id = int(self.comboBox_2.currentText())
            
            # 获取用户输入的BSB值（十进制）
            try:
                bsb_value = int(self.lineEdit_5.text())
                if bsb_value < 0 or bsb_value > 65535:
                    self.show_message_on_text_browser("错误: BSB值必须在0-65535范围内")
                    return
            except ValueError:
                self.show_message_on_text_browser("错误: BSB值必须为有效的数字")
                return
            
            # 将BSB值转换为两个字节的十六进制
            bsb_high = (bsb_value >> 8) & 0xFF
            bsb_low = bsb_value & 0xFF
            
            # 构建指令（不含CRC）
            command_without_crc = bytes([
                station_id,  # 站号（动态）
                0x10,        # 功能码
                0x20, 0x11,  # 寄存器地址：0x2011
                0x00, 0x01,  # 寄存器数量：1
                0x02,        # 数据字节数：2
                bsb_high, bsb_low  # BSB值（两个字节）
            ])
            
            # 计算CRC
            crc = self.calculate_crc(command_without_crc)
            
            # 构建完整命令（包含CRC）
            command = command_without_crc + crc
            
            # 发送指令
            self.serial.write(command)
            
            # 设置命令类型标记
            self.last_command_type = 'write_bsb'
            
            # 显示发送的指令
            hex_command = ' '.join([f"{b:02X}" for b in command])
            self.show_message_on_text_browser(f"BSB: {hex_command}")
            
            # 等待并处理响应
            QtCore.QTimer.singleShot(100, self.read_write_bs_response)
            
        except Exception as e:
            self.show_message_on_text_browser(f"写入BSB错误: {str(e)}")            

    def get_current_station_id(self):
        """获取当前设备站号"""
        # 如果已经检测到设备站号，优先使用它
        if hasattr(self, 'current_device_station_id') and self.current_device_station_id is not None:
            # 输出日志便于调试
            #self.show_message_on_text_browser(f"站号: {self.current_device_station_id}")
            return self.current_device_station_id
        # 否则使用界面当前值
        try:
            return int(self.comboBox_2.currentText())
        except:
            # 默认值
            return 1

    def set_position_status_error(self, error_code, message):
        """设置位置状态为错误，只在确认是位置相关操作时才处理"""
        try:
            # 只有当前命令与位置相关或已经在位置页面时才显示错误
            if (hasattr(self, 'last_command_type') and self.last_command_type == 'read_position') or \
               self.tabWidget.currentIndex() == 2:  # 假设位置页面索引为2
                
                # 获取错误信息
                error_message = self.get_error_message(error_code) if message is None else message
                
                # 更新UI显示
                self.label_47.setText(f"错误: {error_message}")
                self.label_47.setStyleSheet("color: red;")
                
                # 更新错误代码显示
                self.lineEdit_53.setText(str(error_code))
                
                # 记录错误
                #self.show_message_on_text_browser(f"位置读取错误: 代码={error_code}, {error_message}")

        except Exception as e:
                # 不是位置相关操作，只记录错误但不更新位置状态
                self.show_message_on_text_browser(f"设备返回错误: 代码={error_code}, {message}")
                self.lineEdit_6.setStyleSheet("background-color: #ffcccc;")  # 浅红色背景
                self.lineEdit_6.setText(f"错误代码: {error_code} - {message}")
       #except Exception as e:
            #self.show_message_on_text_browser(f"显示错误状态时出错: {str(e)}")

    def toggle_auto_send_checkbox(self, state):
        """通过复选框控制自动发送"""
        try:
            if state == QtCore.Qt.Checked:
                # 获取时间间隔
                interval = int(self.lineEdit_2.text())
                if interval < 100:  # 设置最小间隔
                    interval = 100
                    self.lineEdit_2.setText("100")
                self.auto_send_timer.start(interval)
            else:
                self.auto_send_timer.stop()
        except ValueError:
            # 如果输入的不是有效数字，停止自动发送并显示错误
            self.auto_send_timer.stop()
            self.checkBox_2.setChecked(False)
            self.show_message_on_text_browser("错误：请输入有效的时间间隔（100-60000ms）")

    def update_auto_send_interval(self):
        """更新自动发送的时间间隔"""
        try:
            if self.checkBox_2.isChecked():
                interval = int(self.lineEdit_2.text())
                if interval >= 100:  # 只有当间隔大于等于100ms时才更新
                    self.auto_send_timer.start(interval)
        except ValueError:
            pass  # 输入非数字时不处理，等待用户输入有效数字

    # 添加新方法来处理pushButton_17点击事件
    def read_modbus_data(self):
        """读取Modbus数据"""
        if not self.serial.isOpen():
            self.show_message_on_text_browser("错误: 串口未打开")
            return
            
        try:
            # 获取当前站号
            station_id = self.get_current_station_id()
            
            # 构建Modbus指令: 站号 + 03 20 07 00 02 + CRC校验
            command_data = bytes([
                station_id,  # 站号
                0x03,        # 功能码
                0x20, 0x07,  # 起始地址
                0x00, 0x02   # 寄存器数量
            ])
            
            # 计算CRC校验
            crc = self.calculate_crc(command_data)
            
            # 完整指令
            full_command = command_data + crc
            
            # 显示发送的指令
            hex_display = ' '.join([f"{b:02X}" for b in full_command])
            self.show_message_on_text_browser(f"发送: {hex_display}")
            
            # 发送指令
            self.serial.write(full_command)
            
            # 关键：设置命令类型，以便在receive_data中识别
            self.last_command_type = 'modbus_data'
            
        except Exception as e:
            self.show_message_on_text_browser(f"发送错误: {str(e)}")

    # 添加处理Modbus响应的方法   初始值读取那个功能
    def process_modbus_data_response(self, response):
        """处理Modbus数据响应"""
        try:
            # 验证响应长度
            if len(response) < 9:  # 站号(1) + 功能码(1) + 数据长度(1) + 数据(4) + CRC(2)
                self.show_message_on_text_browser("Modbus响应数据长度不足")
                return
                
            # 验证响应中的站号
            if response[0] != self.get_current_station_id():
                self.show_message_on_text_browser(f"站号不匹配: 预期 {self.get_current_station_id()}, 实际 {response[0]}")
                return
                
            # 验证功能码
            if response[1] != 0x03:
                self.show_message_on_text_browser(f"功能码错误: {response[1]:02X}")
                return
                
            # 验证数据长度
            if response[2] != 0x04:
                self.show_message_on_text_browser(f"数据长度错误: {response[2]:02X}")
                return
                
            # 获取数据部分 (4字节)
            data_bytes = response[3:7]
            
            # 显示原始数据
            hex_str_original = ' '.join([f"{b:02X}" for b in data_bytes])
            #self.show_message_on_text_browser(f"Modbus原始数据: {hex_str_original}")
            
            # 按照2,3,0,1的顺序重排数据字节
            reordered_data_bytes = bytes([
                data_bytes[0],  # 第三个字节
                data_bytes[1],  # 第四个字节
                data_bytes[2],  # 第一个字节
                data_bytes[3]   # 第二个字节
            ])
            
            # 转换为整数值（使用大端格式）
            data_value = int.from_bytes(reordered_data_bytes, byteorder='big', signed=False)
            
            # 显示重排序后的数据和十进制值
            hex_str_reordered = ' '.join([f"{b:02X}" for b in reordered_data_bytes])
            #self.show_message_on_text_browser(f"Modbus重排序数据: {hex_str_reordered}, 十进制值: {data_value}")
            
            # 更新lineEdit显示
            self.lineEdit.setText(str(data_value))
            
            # 注释：这是初始值读取功能，不需要添加到位置曲线图
            # 位置曲线图只在连续位置监控时更新
            
        except Exception as e:
            self.show_message_on_text_browser(f"处理Modbus响应错误: {str(e)}")
        finally:
            # 清除命令类型标记
            if hasattr(self, 'last_command_type'):
                self.last_command_type = None

    # 添加初始化位置曲线图的方法
    def init_position_chart(self):
        """初始化位置曲线图"""
        if not HAS_POSITION_CHART:
            return
        
        # 创建位置曲线图实例
        self.position_chart = position_chart.PositionChart(self)
        
        # 获取位置曲线图控件
        chart_widget = self.position_chart.get_widget()
        
        try:
            # 直接获取tab_3（位置标签页）
            tab_3 = self.tabWidget.widget(2)  # 位置标签页是第3个标签页(索引2)
            
            if tab_3:
                # 直接获取textBrowser_5
                if hasattr(self, 'textBrowser_5'):
                    # 获取其父控件
                    parent = self.textBrowser_5.parent()
                    
                    # 获取当前的布局
                    layout = self.verticalLayout_7  # 根据UI截图，是verticalLayout_7
                    
                    # 获取textBrowser_5在布局中的索引
                    index = -1
                    for i in range(layout.count()):
                        if layout.itemAt(i).widget() == self.textBrowser_5:
                            index = i
                            break
                    
                    if index >= 0:
                        # 移除textBrowser_5
                        layout.removeWidget(self.textBrowser_5)
                        self.textBrowser_5.hide()
                        
                        # 在相同位置添加图表控件
                        layout.insertWidget(index, chart_widget)
                        print("位置曲线图已初始化")
                    else:
                        print("警告: 无法确定textBrowser_5在布局中的位置")
                else:
                    print("警告: 找不到textBrowser_5控件")
            else:
                print("警告: 找不到位置标签页")
        except Exception as e:
            print(f"初始化位置曲线图错误: {str(e)}")
            # 详细记录错误信息
            import traceback
            traceback.print_exc()

if __name__ == "__main__":
    app = QtWidgets.QApplication(sys.argv)
    window = BSLink()
    window.show()
    sys.exit(app.exec_())
