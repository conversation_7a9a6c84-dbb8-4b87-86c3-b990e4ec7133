// qpagesetupdialog.sip generated by MetaSIP
//
// This file is part of the QtPrintSupport Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%If (PyQt_PrintDialog)

class QPageSetupDialog : public QDialog
{
%TypeHeaderCode
#include <qpagesetupdialog.h>
%End

public:
    QPageSetupDialog(QPrinter *printer, QWidget *parent /TransferThis/ = 0);
    explicit QPageSetupDialog(QWidget *parent /TransferThis/ = 0);
    virtual ~QPageSetupDialog();
    virtual void setVisible(bool visible);
    virtual int exec() /PostHook=__pyQtPostEventLoopHook__,PreHook=__pyQtPreEventLoopHook__,PyName=exec_,ReleaseGIL/;
%MethodCode
        // Transfer ownership back to Python (a modal dialog will probably have the
        // main window as it's parent).  This means the Qt dialog will be deleted when
        // the Python wrapper is garbage collected.  Although this is a little
        // inconsistent, it saves having to code it explicitly to avoid the memory
        // leak.
        sipTransferBack(sipSelf);
        
        Py_BEGIN_ALLOW_THREADS
        sipRes = sipSelfWasArg ? sipCpp->QPageSetupDialog::exec()
                               : sipCpp->exec();
        Py_END_ALLOW_THREADS
%End

    virtual int exec() /PostHook=__pyQtPostEventLoopHook__,PreHook=__pyQtPreEventLoopHook__,ReleaseGIL/;
%MethodCode
        // Transfer ownership back to Python (a modal dialog will probably have the
        // main window as it's parent).  This means the Qt dialog will be deleted when
        // the Python wrapper is garbage collected.  Although this is a little
        // inconsistent, it saves having to code it explicitly to avoid the memory
        // leak.
        sipTransferBack(sipSelf);
        
        Py_BEGIN_ALLOW_THREADS
        sipRes = sipSelfWasArg ? sipCpp->QPageSetupDialog::exec()
                               : sipCpp->exec();
        Py_END_ALLOW_THREADS
%End

    virtual void open();
    void open(SIP_PYOBJECT slot /TypeHint="PYQT_SLOT"/);
%MethodCode
        QObject *receiver;
        QByteArray slot_signature;
        
        if ((sipError = pyqt5_qtprintsupport_get_connection_parts(a0, sipCpp, "()", false, &receiver, slot_signature)) == sipErrorNone)
        {
            sipCpp->open(receiver, slot_signature.constData());
        }
        else if (sipError == sipErrorContinue)
        {
            sipError = sipBadCallableArg(0, a0);
        }
%End

    virtual void done(int result);
    QPrinter *printer();
};

%End
