// qimagewriter.sip generated by MetaSIP
//
// This file is part of the QtGui Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QImageWriter
{
%TypeHeaderCode
#include <qimagewriter.h>
%End

public:
    enum ImageWriterError
    {
        UnknownError,
        DeviceError,
        UnsupportedFormatError,
%If (Qt_5_10_0 -)
        InvalidImageError,
%End
    };

    QImageWriter();
    QImageWriter(QIODevice *device, const QByteArray &format);
    QImageWriter(const QString &fileName, const QByteArray &format = QByteArray());
    ~QImageWriter();
    void setFormat(const QByteArray &format);
    QByteArray format() const;
    void setDevice(QIODevice *device);
    QIODevice *device() const;
    void setFileName(const QString &fileName);
    QString fileName() const;
    void setQuality(int quality);
    int quality() const;
    void setGamma(float gamma);
    float gamma() const;
    bool canWrite() const;
    bool write(const QImage &image) /ReleaseGIL/;
    QImageWriter::ImageWriterError error() const;
    QString errorString() const;
    static QList<QByteArray> supportedImageFormats();
    void setText(const QString &key, const QString &text);
    bool supportsOption(QImageIOHandler::ImageOption option) const;
    void setCompression(int compression);
    int compression() const;
%If (Qt_5_1_0 -)
    static QList<QByteArray> supportedMimeTypes();
%End
%If (Qt_5_4_0 -)
    void setSubType(const QByteArray &type);
%End
%If (Qt_5_4_0 -)
    QByteArray subType() const;
%End
%If (Qt_5_4_0 -)
    QList<QByteArray> supportedSubTypes() const;
%End
%If (Qt_5_5_0 -)
    void setOptimizedWrite(bool optimize);
%End
%If (Qt_5_5_0 -)
    bool optimizedWrite() const;
%End
%If (Qt_5_5_0 -)
    void setProgressiveScanWrite(bool progressive);
%End
%If (Qt_5_5_0 -)
    bool progressiveScanWrite() const;
%End
%If (Qt_5_5_0 -)
    QImageIOHandler::Transformations transformation() const;
%End
%If (Qt_5_5_0 -)
    void setTransformation(QImageIOHandler::Transformations orientation);
%End
%If (Qt_5_12_0 -)
    static QList<QByteArray> imageFormatsForMimeType(const QByteArray &mimeType);
%End

private:
    QImageWriter(const QImageWriter &);
};
