// qdbusconnection.sip generated by MetaSIP
//
// This file is part of the QtDBus Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICEN<PERSON> included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


namespace QDBus
{
%TypeHeaderCode
#include <qdbusconnection.h>
%End

    enum CallMode
    {
        NoB<PERSON>,
        Block,
        BlockWithGui,
        AutoDetect,
    };
};

class QDBusConnection
{
%TypeHeaderCode
#include <qdbusconnection.h>
%End

public:
    enum BusType
    {
        SessionBus,
        SystemBus,
        ActivationBus,
    };

    enum RegisterOption
    {
        ExportAdaptors,
        ExportScriptableSlots,
        ExportScriptableSignals,
        ExportScriptableProperties,
        ExportScriptableInvokables,
        ExportScriptableContents,
        ExportNonScriptableSlots,
        ExportNonScriptableSignals,
        ExportNonScriptableProperties,
        ExportNonScriptableInvokables,
        ExportNonScriptableContents,
        ExportAllSlots,
        ExportAllSignals,
        ExportAllProperties,
        ExportAllInvokables,
        ExportAllContents,
        ExportAllSignal,
        ExportChildObjects,
    };

    enum UnregisterMode
    {
        UnregisterNode,
        UnregisterTree,
    };

    typedef QFlags<QDBusConnection::RegisterOption> RegisterOptions;

    enum ConnectionCapability
    {
        UnixFileDescriptorPassing,
    };

    typedef QFlags<QDBusConnection::ConnectionCapability> ConnectionCapabilities;
    explicit QDBusConnection(const QString &name);
    QDBusConnection(const QDBusConnection &other);
    ~QDBusConnection();
    bool isConnected() const;
    QString baseService() const;
    QDBusError lastError() const;
    QString name() const;
    QDBusConnection::ConnectionCapabilities connectionCapabilities() const;
    bool send(const QDBusMessage &message) const;
    bool callWithCallback(const QDBusMessage &message, SIP_PYOBJECT returnMethod /TypeHint="PYQT_SLOT"/, SIP_PYOBJECT errorMethod /TypeHint="PYQT_SLOT"/, int timeout = -1) const;
%MethodCode
        QObject *receiver;
        QByteArray return_slot;
        
        if ((sipError = pyqt5_qtdbus_get_pyqtslot_parts(a1, &receiver, return_slot)) == sipErrorNone)
        {
            QObject *error_receiver;
            QByteArray error_slot;
        
            if ((sipError = pyqt5_qtdbus_get_pyqtslot_parts(a2, &error_receiver, error_slot)) == sipErrorNone)
            {
                if (receiver == error_receiver)
                {
                    sipRes = sipCpp->callWithCallback(*a0, receiver, return_slot.constData(), error_slot.constData(), a3);
                }
                else
                {
                    PyErr_SetString(PyExc_ValueError,
                            "the return and error methods must be bound to the same QObject instance");
                    sipError = sipErrorFail;
                }
            }
            else if (sipError == sipErrorContinue)
            {
                sipError = sipBadCallableArg(2, a2);
            }
        }
        else if (sipError == sipErrorContinue)
        {
            sipError = sipBadCallableArg(1, a1);
        }
%End

    QDBusMessage call(const QDBusMessage &message, QDBus::CallMode mode = QDBus::Block, int timeout = -1) const /ReleaseGIL/;
    QDBusPendingCall asyncCall(const QDBusMessage &message, int timeout = -1) const;
    bool connect(const QString &service, const QString &path, const QString &interface, const QString &name, SIP_PYOBJECT slot /TypeHint="PYQT_SLOT"/) /ReleaseGIL/;
%MethodCode
        QObject *receiver;
        QByteArray slot;
        
        if ((sipError = pyqt5_qtdbus_get_pyqtslot_parts(a4, &receiver, slot)) == sipErrorNone)
        {
            Py_BEGIN_ALLOW_THREADS
            sipRes = sipCpp->connect(*a0, *a1, *a2, *a3, receiver, slot.constData());
            Py_END_ALLOW_THREADS
        }
        else if (sipError == sipErrorContinue)
        {
            sipError = sipBadCallableArg(4, a4);
        }
%End

    bool connect(const QString &service, const QString &path, const QString &interface, const QString &name, const QString &signature, SIP_PYOBJECT slot /TypeHint="PYQT_SLOT"/) /ReleaseGIL/;
%MethodCode
        QObject *receiver;
        QByteArray slot;
        
        if ((sipError = pyqt5_qtdbus_get_pyqtslot_parts(a5, &receiver, slot)) == sipErrorNone)
        {
            Py_BEGIN_ALLOW_THREADS
            sipRes = sipCpp->connect(*a0, *a1, *a2, *a3, *a4, receiver, slot.constData());
            Py_END_ALLOW_THREADS
        }
        else if (sipError == sipErrorContinue)
        {
            sipError = sipBadCallableArg(5, a5);
        }
%End

    bool connect(const QString &service, const QString &path, const QString &interface, const QString &name, const QStringList &argumentMatch, const QString &signature, SIP_PYOBJECT slot /TypeHint="PYQT_SLOT"/) /ReleaseGIL/;
%MethodCode
        QObject *receiver;
        QByteArray slot;
        
        if ((sipError = pyqt5_qtdbus_get_pyqtslot_parts(a6, &receiver, slot)) == sipErrorNone)
        {
            Py_BEGIN_ALLOW_THREADS
            sipRes = sipCpp->connect(*a0, *a1, *a2, *a3, *a4, *a5, receiver, slot.constData());
            Py_END_ALLOW_THREADS
        }
        else if (sipError == sipErrorContinue)
        {
            sipError = sipBadCallableArg(6, a6);
        }
%End

    bool disconnect(const QString &service, const QString &path, const QString &interface, const QString &name, SIP_PYOBJECT slot /TypeHint="PYQT_SLOT"/) /ReleaseGIL/;
%MethodCode
        QObject *receiver;
        QByteArray slot;
        
        if ((sipError = pyqt5_qtdbus_get_pyqtslot_parts(a4, &receiver, slot)) == sipErrorNone)
        {
            Py_BEGIN_ALLOW_THREADS
            sipRes = sipCpp->disconnect(*a0, *a1, *a2, *a3, receiver, slot.constData());
            Py_END_ALLOW_THREADS
        }
        else if (sipError == sipErrorContinue)
        {
            sipError = sipBadCallableArg(4, a4);
        }
%End

    bool disconnect(const QString &service, const QString &path, const QString &interface, const QString &name, const QString &signature, SIP_PYOBJECT slot /TypeHint="PYQT_SLOT"/) /ReleaseGIL/;
%MethodCode
        QObject *receiver;
        QByteArray slot;
        
        if ((sipError = pyqt5_qtdbus_get_pyqtslot_parts(a5, &receiver, slot)) == sipErrorNone)
        {
            Py_BEGIN_ALLOW_THREADS
            sipRes = sipCpp->disconnect(*a0, *a1, *a2, *a3, *a4, receiver, slot.constData());
            Py_END_ALLOW_THREADS
        }
        else if (sipError == sipErrorContinue)
        {
            sipError = sipBadCallableArg(5, a5);
        }
%End

    bool disconnect(const QString &service, const QString &path, const QString &interface, const QString &name, const QStringList &argumentMatch, const QString &signature, SIP_PYOBJECT slot /TypeHint="PYQT_SLOT"/) /ReleaseGIL/;
%MethodCode
        QObject *receiver;
        QByteArray slot;
        
        if ((sipError = pyqt5_qtdbus_get_pyqtslot_parts(a6, &receiver, slot)) == sipErrorNone)
        {
            Py_BEGIN_ALLOW_THREADS
            sipRes = sipCpp->disconnect(*a0, *a1, *a2, *a3, *a4, *a5, receiver, slot.constData());
            Py_END_ALLOW_THREADS
        }
        else if (sipError == sipErrorContinue)
        {
            sipError = sipBadCallableArg(6, a6);
        }
%End

    bool registerObject(const QString &path, QObject *object, QDBusConnection::RegisterOptions options = QDBusConnection::ExportAdaptors);
%If (Qt_5_5_0 -)
    bool registerObject(const QString &path, const QString &interface, QObject *object, QDBusConnection::RegisterOptions options = QDBusConnection::ExportAdaptors);
%End
    void unregisterObject(const QString &path, QDBusConnection::UnregisterMode mode = QDBusConnection::UnregisterNode);
    QObject *objectRegisteredAt(const QString &path) const;
    bool registerService(const QString &serviceName);
    bool unregisterService(const QString &serviceName);
    QDBusConnectionInterface *interface() const;
    static QDBusConnection connectToBus(QDBusConnection::BusType type, const QString &name) /ReleaseGIL/;
    static QDBusConnection connectToBus(const QString &address, const QString &name) /ReleaseGIL/;
    static QDBusConnection connectToPeer(const QString &address, const QString &name) /ReleaseGIL/;
    static void disconnectFromBus(const QString &name) /ReleaseGIL/;
    static void disconnectFromPeer(const QString &name) /ReleaseGIL/;
    static QByteArray localMachineId();
    static QDBusConnection sessionBus();
    static QDBusConnection systemBus();
    static QDBusConnection sender();
%If (Qt_5_6_0 -)
    void swap(QDBusConnection &other /Constrained/);
%End
};

QFlags<QDBusConnection::RegisterOption> operator|(QDBusConnection::RegisterOption f1, QFlags<QDBusConnection::RegisterOption> f2);
QFlags<QDBusConnection::RegisterOption> operator|(QDBusConnection::RegisterOption f1, QDBusConnection::RegisterOption f2);
%If (Qt_5_6_0 -)
QFlags<QDBusConnection::ConnectionCapability> operator|(QDBusConnection::ConnectionCapability f1, QFlags<QDBusConnection::ConnectionCapability> f2);
%End
