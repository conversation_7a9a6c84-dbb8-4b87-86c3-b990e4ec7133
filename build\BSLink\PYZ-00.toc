('D:\\wenjian\\BS-Link\\build\\BSLink\\PYZ-00.pyz',
 [('BSLink_ui', 'D:\\wenjian\\BS-Link\\BSLink_ui.py', 'PYMODULE'),
  ('PyQt5',
   'D:\\wenjian\\BS-Link\\.venv\\lib\\site-packages\\PyQt5\\__init__.py',
   'PYMODULE'),
  ('PyQt5.Qt5', '-', 'PYMODULE'),
  ('PyQt5.pylupdate_main',
   'D:\\wenjian\\BS-Link\\.venv\\lib\\site-packages\\PyQt5\\pylupdate_main.py',
   'PYMODULE'),
  ('PyQt5.pyrcc_main',
   'D:\\wenjian\\BS-Link\\.venv\\lib\\site-packages\\PyQt5\\pyrcc_main.py',
   'PYMODULE'),
  ('PyQt5.uic',
   'D:\\wenjian\\BS-Link\\.venv\\lib\\site-packages\\PyQt5\\uic\\__init__.py',
   'PYMODULE'),
  ('PyQt5.uic.Compiler',
   'D:\\wenjian\\BS-Link\\.venv\\lib\\site-packages\\PyQt5\\uic\\Compiler\\__init__.py',
   'PYMODULE'),
  ('PyQt5.uic.Compiler.compiler',
   'D:\\wenjian\\BS-Link\\.venv\\lib\\site-packages\\PyQt5\\uic\\Compiler\\compiler.py',
   'PYMODULE'),
  ('PyQt5.uic.Compiler.indenter',
   'D:\\wenjian\\BS-Link\\.venv\\lib\\site-packages\\PyQt5\\uic\\Compiler\\indenter.py',
   'PYMODULE'),
  ('PyQt5.uic.Compiler.misc',
   'D:\\wenjian\\BS-Link\\.venv\\lib\\site-packages\\PyQt5\\uic\\Compiler\\misc.py',
   'PYMODULE'),
  ('PyQt5.uic.Compiler.proxy_metaclass',
   'D:\\wenjian\\BS-Link\\.venv\\lib\\site-packages\\PyQt5\\uic\\Compiler\\proxy_metaclass.py',
   'PYMODULE'),
  ('PyQt5.uic.Compiler.qobjectcreator',
   'D:\\wenjian\\BS-Link\\.venv\\lib\\site-packages\\PyQt5\\uic\\Compiler\\qobjectcreator.py',
   'PYMODULE'),
  ('PyQt5.uic.Compiler.qtproxies',
   'D:\\wenjian\\BS-Link\\.venv\\lib\\site-packages\\PyQt5\\uic\\Compiler\\qtproxies.py',
   'PYMODULE'),
  ('PyQt5.uic.Loader',
   'D:\\wenjian\\BS-Link\\.venv\\lib\\site-packages\\PyQt5\\uic\\Loader\\__init__.py',
   'PYMODULE'),
  ('PyQt5.uic.Loader.loader',
   'D:\\wenjian\\BS-Link\\.venv\\lib\\site-packages\\PyQt5\\uic\\Loader\\loader.py',
   'PYMODULE'),
  ('PyQt5.uic.Loader.qobjectcreator',
   'D:\\wenjian\\BS-Link\\.venv\\lib\\site-packages\\PyQt5\\uic\\Loader\\qobjectcreator.py',
   'PYMODULE'),
  ('PyQt5.uic.driver',
   'D:\\wenjian\\BS-Link\\.venv\\lib\\site-packages\\PyQt5\\uic\\driver.py',
   'PYMODULE'),
  ('PyQt5.uic.exceptions',
   'D:\\wenjian\\BS-Link\\.venv\\lib\\site-packages\\PyQt5\\uic\\exceptions.py',
   'PYMODULE'),
  ('PyQt5.uic.icon_cache',
   'D:\\wenjian\\BS-Link\\.venv\\lib\\site-packages\\PyQt5\\uic\\icon_cache.py',
   'PYMODULE'),
  ('PyQt5.uic.objcreator',
   'D:\\wenjian\\BS-Link\\.venv\\lib\\site-packages\\PyQt5\\uic\\objcreator.py',
   'PYMODULE'),
  ('PyQt5.uic.port_v3',
   'D:\\wenjian\\BS-Link\\.venv\\lib\\site-packages\\PyQt5\\uic\\port_v3\\__init__.py',
   'PYMODULE'),
  ('PyQt5.uic.port_v3.as_string',
   'D:\\wenjian\\BS-Link\\.venv\\lib\\site-packages\\PyQt5\\uic\\port_v3\\as_string.py',
   'PYMODULE'),
  ('PyQt5.uic.port_v3.ascii_upper',
   'D:\\wenjian\\BS-Link\\.venv\\lib\\site-packages\\PyQt5\\uic\\port_v3\\ascii_upper.py',
   'PYMODULE'),
  ('PyQt5.uic.port_v3.proxy_base',
   'D:\\wenjian\\BS-Link\\.venv\\lib\\site-packages\\PyQt5\\uic\\port_v3\\proxy_base.py',
   'PYMODULE'),
  ('PyQt5.uic.port_v3.string_io',
   'D:\\wenjian\\BS-Link\\.venv\\lib\\site-packages\\PyQt5\\uic\\port_v3\\string_io.py',
   'PYMODULE'),
  ('PyQt5.uic.properties',
   'D:\\wenjian\\BS-Link\\.venv\\lib\\site-packages\\PyQt5\\uic\\properties.py',
   'PYMODULE'),
  ('PyQt5.uic.pyuic',
   'D:\\wenjian\\BS-Link\\.venv\\lib\\site-packages\\PyQt5\\uic\\pyuic.py',
   'PYMODULE'),
  ('PyQt5.uic.uiparser',
   'D:\\wenjian\\BS-Link\\.venv\\lib\\site-packages\\PyQt5\\uic\\uiparser.py',
   'PYMODULE'),
  ('__future__', 'C:\\Program Files\\Python38\\lib\\__future__.py', 'PYMODULE'),
  ('_compat_pickle',
   'C:\\Program Files\\Python38\\lib\\_compat_pickle.py',
   'PYMODULE'),
  ('_compression',
   'C:\\Program Files\\Python38\\lib\\_compression.py',
   'PYMODULE'),
  ('_py_abc', 'C:\\Program Files\\Python38\\lib\\_py_abc.py', 'PYMODULE'),
  ('_pyi_rth_utils',
   'D:\\wenjian\\BS-Link\\.venv\\lib\\site-packages\\PyInstaller\\fake-modules\\_pyi_rth_utils\\__init__.py',
   'PYMODULE'),
  ('_pyi_rth_utils.qt',
   'D:\\wenjian\\BS-Link\\.venv\\lib\\site-packages\\PyInstaller\\fake-modules\\_pyi_rth_utils\\qt.py',
   'PYMODULE'),
  ('_strptime', 'C:\\Program Files\\Python38\\lib\\_strptime.py', 'PYMODULE'),
  ('_threading_local',
   'C:\\Program Files\\Python38\\lib\\_threading_local.py',
   'PYMODULE'),
  ('argparse', 'C:\\Program Files\\Python38\\lib\\argparse.py', 'PYMODULE'),
  ('ast', 'C:\\Program Files\\Python38\\lib\\ast.py', 'PYMODULE'),
  ('base64', 'C:\\Program Files\\Python38\\lib\\base64.py', 'PYMODULE'),
  ('bisect', 'C:\\Program Files\\Python38\\lib\\bisect.py', 'PYMODULE'),
  ('bz2', 'C:\\Program Files\\Python38\\lib\\bz2.py', 'PYMODULE'),
  ('calendar', 'C:\\Program Files\\Python38\\lib\\calendar.py', 'PYMODULE'),
  ('configparser',
   'C:\\Program Files\\Python38\\lib\\configparser.py',
   'PYMODULE'),
  ('contextlib', 'C:\\Program Files\\Python38\\lib\\contextlib.py', 'PYMODULE'),
  ('copy', 'C:\\Program Files\\Python38\\lib\\copy.py', 'PYMODULE'),
  ('csv', 'C:\\Program Files\\Python38\\lib\\csv.py', 'PYMODULE'),
  ('ctypes',
   'C:\\Program Files\\Python38\\lib\\ctypes\\__init__.py',
   'PYMODULE'),
  ('ctypes._endian',
   'C:\\Program Files\\Python38\\lib\\ctypes\\_endian.py',
   'PYMODULE'),
  ('ctypes.wintypes',
   'C:\\Program Files\\Python38\\lib\\ctypes\\wintypes.py',
   'PYMODULE'),
  ('datetime', 'C:\\Program Files\\Python38\\lib\\datetime.py', 'PYMODULE'),
  ('dis', 'C:\\Program Files\\Python38\\lib\\dis.py', 'PYMODULE'),
  ('email', 'C:\\Program Files\\Python38\\lib\\email\\__init__.py', 'PYMODULE'),
  ('email._encoded_words',
   'C:\\Program Files\\Python38\\lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('email._header_value_parser',
   'C:\\Program Files\\Python38\\lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('email._parseaddr',
   'C:\\Program Files\\Python38\\lib\\email\\_parseaddr.py',
   'PYMODULE'),
  ('email._policybase',
   'C:\\Program Files\\Python38\\lib\\email\\_policybase.py',
   'PYMODULE'),
  ('email.base64mime',
   'C:\\Program Files\\Python38\\lib\\email\\base64mime.py',
   'PYMODULE'),
  ('email.charset',
   'C:\\Program Files\\Python38\\lib\\email\\charset.py',
   'PYMODULE'),
  ('email.contentmanager',
   'C:\\Program Files\\Python38\\lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.encoders',
   'C:\\Program Files\\Python38\\lib\\email\\encoders.py',
   'PYMODULE'),
  ('email.errors',
   'C:\\Program Files\\Python38\\lib\\email\\errors.py',
   'PYMODULE'),
  ('email.feedparser',
   'C:\\Program Files\\Python38\\lib\\email\\feedparser.py',
   'PYMODULE'),
  ('email.generator',
   'C:\\Program Files\\Python38\\lib\\email\\generator.py',
   'PYMODULE'),
  ('email.header',
   'C:\\Program Files\\Python38\\lib\\email\\header.py',
   'PYMODULE'),
  ('email.headerregistry',
   'C:\\Program Files\\Python38\\lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email.iterators',
   'C:\\Program Files\\Python38\\lib\\email\\iterators.py',
   'PYMODULE'),
  ('email.message',
   'C:\\Program Files\\Python38\\lib\\email\\message.py',
   'PYMODULE'),
  ('email.parser',
   'C:\\Program Files\\Python38\\lib\\email\\parser.py',
   'PYMODULE'),
  ('email.policy',
   'C:\\Program Files\\Python38\\lib\\email\\policy.py',
   'PYMODULE'),
  ('email.quoprimime',
   'C:\\Program Files\\Python38\\lib\\email\\quoprimime.py',
   'PYMODULE'),
  ('email.utils',
   'C:\\Program Files\\Python38\\lib\\email\\utils.py',
   'PYMODULE'),
  ('fnmatch', 'C:\\Program Files\\Python38\\lib\\fnmatch.py', 'PYMODULE'),
  ('ftplib', 'C:\\Program Files\\Python38\\lib\\ftplib.py', 'PYMODULE'),
  ('getopt', 'C:\\Program Files\\Python38\\lib\\getopt.py', 'PYMODULE'),
  ('getpass', 'C:\\Program Files\\Python38\\lib\\getpass.py', 'PYMODULE'),
  ('gettext', 'C:\\Program Files\\Python38\\lib\\gettext.py', 'PYMODULE'),
  ('glob', 'C:\\Program Files\\Python38\\lib\\glob.py', 'PYMODULE'),
  ('gzip', 'C:\\Program Files\\Python38\\lib\\gzip.py', 'PYMODULE'),
  ('hashlib', 'C:\\Program Files\\Python38\\lib\\hashlib.py', 'PYMODULE'),
  ('http', 'C:\\Program Files\\Python38\\lib\\http\\__init__.py', 'PYMODULE'),
  ('http.client',
   'C:\\Program Files\\Python38\\lib\\http\\client.py',
   'PYMODULE'),
  ('http.cookiejar',
   'C:\\Program Files\\Python38\\lib\\http\\cookiejar.py',
   'PYMODULE'),
  ('importlib',
   'C:\\Program Files\\Python38\\lib\\importlib\\__init__.py',
   'PYMODULE'),
  ('importlib._bootstrap',
   'C:\\Program Files\\Python38\\lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   'C:\\Program Files\\Python38\\lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.abc',
   'C:\\Program Files\\Python38\\lib\\importlib\\abc.py',
   'PYMODULE'),
  ('importlib.machinery',
   'C:\\Program Files\\Python38\\lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('importlib.metadata',
   'C:\\Program Files\\Python38\\lib\\importlib\\metadata.py',
   'PYMODULE'),
  ('importlib.util',
   'C:\\Program Files\\Python38\\lib\\importlib\\util.py',
   'PYMODULE'),
  ('inspect', 'C:\\Program Files\\Python38\\lib\\inspect.py', 'PYMODULE'),
  ('logging',
   'C:\\Program Files\\Python38\\lib\\logging\\__init__.py',
   'PYMODULE'),
  ('lzma', 'C:\\Program Files\\Python38\\lib\\lzma.py', 'PYMODULE'),
  ('mimetypes', 'C:\\Program Files\\Python38\\lib\\mimetypes.py', 'PYMODULE'),
  ('netrc', 'C:\\Program Files\\Python38\\lib\\netrc.py', 'PYMODULE'),
  ('nturl2path', 'C:\\Program Files\\Python38\\lib\\nturl2path.py', 'PYMODULE'),
  ('numbers', 'C:\\Program Files\\Python38\\lib\\numbers.py', 'PYMODULE'),
  ('opcode', 'C:\\Program Files\\Python38\\lib\\opcode.py', 'PYMODULE'),
  ('optparse', 'C:\\Program Files\\Python38\\lib\\optparse.py', 'PYMODULE'),
  ('pathlib', 'C:\\Program Files\\Python38\\lib\\pathlib.py', 'PYMODULE'),
  ('pickle', 'C:\\Program Files\\Python38\\lib\\pickle.py', 'PYMODULE'),
  ('pkgutil', 'C:\\Program Files\\Python38\\lib\\pkgutil.py', 'PYMODULE'),
  ('platform', 'C:\\Program Files\\Python38\\lib\\platform.py', 'PYMODULE'),
  ('pprint', 'C:\\Program Files\\Python38\\lib\\pprint.py', 'PYMODULE'),
  ('py_compile', 'C:\\Program Files\\Python38\\lib\\py_compile.py', 'PYMODULE'),
  ('queue', 'C:\\Program Files\\Python38\\lib\\queue.py', 'PYMODULE'),
  ('quopri', 'C:\\Program Files\\Python38\\lib\\quopri.py', 'PYMODULE'),
  ('random', 'C:\\Program Files\\Python38\\lib\\random.py', 'PYMODULE'),
  ('selectors', 'C:\\Program Files\\Python38\\lib\\selectors.py', 'PYMODULE'),
  ('serial',
   'D:\\wenjian\\BS-Link\\.venv\\lib\\site-packages\\serial\\__init__.py',
   'PYMODULE'),
  ('serial.__main__',
   'D:\\wenjian\\BS-Link\\.venv\\lib\\site-packages\\serial\\__main__.py',
   'PYMODULE'),
  ('serial.rfc2217',
   'D:\\wenjian\\BS-Link\\.venv\\lib\\site-packages\\serial\\rfc2217.py',
   'PYMODULE'),
  ('serial.rs485',
   'D:\\wenjian\\BS-Link\\.venv\\lib\\site-packages\\serial\\rs485.py',
   'PYMODULE'),
  ('serial.serialcli',
   'D:\\wenjian\\BS-Link\\.venv\\lib\\site-packages\\serial\\serialcli.py',
   'PYMODULE'),
  ('serial.serialjava',
   'D:\\wenjian\\BS-Link\\.venv\\lib\\site-packages\\serial\\serialjava.py',
   'PYMODULE'),
  ('serial.serialposix',
   'D:\\wenjian\\BS-Link\\.venv\\lib\\site-packages\\serial\\serialposix.py',
   'PYMODULE'),
  ('serial.serialutil',
   'D:\\wenjian\\BS-Link\\.venv\\lib\\site-packages\\serial\\serialutil.py',
   'PYMODULE'),
  ('serial.serialwin32',
   'D:\\wenjian\\BS-Link\\.venv\\lib\\site-packages\\serial\\serialwin32.py',
   'PYMODULE'),
  ('serial.threaded',
   'D:\\wenjian\\BS-Link\\.venv\\lib\\site-packages\\serial\\threaded\\__init__.py',
   'PYMODULE'),
  ('serial.tools',
   'D:\\wenjian\\BS-Link\\.venv\\lib\\site-packages\\serial\\tools\\__init__.py',
   'PYMODULE'),
  ('serial.tools.hexlify_codec',
   'D:\\wenjian\\BS-Link\\.venv\\lib\\site-packages\\serial\\tools\\hexlify_codec.py',
   'PYMODULE'),
  ('serial.tools.list_ports',
   'D:\\wenjian\\BS-Link\\.venv\\lib\\site-packages\\serial\\tools\\list_ports.py',
   'PYMODULE'),
  ('serial.tools.list_ports_common',
   'D:\\wenjian\\BS-Link\\.venv\\lib\\site-packages\\serial\\tools\\list_ports_common.py',
   'PYMODULE'),
  ('serial.tools.list_ports_linux',
   'D:\\wenjian\\BS-Link\\.venv\\lib\\site-packages\\serial\\tools\\list_ports_linux.py',
   'PYMODULE'),
  ('serial.tools.list_ports_osx',
   'D:\\wenjian\\BS-Link\\.venv\\lib\\site-packages\\serial\\tools\\list_ports_osx.py',
   'PYMODULE'),
  ('serial.tools.list_ports_posix',
   'D:\\wenjian\\BS-Link\\.venv\\lib\\site-packages\\serial\\tools\\list_ports_posix.py',
   'PYMODULE'),
  ('serial.tools.list_ports_windows',
   'D:\\wenjian\\BS-Link\\.venv\\lib\\site-packages\\serial\\tools\\list_ports_windows.py',
   'PYMODULE'),
  ('serial.tools.miniterm',
   'D:\\wenjian\\BS-Link\\.venv\\lib\\site-packages\\serial\\tools\\miniterm.py',
   'PYMODULE'),
  ('serial.urlhandler',
   'D:\\wenjian\\BS-Link\\.venv\\lib\\site-packages\\serial\\urlhandler\\__init__.py',
   'PYMODULE'),
  ('serial.urlhandler.protocol_alt',
   'D:\\wenjian\\BS-Link\\.venv\\lib\\site-packages\\serial\\urlhandler\\protocol_alt.py',
   'PYMODULE'),
  ('serial.urlhandler.protocol_cp2110',
   'D:\\wenjian\\BS-Link\\.venv\\lib\\site-packages\\serial\\urlhandler\\protocol_cp2110.py',
   'PYMODULE'),
  ('serial.urlhandler.protocol_hwgrep',
   'D:\\wenjian\\BS-Link\\.venv\\lib\\site-packages\\serial\\urlhandler\\protocol_hwgrep.py',
   'PYMODULE'),
  ('serial.urlhandler.protocol_loop',
   'D:\\wenjian\\BS-Link\\.venv\\lib\\site-packages\\serial\\urlhandler\\protocol_loop.py',
   'PYMODULE'),
  ('serial.urlhandler.protocol_rfc2217',
   'D:\\wenjian\\BS-Link\\.venv\\lib\\site-packages\\serial\\urlhandler\\protocol_rfc2217.py',
   'PYMODULE'),
  ('serial.urlhandler.protocol_socket',
   'D:\\wenjian\\BS-Link\\.venv\\lib\\site-packages\\serial\\urlhandler\\protocol_socket.py',
   'PYMODULE'),
  ('serial.urlhandler.protocol_spy',
   'D:\\wenjian\\BS-Link\\.venv\\lib\\site-packages\\serial\\urlhandler\\protocol_spy.py',
   'PYMODULE'),
  ('serial.win32',
   'D:\\wenjian\\BS-Link\\.venv\\lib\\site-packages\\serial\\win32.py',
   'PYMODULE'),
  ('shlex', 'C:\\Program Files\\Python38\\lib\\shlex.py', 'PYMODULE'),
  ('shutil', 'C:\\Program Files\\Python38\\lib\\shutil.py', 'PYMODULE'),
  ('signal', 'C:\\Program Files\\Python38\\lib\\signal.py', 'PYMODULE'),
  ('socket', 'C:\\Program Files\\Python38\\lib\\socket.py', 'PYMODULE'),
  ('ssl', 'C:\\Program Files\\Python38\\lib\\ssl.py', 'PYMODULE'),
  ('string', 'C:\\Program Files\\Python38\\lib\\string.py', 'PYMODULE'),
  ('stringprep', 'C:\\Program Files\\Python38\\lib\\stringprep.py', 'PYMODULE'),
  ('subprocess', 'C:\\Program Files\\Python38\\lib\\subprocess.py', 'PYMODULE'),
  ('tarfile', 'C:\\Program Files\\Python38\\lib\\tarfile.py', 'PYMODULE'),
  ('tempfile', 'C:\\Program Files\\Python38\\lib\\tempfile.py', 'PYMODULE'),
  ('textwrap', 'C:\\Program Files\\Python38\\lib\\textwrap.py', 'PYMODULE'),
  ('threading', 'C:\\Program Files\\Python38\\lib\\threading.py', 'PYMODULE'),
  ('token', 'C:\\Program Files\\Python38\\lib\\token.py', 'PYMODULE'),
  ('tokenize', 'C:\\Program Files\\Python38\\lib\\tokenize.py', 'PYMODULE'),
  ('tracemalloc',
   'C:\\Program Files\\Python38\\lib\\tracemalloc.py',
   'PYMODULE'),
  ('typing', 'C:\\Program Files\\Python38\\lib\\typing.py', 'PYMODULE'),
  ('urllib',
   'C:\\Program Files\\Python38\\lib\\urllib\\__init__.py',
   'PYMODULE'),
  ('urllib.error',
   'C:\\Program Files\\Python38\\lib\\urllib\\error.py',
   'PYMODULE'),
  ('urllib.parse',
   'C:\\Program Files\\Python38\\lib\\urllib\\parse.py',
   'PYMODULE'),
  ('urllib.request',
   'C:\\Program Files\\Python38\\lib\\urllib\\request.py',
   'PYMODULE'),
  ('urllib.response',
   'C:\\Program Files\\Python38\\lib\\urllib\\response.py',
   'PYMODULE'),
  ('uu', 'C:\\Program Files\\Python38\\lib\\uu.py', 'PYMODULE'),
  ('xml', 'C:\\Program Files\\Python38\\lib\\xml\\__init__.py', 'PYMODULE'),
  ('xml.etree',
   'C:\\Program Files\\Python38\\lib\\xml\\etree\\__init__.py',
   'PYMODULE'),
  ('xml.etree.ElementInclude',
   'C:\\Program Files\\Python38\\lib\\xml\\etree\\ElementInclude.py',
   'PYMODULE'),
  ('xml.etree.ElementPath',
   'C:\\Program Files\\Python38\\lib\\xml\\etree\\ElementPath.py',
   'PYMODULE'),
  ('xml.etree.ElementTree',
   'C:\\Program Files\\Python38\\lib\\xml\\etree\\ElementTree.py',
   'PYMODULE'),
  ('xml.etree.cElementTree',
   'C:\\Program Files\\Python38\\lib\\xml\\etree\\cElementTree.py',
   'PYMODULE'),
  ('xml.parsers',
   'C:\\Program Files\\Python38\\lib\\xml\\parsers\\__init__.py',
   'PYMODULE'),
  ('xml.parsers.expat',
   'C:\\Program Files\\Python38\\lib\\xml\\parsers\\expat.py',
   'PYMODULE'),
  ('xml.sax',
   'C:\\Program Files\\Python38\\lib\\xml\\sax\\__init__.py',
   'PYMODULE'),
  ('xml.sax._exceptions',
   'C:\\Program Files\\Python38\\lib\\xml\\sax\\_exceptions.py',
   'PYMODULE'),
  ('xml.sax.expatreader',
   'C:\\Program Files\\Python38\\lib\\xml\\sax\\expatreader.py',
   'PYMODULE'),
  ('xml.sax.handler',
   'C:\\Program Files\\Python38\\lib\\xml\\sax\\handler.py',
   'PYMODULE'),
  ('xml.sax.saxutils',
   'C:\\Program Files\\Python38\\lib\\xml\\sax\\saxutils.py',
   'PYMODULE'),
  ('xml.sax.xmlreader',
   'C:\\Program Files\\Python38\\lib\\xml\\sax\\xmlreader.py',
   'PYMODULE'),
  ('zipfile', 'C:\\Program Files\\Python38\\lib\\zipfile.py', 'PYMODULE'),
  ('zipimport', 'C:\\Program Files\\Python38\\lib\\zipimport.py', 'PYMODULE')])
