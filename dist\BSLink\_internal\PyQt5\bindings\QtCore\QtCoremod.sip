// QtCoremod.sip generated by MetaSIP
//
// This file is part of the QtCore Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%Module(name=PyQt5.QtCore, call_super_init=True, default_VirtualErrorHandler=PyQt5, keyword_arguments="Optional", use_limited_api=True, py_ssize_t_clean=True)

%Timeline {Qt_5_0_0 Qt_5_0_1 Qt_5_0_2 Qt_5_1_0 Qt_5_1_1 Qt_5_2_0 Qt_5_2_1 Qt_5_3_0 Qt_5_3_1 Qt_5_3_2 Qt_5_4_0 Qt_5_4_1 Qt_5_4_2 Qt_5_5_0 Qt_5_5_1 Qt_5_6_0 Qt_5_6_1 Qt_5_6_2 Qt_5_6_3 Qt_5_6_4 Qt_5_6_5 Qt_5_6_6 Qt_5_6_7 Qt_5_6_8 Qt_5_6_9 Qt_5_7_0 Qt_5_7_1 Qt_5_8_0 Qt_5_8_1 Qt_5_9_0 Qt_5_9_1 Qt_5_9_2 Qt_5_9_3 Qt_5_9_4 Qt_5_9_5 Qt_5_9_6 Qt_5_9_7 Qt_5_9_8 Qt_5_9_9 Qt_5_10_0 Qt_5_10_1 Qt_5_11_0 Qt_5_11_1 Qt_5_11_2 Qt_5_11_3 Qt_5_12_0 Qt_5_12_1 Qt_5_12_2 Qt_5_12_3 Qt_5_12_4 Qt_5_13_0 Qt_5_14_0 Qt_5_15_0 Qt_5_15_1 Qt_5_15_2}

%Platforms {WS_X11 WS_WIN WS_MACX}

%Feature PyQt_Accessibility
%Feature PyQt_SessionManager
%Feature PyQt_SSL
%Feature PyQt_qreal_double
%Feature Py_v3
%Feature PyQt_PrintDialog
%Feature PyQt_Printer
%Feature PyQt_PrintPreviewWidget
%Feature PyQt_PrintPreviewDialog
%Feature PyQt_RawFont
%Feature PyQt_OpenGL
%Feature PyQt_Desktop_OpenGL
%Feature PyQt_NotBootstrapped
%Feature PyQt_Process
%Feature PyQt_MacOSXOnly
%Feature PyQt_WebChannel
%Feature PyQt_CONSTEXPR

%Copying
Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>

This file is part of PyQt5.

This file may be used under the terms of the GNU General Public License
version 3.0 as published by the Free Software Foundation and appearing in
the file LICENSE included in the packaging of this file.  Please review the
following information to ensure the GNU General Public License version 3.0
requirements will be met: http://www.gnu.org/copyleft/gpl.html.

If you do not wish to use this file under the terms of the GPL version 3.0
then you may purchase a commercial license.  For more information contact
<EMAIL>.

This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.
%End

%Plugin PyQt5

%If (Py_v3)
%DefaultEncoding "ASCII"
%End

%If (!Py_v3)
// Hook into the VendorID package if it is enabled.
%Feature VendorID

%If (VendorID)

%ModuleCode
#include <vendorid.h>
%End

%PreInitialisationCode
    if (!vendorid_check())
    {
        PyErr_SetString(PyExc_RuntimeError, "PyQt cannot be used with this Python interpreter");
        return;
    }
%End

%End

%End


%Include(name=pyqt-internal.sip5, optional=True)
%Include(name=pyqt-gpl.sip5, optional=True)
%Include(name=pyqt-commercial.sip5, optional=True)

%DefaultSupertype sip.simplewrapper

%Include qglobal.sip
%Include qnamespace.sip
%Include qabstractanimation.sip
%Include qabstracteventdispatcher.sip
%Include qabstractitemmodel.sip
%Include qabstractnativeeventfilter.sip
%Include qabstractproxymodel.sip
%Include qabstractstate.sip
%Include qabstracttransition.sip
%Include qanimationgroup.sip
%Include qbasictimer.sip
%Include qbitarray.sip
%Include qbuffer.sip
%Include qbytearray.sip
%Include qbytearraymatcher.sip
%Include qcalendar.sip
%Include qcborcommon.sip
%Include qcborstream.sip
%Include qchar.sip
%Include qcollator.sip
%Include qcommandlineoption.sip
%Include qcommandlineparser.sip
%Include qconcatenatetablesproxymodel.sip
%Include qcoreapplication.sip
%Include qcoreevent.sip
%Include qcryptographichash.sip
%Include qdatastream.sip
%Include qdatetime.sip
%Include qdeadlinetimer.sip
%Include qdir.sip
%Include qdiriterator.sip
%Include qeasingcurve.sip
%Include qelapsedtimer.sip
%Include qeventloop.sip
%Include qeventtransition.sip
%Include qfile.sip
%Include qfiledevice.sip
%Include qfileinfo.sip
%Include qfileselector.sip
%Include qfilesystemwatcher.sip
%Include qfinalstate.sip
%Include qhistorystate.sip
%Include qidentityproxymodel.sip
%Include qiodevice.sip
%Include qitemselectionmodel.sip
%Include qjsondocument.sip
%Include qjsonvalue.sip
%Include qlibrary.sip
%Include qlibraryinfo.sip
%Include qline.sip
%Include qlocale.sip
%Include qlockfile.sip
%Include qlogging.sip
%Include qloggingcategory.sip
%Include qmargins.sip
%Include qmessageauthenticationcode.sip
%Include qmetaobject.sip
%Include qmetatype.sip
%Include qmimedata.sip
%Include qmimedatabase.sip
%Include qmimetype.sip
%Include qmutex.sip
%Include qnumeric.sip
%Include qobject.sip
%Include qobjectcleanuphandler.sip
%Include qobjectdefs.sip
%Include qoperatingsystemversion.sip
%Include qparallelanimationgroup.sip
%Include qpauseanimation.sip
%Include qpropertyanimation.sip
%Include qpluginloader.sip
%Include qpoint.sip
%Include qprocess.sip
%Include qrandom.sip
%Include qreadwritelock.sip
%Include qrect.sip
%Include qregexp.sip
%Include qregularexpression.sip
%Include qresource.sip
%Include qrunnable.sip
%Include qsavefile.sip
%Include qsemaphore.sip
%Include qsequentialanimationgroup.sip
%Include qsettings.sip
%Include qsharedmemory.sip
%Include qsignalmapper.sip
%Include qsignaltransition.sip
%Include qsize.sip
%Include qsocketnotifier.sip
%Include qsortfilterproxymodel.sip
%Include qstandardpaths.sip
%Include qstate.sip
%Include qstatemachine.sip
%Include qstorageinfo.sip
%Include qstring.sip
%Include qstringlistmodel.sip
%Include qsystemsemaphore.sip
%Include qtemporarydir.sip
%Include qtemporaryfile.sip
%Include qtextboundaryfinder.sip
%Include qtextcodec.sip
%Include qtextstream.sip
%Include qthread.sip
%Include qthreadpool.sip
%Include qtimeline.sip
%Include qtimer.sip
%Include qtimezone.sip
%Include qtranslator.sip
%Include qtransposeproxymodel.sip
%Include qurl.sip
%Include qurlquery.sip
%Include quuid.sip
%Include qvariant.sip
%Include qvariantanimation.sip
%Include qversionnumber.sip
%Include qwaitcondition.sip
%Include qxmlstream.sip
%Include qjsonarray.sip
%Include qjsonobject.sip
%Include qpycore_qhash.sip
%Include qpycore_qlist.sip
%Include qpycore_qmap.sip
%Include qpycore_qpair.sip
%Include qpycore_qset.sip
%Include qpycore_qvariantmap.sip
%Include qpycore_qvector.sip
%Include qpycore_virtual_error_handler.sip
%Include qstringlist.sip
%Include qsysinfo.sip
%Include qwineventnotifier.sip
