// qcalendar.sip generated by MetaSIP
//
// This file is part of the QtCore Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%If (Qt_5_14_0 -)

class QCalendar
{
%TypeHeaderCode
#include <qcalendar.h>
%End

public:
    enum
    {
        Unspecified,
    };

    struct YearMonthDay
    {
%TypeHeaderCode
#include <qcalendar.h>
%End

        YearMonthDay();
        YearMonthDay(int year, int month = 1, int day = 1);
        bool isValid() const;
        int year;
        int month;
        int day;
    };

    enum class System
    {
        Gregorian,
        Julian,
        Milankovic,
        Jalali,
        IslamicCivil,
    };

    QCalendar();
    explicit QCalendar(QCalendar::System system);
    explicit QCalendar(const char *name /Encoding="Latin-1"/) [(QLatin1String name)];
%MethodCode
        // This is currently the only occurence of a QLatin1String argument.
        sipCpp = new QCalendar(QLatin1String(a0));
%End

    int daysInMonth(int month, int year = QCalendar::Unspecified) const;
    int daysInYear(int year) const;
    int monthsInYear(int year) const;
    bool isDateValid(int year, int month, int day) const;
    bool isLeapYear(int year) const;
    bool isGregorian() const;
    bool isLunar() const;
    bool isLuniSolar() const;
    bool isSolar() const;
    bool isProleptic() const;
    bool hasYearZero() const;
    int maximumDaysInMonth() const;
    int minimumDaysInMonth() const;
    int maximumMonthsInYear() const;
    QString name() const;
    QDate dateFromParts(int year, int month, int day) const;
    QDate dateFromParts(const QCalendar::YearMonthDay &parts) const;
    QCalendar::YearMonthDay partsFromDate(QDate date) const;
    int dayOfWeek(QDate date) const;
    QString monthName(const QLocale &locale, int month, int year = QCalendar::Unspecified, QLocale::FormatType format = QLocale::LongFormat) const;
    QString standaloneMonthName(const QLocale &locale, int month, int year = QCalendar::Unspecified, QLocale::FormatType format = QLocale::LongFormat) const;
    QString weekDayName(const QLocale &locale, int day, QLocale::FormatType format = QLocale::LongFormat) const;
    QString standaloneWeekDayName(const QLocale &locale, int day, QLocale::FormatType format = QLocale::LongFormat) const;
    QString dateTimeToString(const QString &format, const QDateTime &datetime, const QDate &dateOnly, const QTime &timeOnly, const QLocale &locale) const;
%MethodCode
        // QStringView has issues being implemented as a mapped type.
        sipRes = new QString(sipCpp->dateTimeToString(QStringView(*a0), *a1, *a2, *a3, *a4));
%End

    static QStringList availableCalendars();
};

%End
