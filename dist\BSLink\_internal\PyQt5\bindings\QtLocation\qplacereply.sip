// qplacereply.sip generated by MetaSIP
//
// This file is part of the QtLocation Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%If (Qt_5_5_0 -)

class QPlaceReply : public QObject
{
%TypeHeaderCode
#include <qplacereply.h>
%End

public:
    enum Error
    {
        NoError,
        PlaceDoesNotExistError,
        CategoryDoesNotExistError,
        CommunicationError,
        ParseError,
        PermissionsError,
        UnsupportedError,
        BadArgumentError,
        CancelError,
        UnknownError,
    };

    enum Type
    {
        Reply,
        DetailsReply,
        SearchReply,
        SearchSuggestionReply,
        ContentReply,
        IdReply,
        MatchReply,
    };

    explicit QPlaceReply(QObject *parent /TransferThis/ = 0);
    virtual ~QPlaceReply();
    bool isFinished() const;
    virtual QPlaceReply::Type type() const;
    QString errorString() const;
    QPlaceReply::Error error() const;

public slots:
    virtual void abort();

signals:
%If (Qt_5_9_0 -)
    void aborted();
%End
    void finished();
    void error(QPlaceReply::Error error, const QString &errorString = QString());
%If (Qt_5_12_0 -)
    void contentUpdated();
%End

protected:
    void setFinished(bool finished);
    void setError(QPlaceReply::Error error, const QString &errorString);
};

%End
