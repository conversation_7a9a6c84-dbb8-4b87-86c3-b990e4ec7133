// qaudioformat.sip generated by MetaSIP
//
// This file is part of the QtMultimedia Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QAudioFormat
{
%TypeHeaderCode
#include <qaudioformat.h>
%End

public:
    enum SampleType
    {
        Unknown,
        SignedInt,
        UnSignedInt,
        Float,
    };

    enum <PERSON>ian
    {
        <PERSON><PERSON><PERSON><PERSON>,
        <PERSON><PERSON>ndi<PERSON>,
    };

    QAudioFormat();
    QAudioFormat(const QAudioFormat &other);
    ~QAudioFormat();
    bool operator==(const QAudioFormat &other) const;
    bool operator!=(const QAudioFormat &other) const;
    bool isValid() const;
    void setSampleSize(int sampleSize);
    int sampleSize() const;
    void setCodec(const QString &codec);
    QString codec() const;
    void setByteOrder(QAudioFormat::Endian byteOrder);
    QAudioFormat::Endian byteOrder() const;
    void setSampleType(QAudioFormat::SampleType sampleType);
    QAudioFormat::SampleType sampleType() const;
    void setSampleRate(int sampleRate);
    int sampleRate() const;
    void setChannelCount(int channelCount);
    int channelCount() const;
    qint32 bytesForDuration(qint64 duration) const;
    qint64 durationForBytes(qint32 byteCount) const;
    qint32 bytesForFrames(qint32 frameCount) const;
    qint32 framesForBytes(qint32 byteCount) const;
    qint32 framesForDuration(qint64 duration) const;
    qint64 durationForFrames(qint32 frameCount) const;
    int bytesPerFrame() const;
};
