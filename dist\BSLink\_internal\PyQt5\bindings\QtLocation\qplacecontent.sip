// qplacecontent.sip generated by MetaSIP
//
// This file is part of the QtLocation Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%If (Qt_5_5_0 -)

class QPlaceContent
{
%TypeHeaderCode
#include <qplacecontent.h>
%End

    typedef QMap<int, QPlaceContent> Collection;

public:
    enum Type
    {
        NoType,
        ImageType,
        ReviewType,
        EditorialType,
%If (Qt_5_11_0 -)
        CustomType,
%End
    };

    QPlaceContent();
    QPlaceContent(const QPlaceContent &other);
    virtual ~QPlaceContent();
    bool operator==(const QPlaceContent &other) const;
    bool operator!=(const QPlaceContent &other) const;
    QPlaceContent::Type type() const;
    QPlaceSupplier supplier() const;
    void setSupplier(const QPlaceSupplier &supplier);
    QPlaceUser user() const;
    void setUser(const QPlaceUser &user);
    QString attribution() const;
    void setAttribution(const QString &attribution);
};

%End
