('D:\\wenjian\\BS-Link\\build\\BSLink串口调试助手\\PYZ-00.pyz',
 [('BSLink_ui', 'D:\\wenjian\\BS-Link\\BSLink_ui.py', 'PYMODULE'),
  ('PyQt5',
   'D:\\wenjian\\BS-Link\\.venv\\lib\\site-packages\\PyQt5\\__init__.py',
   'PYMODULE'),
  ('__future__', 'C:\\Program Files\\Python38\\lib\\__future__.py', 'PYMODULE'),
  ('_compat_pickle',
   'C:\\Program Files\\Python38\\lib\\_compat_pickle.py',
   'PYMODULE'),
  ('_compression',
   'C:\\Program Files\\Python38\\lib\\_compression.py',
   'PYMODULE'),
  ('_py_abc', 'C:\\Program Files\\Python38\\lib\\_py_abc.py', 'PYMODULE'),
  ('_pyi_rth_utils',
   'D:\\wenjian\\BS-Link\\.venv\\lib\\site-packages\\PyInstaller\\fake-modules\\_pyi_rth_utils\\__init__.py',
   'PYMODULE'),
  ('_pyi_rth_utils.qt',
   'D:\\wenjian\\BS-Link\\.venv\\lib\\site-packages\\PyInstaller\\fake-modules\\_pyi_rth_utils\\qt.py',
   'PYMODULE'),
  ('_strptime', 'C:\\Program Files\\Python38\\lib\\_strptime.py', 'PYMODULE'),
  ('_threading_local',
   'C:\\Program Files\\Python38\\lib\\_threading_local.py',
   'PYMODULE'),
  ('argparse', 'C:\\Program Files\\Python38\\lib\\argparse.py', 'PYMODULE'),
  ('ast', 'C:\\Program Files\\Python38\\lib\\ast.py', 'PYMODULE'),
  ('base64', 'C:\\Program Files\\Python38\\lib\\base64.py', 'PYMODULE'),
  ('bisect', 'C:\\Program Files\\Python38\\lib\\bisect.py', 'PYMODULE'),
  ('bz2', 'C:\\Program Files\\Python38\\lib\\bz2.py', 'PYMODULE'),
  ('calendar', 'C:\\Program Files\\Python38\\lib\\calendar.py', 'PYMODULE'),
  ('configparser',
   'C:\\Program Files\\Python38\\lib\\configparser.py',
   'PYMODULE'),
  ('contextlib', 'C:\\Program Files\\Python38\\lib\\contextlib.py', 'PYMODULE'),
  ('copy', 'C:\\Program Files\\Python38\\lib\\copy.py', 'PYMODULE'),
  ('csv', 'C:\\Program Files\\Python38\\lib\\csv.py', 'PYMODULE'),
  ('ctypes',
   'C:\\Program Files\\Python38\\lib\\ctypes\\__init__.py',
   'PYMODULE'),
  ('ctypes._endian',
   'C:\\Program Files\\Python38\\lib\\ctypes\\_endian.py',
   'PYMODULE'),
  ('ctypes.wintypes',
   'C:\\Program Files\\Python38\\lib\\ctypes\\wintypes.py',
   'PYMODULE'),
  ('datetime', 'C:\\Program Files\\Python38\\lib\\datetime.py', 'PYMODULE'),
  ('dis', 'C:\\Program Files\\Python38\\lib\\dis.py', 'PYMODULE'),
  ('email', 'C:\\Program Files\\Python38\\lib\\email\\__init__.py', 'PYMODULE'),
  ('email._encoded_words',
   'C:\\Program Files\\Python38\\lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('email._header_value_parser',
   'C:\\Program Files\\Python38\\lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('email._parseaddr',
   'C:\\Program Files\\Python38\\lib\\email\\_parseaddr.py',
   'PYMODULE'),
  ('email._policybase',
   'C:\\Program Files\\Python38\\lib\\email\\_policybase.py',
   'PYMODULE'),
  ('email.base64mime',
   'C:\\Program Files\\Python38\\lib\\email\\base64mime.py',
   'PYMODULE'),
  ('email.charset',
   'C:\\Program Files\\Python38\\lib\\email\\charset.py',
   'PYMODULE'),
  ('email.contentmanager',
   'C:\\Program Files\\Python38\\lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.encoders',
   'C:\\Program Files\\Python38\\lib\\email\\encoders.py',
   'PYMODULE'),
  ('email.errors',
   'C:\\Program Files\\Python38\\lib\\email\\errors.py',
   'PYMODULE'),
  ('email.feedparser',
   'C:\\Program Files\\Python38\\lib\\email\\feedparser.py',
   'PYMODULE'),
  ('email.generator',
   'C:\\Program Files\\Python38\\lib\\email\\generator.py',
   'PYMODULE'),
  ('email.header',
   'C:\\Program Files\\Python38\\lib\\email\\header.py',
   'PYMODULE'),
  ('email.headerregistry',
   'C:\\Program Files\\Python38\\lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email.iterators',
   'C:\\Program Files\\Python38\\lib\\email\\iterators.py',
   'PYMODULE'),
  ('email.message',
   'C:\\Program Files\\Python38\\lib\\email\\message.py',
   'PYMODULE'),
  ('email.parser',
   'C:\\Program Files\\Python38\\lib\\email\\parser.py',
   'PYMODULE'),
  ('email.policy',
   'C:\\Program Files\\Python38\\lib\\email\\policy.py',
   'PYMODULE'),
  ('email.quoprimime',
   'C:\\Program Files\\Python38\\lib\\email\\quoprimime.py',
   'PYMODULE'),
  ('email.utils',
   'C:\\Program Files\\Python38\\lib\\email\\utils.py',
   'PYMODULE'),
  ('fnmatch', 'C:\\Program Files\\Python38\\lib\\fnmatch.py', 'PYMODULE'),
  ('getopt', 'C:\\Program Files\\Python38\\lib\\getopt.py', 'PYMODULE'),
  ('gettext', 'C:\\Program Files\\Python38\\lib\\gettext.py', 'PYMODULE'),
  ('glob', 'C:\\Program Files\\Python38\\lib\\glob.py', 'PYMODULE'),
  ('gzip', 'C:\\Program Files\\Python38\\lib\\gzip.py', 'PYMODULE'),
  ('hashlib', 'C:\\Program Files\\Python38\\lib\\hashlib.py', 'PYMODULE'),
  ('importlib',
   'C:\\Program Files\\Python38\\lib\\importlib\\__init__.py',
   'PYMODULE'),
  ('importlib._bootstrap',
   'C:\\Program Files\\Python38\\lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   'C:\\Program Files\\Python38\\lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.abc',
   'C:\\Program Files\\Python38\\lib\\importlib\\abc.py',
   'PYMODULE'),
  ('importlib.machinery',
   'C:\\Program Files\\Python38\\lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('importlib.metadata',
   'C:\\Program Files\\Python38\\lib\\importlib\\metadata.py',
   'PYMODULE'),
  ('importlib.util',
   'C:\\Program Files\\Python38\\lib\\importlib\\util.py',
   'PYMODULE'),
  ('inspect', 'C:\\Program Files\\Python38\\lib\\inspect.py', 'PYMODULE'),
  ('logging',
   'C:\\Program Files\\Python38\\lib\\logging\\__init__.py',
   'PYMODULE'),
  ('lzma', 'C:\\Program Files\\Python38\\lib\\lzma.py', 'PYMODULE'),
  ('opcode', 'C:\\Program Files\\Python38\\lib\\opcode.py', 'PYMODULE'),
  ('optparse', 'C:\\Program Files\\Python38\\lib\\optparse.py', 'PYMODULE'),
  ('pathlib', 'C:\\Program Files\\Python38\\lib\\pathlib.py', 'PYMODULE'),
  ('pickle', 'C:\\Program Files\\Python38\\lib\\pickle.py', 'PYMODULE'),
  ('pkgutil', 'C:\\Program Files\\Python38\\lib\\pkgutil.py', 'PYMODULE'),
  ('pprint', 'C:\\Program Files\\Python38\\lib\\pprint.py', 'PYMODULE'),
  ('py_compile', 'C:\\Program Files\\Python38\\lib\\py_compile.py', 'PYMODULE'),
  ('quopri', 'C:\\Program Files\\Python38\\lib\\quopri.py', 'PYMODULE'),
  ('random', 'C:\\Program Files\\Python38\\lib\\random.py', 'PYMODULE'),
  ('selectors', 'C:\\Program Files\\Python38\\lib\\selectors.py', 'PYMODULE'),
  ('serial',
   'D:\\wenjian\\BS-Link\\.venv\\lib\\site-packages\\serial\\__init__.py',
   'PYMODULE'),
  ('serial.serialcli',
   'D:\\wenjian\\BS-Link\\.venv\\lib\\site-packages\\serial\\serialcli.py',
   'PYMODULE'),
  ('serial.serialjava',
   'D:\\wenjian\\BS-Link\\.venv\\lib\\site-packages\\serial\\serialjava.py',
   'PYMODULE'),
  ('serial.serialposix',
   'D:\\wenjian\\BS-Link\\.venv\\lib\\site-packages\\serial\\serialposix.py',
   'PYMODULE'),
  ('serial.serialutil',
   'D:\\wenjian\\BS-Link\\.venv\\lib\\site-packages\\serial\\serialutil.py',
   'PYMODULE'),
  ('serial.serialwin32',
   'D:\\wenjian\\BS-Link\\.venv\\lib\\site-packages\\serial\\serialwin32.py',
   'PYMODULE'),
  ('serial.tools',
   'D:\\wenjian\\BS-Link\\.venv\\lib\\site-packages\\serial\\tools\\__init__.py',
   'PYMODULE'),
  ('serial.tools.list_ports',
   'D:\\wenjian\\BS-Link\\.venv\\lib\\site-packages\\serial\\tools\\list_ports.py',
   'PYMODULE'),
  ('serial.tools.list_ports_common',
   'D:\\wenjian\\BS-Link\\.venv\\lib\\site-packages\\serial\\tools\\list_ports_common.py',
   'PYMODULE'),
  ('serial.tools.list_ports_linux',
   'D:\\wenjian\\BS-Link\\.venv\\lib\\site-packages\\serial\\tools\\list_ports_linux.py',
   'PYMODULE'),
  ('serial.tools.list_ports_osx',
   'D:\\wenjian\\BS-Link\\.venv\\lib\\site-packages\\serial\\tools\\list_ports_osx.py',
   'PYMODULE'),
  ('serial.tools.list_ports_posix',
   'D:\\wenjian\\BS-Link\\.venv\\lib\\site-packages\\serial\\tools\\list_ports_posix.py',
   'PYMODULE'),
  ('serial.tools.list_ports_windows',
   'D:\\wenjian\\BS-Link\\.venv\\lib\\site-packages\\serial\\tools\\list_ports_windows.py',
   'PYMODULE'),
  ('serial.win32',
   'D:\\wenjian\\BS-Link\\.venv\\lib\\site-packages\\serial\\win32.py',
   'PYMODULE'),
  ('shutil', 'C:\\Program Files\\Python38\\lib\\shutil.py', 'PYMODULE'),
  ('signal', 'C:\\Program Files\\Python38\\lib\\signal.py', 'PYMODULE'),
  ('socket', 'C:\\Program Files\\Python38\\lib\\socket.py', 'PYMODULE'),
  ('string', 'C:\\Program Files\\Python38\\lib\\string.py', 'PYMODULE'),
  ('stringprep', 'C:\\Program Files\\Python38\\lib\\stringprep.py', 'PYMODULE'),
  ('subprocess', 'C:\\Program Files\\Python38\\lib\\subprocess.py', 'PYMODULE'),
  ('tarfile', 'C:\\Program Files\\Python38\\lib\\tarfile.py', 'PYMODULE'),
  ('textwrap', 'C:\\Program Files\\Python38\\lib\\textwrap.py', 'PYMODULE'),
  ('threading', 'C:\\Program Files\\Python38\\lib\\threading.py', 'PYMODULE'),
  ('token', 'C:\\Program Files\\Python38\\lib\\token.py', 'PYMODULE'),
  ('tokenize', 'C:\\Program Files\\Python38\\lib\\tokenize.py', 'PYMODULE'),
  ('tracemalloc',
   'C:\\Program Files\\Python38\\lib\\tracemalloc.py',
   'PYMODULE'),
  ('typing', 'C:\\Program Files\\Python38\\lib\\typing.py', 'PYMODULE'),
  ('urllib',
   'C:\\Program Files\\Python38\\lib\\urllib\\__init__.py',
   'PYMODULE'),
  ('urllib.parse',
   'C:\\Program Files\\Python38\\lib\\urllib\\parse.py',
   'PYMODULE'),
  ('uu', 'C:\\Program Files\\Python38\\lib\\uu.py', 'PYMODULE'),
  ('zipfile', 'C:\\Program Files\\Python38\\lib\\zipfile.py', 'PYMODULE'),
  ('zipimport', 'C:\\Program Files\\Python38\\lib\\zipimport.py', 'PYMODULE')])
