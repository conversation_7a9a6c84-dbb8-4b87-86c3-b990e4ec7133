import QtQuick.tooling 1.2

// This file describes the plugin-supplied types contained in the library.
// It is used for QML tooling purposes only.
//
// This file was auto-generated by:
// 'qmlplugindump -nonrelocatable -dependencies dependencies.json QtQuick.Controls.Material 2.15'

Module {
    dependencies: ["QtQuick.Controls 2.0"]
    Component { name: "QQuickAttachedObject"; prototype: "QObject" }
    Component {
        name: "QQuickItem"
        defaultProperty: "data"
        prototype: "QObject"
        Enum {
            name: "Flags"
            values: {
                "ItemClipsChildrenToShape": 1,
                "ItemAcceptsInputMethod": 2,
                "ItemIsFocusScope": 4,
                "ItemHasContents": 8,
                "ItemAcceptsDrops": 16
            }
        }
        Enum {
            name: "TransformOrigin"
            values: {
                "TopLeft": 0,
                "Top": 1,
                "TopRight": 2,
                "Left": 3,
                "Center": 4,
                "Right": 5,
                "BottomLeft": 6,
                "Bottom": 7,
                "BottomRight": 8
            }
        }
        Property { name: "parent"; type: "QQuickItem"; isPointer: true }
        Property { name: "data"; type: "QObject"; isList: true; isReadonly: true }
        Property { name: "resources"; type: "QObject"; isList: true; isReadonly: true }
        Property { name: "children"; type: "QQuickItem"; isList: true; isReadonly: true }
        Property { name: "x"; type: "double" }
        Property { name: "y"; type: "double" }
        Property { name: "z"; type: "double" }
        Property { name: "width"; type: "double" }
        Property { name: "height"; type: "double" }
        Property { name: "opacity"; type: "double" }
        Property { name: "enabled"; type: "bool" }
        Property { name: "visible"; type: "bool" }
        Property { name: "visibleChildren"; type: "QQuickItem"; isList: true; isReadonly: true }
        Property { name: "states"; type: "QQuickState"; isList: true; isReadonly: true }
        Property { name: "transitions"; type: "QQuickTransition"; isList: true; isReadonly: true }
        Property { name: "state"; type: "string" }
        Property { name: "childrenRect"; type: "QRectF"; isReadonly: true }
        Property { name: "anchors"; type: "QQuickAnchors"; isReadonly: true; isPointer: true }
        Property { name: "left"; type: "QQuickAnchorLine"; isReadonly: true }
        Property { name: "right"; type: "QQuickAnchorLine"; isReadonly: true }
        Property { name: "horizontalCenter"; type: "QQuickAnchorLine"; isReadonly: true }
        Property { name: "top"; type: "QQuickAnchorLine"; isReadonly: true }
        Property { name: "bottom"; type: "QQuickAnchorLine"; isReadonly: true }
        Property { name: "verticalCenter"; type: "QQuickAnchorLine"; isReadonly: true }
        Property { name: "baseline"; type: "QQuickAnchorLine"; isReadonly: true }
        Property { name: "baselineOffset"; type: "double" }
        Property { name: "clip"; type: "bool" }
        Property { name: "focus"; type: "bool" }
        Property { name: "activeFocus"; type: "bool"; isReadonly: true }
        Property { name: "activeFocusOnTab"; revision: 1; type: "bool" }
        Property { name: "rotation"; type: "double" }
        Property { name: "scale"; type: "double" }
        Property { name: "transformOrigin"; type: "TransformOrigin" }
        Property { name: "transformOriginPoint"; type: "QPointF"; isReadonly: true }
        Property { name: "transform"; type: "QQuickTransform"; isList: true; isReadonly: true }
        Property { name: "smooth"; type: "bool" }
        Property { name: "antialiasing"; type: "bool" }
        Property { name: "implicitWidth"; type: "double" }
        Property { name: "implicitHeight"; type: "double" }
        Property { name: "containmentMask"; revision: 11; type: "QObject"; isPointer: true }
        Property { name: "layer"; type: "QQuickItemLayer"; isReadonly: true; isPointer: true }
        Signal {
            name: "childrenRectChanged"
            Parameter { type: "QRectF" }
        }
        Signal {
            name: "baselineOffsetChanged"
            Parameter { type: "double" }
        }
        Signal {
            name: "stateChanged"
            Parameter { type: "string" }
        }
        Signal {
            name: "focusChanged"
            Parameter { type: "bool" }
        }
        Signal {
            name: "activeFocusChanged"
            Parameter { type: "bool" }
        }
        Signal {
            name: "activeFocusOnTabChanged"
            revision: 1
            Parameter { type: "bool" }
        }
        Signal {
            name: "parentChanged"
            Parameter { type: "QQuickItem"; isPointer: true }
        }
        Signal {
            name: "transformOriginChanged"
            Parameter { type: "TransformOrigin" }
        }
        Signal {
            name: "smoothChanged"
            Parameter { type: "bool" }
        }
        Signal {
            name: "antialiasingChanged"
            Parameter { type: "bool" }
        }
        Signal {
            name: "clipChanged"
            Parameter { type: "bool" }
        }
        Signal {
            name: "windowChanged"
            revision: 1
            Parameter { name: "window"; type: "QQuickWindow"; isPointer: true }
        }
        Signal { name: "containmentMaskChanged"; revision: 11 }
        Method { name: "update" }
        Method {
            name: "grabToImage"
            revision: 4
            type: "bool"
            Parameter { name: "callback"; type: "QJSValue" }
            Parameter { name: "targetSize"; type: "QSize" }
        }
        Method {
            name: "grabToImage"
            revision: 4
            type: "bool"
            Parameter { name: "callback"; type: "QJSValue" }
        }
        Method {
            name: "contains"
            type: "bool"
            Parameter { name: "point"; type: "QPointF" }
        }
        Method {
            name: "mapFromItem"
            Parameter { type: "QQmlV4Function"; isPointer: true }
        }
        Method {
            name: "mapToItem"
            Parameter { type: "QQmlV4Function"; isPointer: true }
        }
        Method {
            name: "mapFromGlobal"
            revision: 7
            Parameter { type: "QQmlV4Function"; isPointer: true }
        }
        Method {
            name: "mapToGlobal"
            revision: 7
            Parameter { type: "QQmlV4Function"; isPointer: true }
        }
        Method { name: "forceActiveFocus" }
        Method {
            name: "forceActiveFocus"
            Parameter { name: "reason"; type: "Qt::FocusReason" }
        }
        Method {
            name: "nextItemInFocusChain"
            revision: 1
            type: "QQuickItem*"
            Parameter { name: "forward"; type: "bool" }
        }
        Method { name: "nextItemInFocusChain"; revision: 1; type: "QQuickItem*" }
        Method {
            name: "childAt"
            type: "QQuickItem*"
            Parameter { name: "x"; type: "double" }
            Parameter { name: "y"; type: "double" }
        }
    }
    Component {
        name: "QQuickMaterialBusyIndicator"
        defaultProperty: "data"
        prototype: "QQuickItem"
        exports: ["QtQuick.Controls.Material.impl/BusyIndicatorImpl 2.0"]
        exportMetaObjectRevisions: [0]
        Property { name: "color"; type: "QColor" }
        Property { name: "running"; type: "bool" }
    }
    Component {
        name: "QQuickMaterialProgressBar"
        defaultProperty: "data"
        prototype: "QQuickItem"
        exports: ["QtQuick.Controls.Material.impl/ProgressBarImpl 2.0"]
        exportMetaObjectRevisions: [0]
        Property { name: "color"; type: "QColor" }
        Property { name: "progress"; type: "double" }
        Property { name: "indeterminate"; type: "bool" }
    }
    Component {
        name: "QQuickMaterialRipple"
        defaultProperty: "data"
        prototype: "QQuickItem"
        exports: ["QtQuick.Controls.Material.impl/Ripple 2.0"]
        exportMetaObjectRevisions: [0]
        Enum {
            name: "Trigger"
            values: {
                "Press": 0,
                "Release": 1
            }
        }
        Property { name: "color"; type: "QColor" }
        Property { name: "clipRadius"; type: "double" }
        Property { name: "pressed"; type: "bool" }
        Property { name: "active"; type: "bool" }
        Property { name: "anchor"; type: "QQuickItem"; isPointer: true }
        Property { name: "trigger"; type: "Trigger" }
    }
    Component {
        name: "QQuickMaterialStyle"
        prototype: "QQuickAttachedObject"
        exports: ["QtQuick.Controls.Material/Material 2.0"]
        isCreatable: false
        exportMetaObjectRevisions: [0]
        Enum {
            name: "Theme"
            values: {
                "Light": 0,
                "Dark": 1,
                "System": 2
            }
        }
        Enum {
            name: "Variant"
            values: {
                "Normal": 0,
                "Dense": 1
            }
        }
        Enum {
            name: "Color"
            values: {
                "Red": 0,
                "Pink": 1,
                "Purple": 2,
                "DeepPurple": 3,
                "Indigo": 4,
                "Blue": 5,
                "LightBlue": 6,
                "Cyan": 7,
                "Teal": 8,
                "Green": 9,
                "LightGreen": 10,
                "Lime": 11,
                "Yellow": 12,
                "Amber": 13,
                "Orange": 14,
                "DeepOrange": 15,
                "Brown": 16,
                "Grey": 17,
                "BlueGrey": 18
            }
        }
        Enum {
            name: "Shade"
            values: {
                "Shade50": 0,
                "Shade100": 1,
                "Shade200": 2,
                "Shade300": 3,
                "Shade400": 4,
                "Shade500": 5,
                "Shade600": 6,
                "Shade700": 7,
                "Shade800": 8,
                "Shade900": 9,
                "ShadeA100": 10,
                "ShadeA200": 11,
                "ShadeA400": 12,
                "ShadeA700": 13
            }
        }
        Property { name: "theme"; type: "Theme" }
        Property { name: "primary"; type: "QVariant" }
        Property { name: "accent"; type: "QVariant" }
        Property { name: "foreground"; type: "QVariant" }
        Property { name: "background"; type: "QVariant" }
        Property { name: "elevation"; type: "int" }
        Property { name: "primaryColor"; type: "QColor"; isReadonly: true }
        Property { name: "accentColor"; type: "QColor"; isReadonly: true }
        Property { name: "backgroundColor"; type: "QColor"; isReadonly: true }
        Property { name: "primaryTextColor"; type: "QColor"; isReadonly: true }
        Property { name: "primaryHighlightedTextColor"; type: "QColor"; isReadonly: true }
        Property { name: "secondaryTextColor"; type: "QColor"; isReadonly: true }
        Property { name: "hintTextColor"; type: "QColor"; isReadonly: true }
        Property { name: "textSelectionColor"; type: "QColor"; isReadonly: true }
        Property { name: "dropShadowColor"; type: "QColor"; isReadonly: true }
        Property { name: "dividerColor"; type: "QColor"; isReadonly: true }
        Property { name: "iconColor"; type: "QColor"; isReadonly: true }
        Property { name: "iconDisabledColor"; type: "QColor"; isReadonly: true }
        Property { name: "buttonColor"; type: "QColor"; isReadonly: true }
        Property { name: "buttonDisabledColor"; type: "QColor"; isReadonly: true }
        Property { name: "highlightedButtonColor"; type: "QColor"; isReadonly: true }
        Property { name: "frameColor"; type: "QColor"; isReadonly: true }
        Property { name: "rippleColor"; type: "QColor"; isReadonly: true }
        Property { name: "highlightedRippleColor"; type: "QColor"; isReadonly: true }
        Property { name: "switchUncheckedTrackColor"; type: "QColor"; isReadonly: true }
        Property { name: "switchCheckedTrackColor"; type: "QColor"; isReadonly: true }
        Property { name: "switchUncheckedHandleColor"; type: "QColor"; isReadonly: true }
        Property { name: "switchCheckedHandleColor"; type: "QColor"; isReadonly: true }
        Property { name: "switchDisabledTrackColor"; type: "QColor"; isReadonly: true }
        Property { name: "switchDisabledHandleColor"; type: "QColor"; isReadonly: true }
        Property { name: "scrollBarColor"; type: "QColor"; isReadonly: true }
        Property { name: "scrollBarHoveredColor"; type: "QColor"; isReadonly: true }
        Property { name: "scrollBarPressedColor"; type: "QColor"; isReadonly: true }
        Property { name: "dialogColor"; type: "QColor"; isReadonly: true }
        Property { name: "backgroundDimColor"; type: "QColor"; isReadonly: true }
        Property { name: "listHighlightColor"; type: "QColor"; isReadonly: true }
        Property { name: "tooltipColor"; type: "QColor"; isReadonly: true }
        Property { name: "toolBarColor"; type: "QColor"; isReadonly: true }
        Property { name: "toolTextColor"; type: "QColor"; isReadonly: true }
        Property { name: "spinBoxDisabledIconColor"; type: "QColor"; isReadonly: true }
        Property { name: "sliderDisabledColor"; revision: 15; type: "QColor"; isReadonly: true }
        Property { name: "touchTarget"; type: "int"; isReadonly: true }
        Property { name: "buttonHeight"; type: "int"; isReadonly: true }
        Property { name: "delegateHeight"; type: "int"; isReadonly: true }
        Property { name: "dialogButtonBoxHeight"; type: "int"; isReadonly: true }
        Property { name: "frameVerticalPadding"; type: "int"; isReadonly: true }
        Property { name: "menuItemHeight"; type: "int"; isReadonly: true }
        Property { name: "menuItemVerticalPadding"; type: "int"; isReadonly: true }
        Property { name: "switchDelegateVerticalPadding"; type: "int"; isReadonly: true }
        Property { name: "tooltipHeight"; type: "int"; isReadonly: true }
        Signal { name: "paletteChanged" }
        Method {
            name: "color"
            type: "QColor"
            Parameter { name: "color"; type: "Color" }
            Parameter { name: "shade"; type: "Shade" }
        }
        Method {
            name: "color"
            type: "QColor"
            Parameter { name: "color"; type: "Color" }
        }
        Method {
            name: "shade"
            type: "QColor"
            Parameter { name: "color"; type: "QColor" }
            Parameter { name: "shade"; type: "Shade" }
        }
    }
    Component {
        prototype: "QQuickItem"
        name: "QtQuick.Controls.Material.impl/BoxShadow 2.0"
        exports: ["QtQuick.Controls.Material.impl/BoxShadow 2.0"]
        exportMetaObjectRevisions: [0]
        isComposite: true
        defaultProperty: "data"
        Property { name: "offsetX"; type: "int" }
        Property { name: "offsetY"; type: "int" }
        Property { name: "blurRadius"; type: "int" }
        Property { name: "spreadRadius"; type: "int" }
        Property { name: "source"; type: "QQuickItem"; isPointer: true }
        Property { name: "fullWidth"; type: "bool" }
        Property { name: "fullHeight"; type: "bool" }
        Property { name: "glowRadius"; type: "double" }
        Property { name: "spread"; type: "double" }
        Property { name: "color"; type: "QColor" }
        Property { name: "cornerRadius"; type: "double" }
        Property { name: "cached"; type: "bool" }
    }
    Component {
        prototype: "QQuickRectangle"
        name: "QtQuick.Controls.Material.impl/CheckIndicator 2.0"
        exports: ["QtQuick.Controls.Material.impl/CheckIndicator 2.0"]
        exportMetaObjectRevisions: [0]
        isComposite: true
        defaultProperty: "data"
        Property { name: "control"; type: "QQuickItem"; isPointer: true }
        Property { name: "checkState"; type: "int" }
    }
    Component {
        prototype: "QQuickRectangle"
        name: "QtQuick.Controls.Material.impl/CursorDelegate 2.0"
        exports: ["QtQuick.Controls.Material.impl/CursorDelegate 2.0"]
        exportMetaObjectRevisions: [0]
        isComposite: true
        defaultProperty: "data"
    }
    Component {
        prototype: "QQuickItem"
        name: "QtQuick.Controls.Material.impl/ElevationEffect 2.0"
        exports: ["QtQuick.Controls.Material.impl/ElevationEffect 2.0"]
        exportMetaObjectRevisions: [0]
        isComposite: true
        defaultProperty: "data"
        Property { name: "source"; type: "QVariant" }
        Property { name: "elevation"; type: "int" }
        Property { name: "fullWidth"; type: "bool" }
        Property { name: "fullHeight"; type: "bool" }
        Property { name: "sourceItem"; type: "QQuickItem"; isReadonly: true; isPointer: true }
        Property { name: "_shadows"; type: "QVariant"; isReadonly: true }
        Property { name: "_shadow"; type: "QVariant"; isReadonly: true }
    }
    Component {
        prototype: "QQuickRectangle"
        name: "QtQuick.Controls.Material.impl/RadioIndicator 2.0"
        exports: ["QtQuick.Controls.Material.impl/RadioIndicator 2.0"]
        exportMetaObjectRevisions: [0]
        isComposite: true
        defaultProperty: "data"
        Property { name: "control"; type: "QQuickItem"; isPointer: true }
    }
    Component {
        prototype: "QQuickItem"
        name: "QtQuick.Controls.Material.impl/RectangularGlow 2.0"
        exports: ["QtQuick.Controls.Material.impl/RectangularGlow 2.0"]
        exportMetaObjectRevisions: [0]
        isComposite: true
        defaultProperty: "data"
        Property { name: "glowRadius"; type: "double" }
        Property { name: "spread"; type: "double" }
        Property { name: "color"; type: "QColor" }
        Property { name: "cornerRadius"; type: "double" }
        Property { name: "cached"; type: "bool" }
    }
    Component {
        prototype: "QQuickItem"
        name: "QtQuick.Controls.Material.impl/SliderHandle 2.0"
        exports: ["QtQuick.Controls.Material.impl/SliderHandle 2.0"]
        exportMetaObjectRevisions: [0]
        isComposite: true
        defaultProperty: "data"
        Property { name: "value"; type: "double" }
        Property { name: "handleHasFocus"; type: "bool" }
        Property { name: "handlePressed"; type: "bool" }
        Property { name: "handleHovered"; type: "bool" }
        Property { name: "initialSize"; type: "int"; isReadonly: true }
        Property { name: "control"; type: "QVariant"; isReadonly: true }
    }
    Component {
        prototype: "QQuickItem"
        name: "QtQuick.Controls.Material.impl/SwitchIndicator 2.0"
        exports: ["QtQuick.Controls.Material.impl/SwitchIndicator 2.0"]
        exportMetaObjectRevisions: [0]
        isComposite: true
        defaultProperty: "data"
        Property { name: "control"; type: "QQuickItem"; isPointer: true }
        Property { name: "handle"; type: "QQuickRectangle"; isReadonly: true; isPointer: true }
    }
}
