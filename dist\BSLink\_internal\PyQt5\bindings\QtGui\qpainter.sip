// qpainter.sip generated by MetaSIP
//
// This file is part of the QtGui Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QPainter
{
%TypeHeaderCode
#include <qpainter.h>
%End

%TypeCode
// Return an array on the heap of class instances extracted from a set of
// Python arguments.
template<class TYPE>
static TYPE *qtgui_inst_array(const TYPE *first, PyObject *t, sipTypeDef *td)
{
    TYPE *arr = new TYPE[1 + PyTuple_Size(t)];

    arr[0] = *first;

    for (Py_ssize_t i = 0; i < PyTuple_Size(t); ++i)
    {
        int iserr = 0, state;
        TYPE *itm;

        itm  = reinterpret_cast<TYPE *>(sipForceConvertToType(PyTuple_GetItem(t, i), td, 0, SIP_NOT_NONE, &state, &iserr));

        if (iserr)
        {
            sipReleaseType(itm, td, state);

            PyErr_Format(PyExc_TypeError, "each argument must be an instance of %s", sipPyTypeName(sipTypeAsPyTypeObject(td)));

            delete[] arr;
            return 0;
        }

        arr[1 + i] = *itm;

        sipReleaseType(itm, td, state);
    }

    return arr;
}
%End

public:
    enum RenderHint
    {
        Antialiasing,
        TextAntialiasing,
        SmoothPixmapTransform,
        HighQualityAntialiasing,
        NonCosmeticDefaultPen,
        Qt4CompatiblePainting,
%If (Qt_5_13_0 -)
        LosslessImageRendering,
%End
    };

    typedef QFlags<QPainter::RenderHint> RenderHints;
    QPainter();
    explicit QPainter(QPaintDevice *);
    ~QPainter();
    SIP_PYOBJECT __enter__();
%MethodCode
        // Check a device was passed.
        if (sipCpp->isActive())
        {
            // Just return a reference to self.
            sipRes = sipSelf;
            Py_INCREF(sipRes);
        }
        else
        {
            PyErr_SetString(PyExc_ValueError, "QPainter must be created with a device");
            sipRes = 0;
        }
%End

    void __exit__(SIP_PYOBJECT type, SIP_PYOBJECT value, SIP_PYOBJECT traceback);
%MethodCode
        sipCpp->end();
%End

    QPaintDevice *device() const;
    bool begin(QPaintDevice *);
    bool end();
    bool isActive() const;

    enum CompositionMode
    {
        CompositionMode_SourceOver,
        CompositionMode_DestinationOver,
        CompositionMode_Clear,
        CompositionMode_Source,
        CompositionMode_Destination,
        CompositionMode_SourceIn,
        CompositionMode_DestinationIn,
        CompositionMode_SourceOut,
        CompositionMode_DestinationOut,
        CompositionMode_SourceAtop,
        CompositionMode_DestinationAtop,
        CompositionMode_Xor,
        CompositionMode_Plus,
        CompositionMode_Multiply,
        CompositionMode_Screen,
        CompositionMode_Overlay,
        CompositionMode_Darken,
        CompositionMode_Lighten,
        CompositionMode_ColorDodge,
        CompositionMode_ColorBurn,
        CompositionMode_HardLight,
        CompositionMode_SoftLight,
        CompositionMode_Difference,
        CompositionMode_Exclusion,
        RasterOp_SourceOrDestination,
        RasterOp_SourceAndDestination,
        RasterOp_SourceXorDestination,
        RasterOp_NotSourceAndNotDestination,
        RasterOp_NotSourceOrNotDestination,
        RasterOp_NotSourceXorDestination,
        RasterOp_NotSource,
        RasterOp_NotSourceAndDestination,
        RasterOp_SourceAndNotDestination,
        RasterOp_NotSourceOrDestination,
        RasterOp_SourceOrNotDestination,
        RasterOp_ClearDestination,
        RasterOp_SetDestination,
        RasterOp_NotDestination,
    };

    void setCompositionMode(QPainter::CompositionMode mode);
    QPainter::CompositionMode compositionMode() const;
    const QFont &font() const;
    void setFont(const QFont &f);
    QFontMetrics fontMetrics() const;
    QFontInfo fontInfo() const;
    void setPen(const QColor &color);
    void setPen(const QPen &pen);
    void setPen(Qt::PenStyle style);
    const QPen &pen() const;
    void setBrush(const QBrush &brush);
    void setBrush(Qt::BrushStyle style);
    const QBrush &brush() const;
    void setBackgroundMode(Qt::BGMode mode);
    Qt::BGMode backgroundMode() const;
    QPoint brushOrigin() const;
    void setBrushOrigin(const QPointF &);
    void setBackground(const QBrush &bg);
    const QBrush &background() const;
    QRegion clipRegion() const;
    QPainterPath clipPath() const;
    void setClipRect(const QRectF &rectangle, Qt::ClipOperation operation = Qt::ReplaceClip);
    void setClipRegion(const QRegion &region, Qt::ClipOperation operation = Qt::ReplaceClip);
    void setClipPath(const QPainterPath &path, Qt::ClipOperation operation = Qt::ReplaceClip);
    void setClipping(bool enable);
    bool hasClipping() const;
    void save();
    void restore();
    void scale(qreal sx, qreal sy);
    void shear(qreal sh, qreal sv);
    void rotate(qreal a);
    void translate(const QPointF &offset);
    QRect window() const;
    void setWindow(const QRect &window);
    QRect viewport() const;
    void setViewport(const QRect &viewport);
    void setViewTransformEnabled(bool enable);
    bool viewTransformEnabled() const;
    void strokePath(const QPainterPath &path, const QPen &pen);
    void fillPath(const QPainterPath &path, const QBrush &brush);
    void drawPath(const QPainterPath &path);
    void drawPoints(const QPolygonF &points);
    void drawPoints(const QPolygon &points);
    void drawPoints(const QPointF *points /Array/, int pointCount /ArraySize/);
    void drawPoints(const QPointF *point, ... /TypeHint="QPointF"/);
%MethodCode
        QPointF *points = qtgui_inst_array<QPointF>(a0, a1, sipType_QPointF);
        
        if (points)
        {
            sipCpp->drawPoints(points, 1 + PyTuple_Size(a1));
            delete[] points;
        }
        else
            sipIsErr = 1;
%End

    void drawPoints(const QPoint *points /Array/, int pointCount /ArraySize/);
    void drawPoints(const QPoint *point, ... /TypeHint="QPoint"/);
%MethodCode
        QPoint *points = qtgui_inst_array<QPoint>(a0, a1, sipType_QPoint);
        
        if (points)
        {
            sipCpp->drawPoints(points, 1 + PyTuple_Size(a1));
            delete[] points;
        }
        else
            sipIsErr = 1;
%End

    void drawLines(const QLineF *lines /Array/, int lineCount /ArraySize/);
    void drawLines(const QLineF *line, ... /TypeHint="QLineF"/);
%MethodCode
        QLineF *lines = qtgui_inst_array<QLineF>(a0, a1, sipType_QLineF);
        
        if (lines)
        {
            sipCpp->drawLines(lines, 1 + PyTuple_Size(a1));
            delete[] lines;
        }
        else
            sipIsErr = 1;
%End

    void drawLines(const QPointF *pointPairs /Array/, int lineCount /ArraySize/);
%MethodCode
        sipCpp->drawLines(a0, a1 / 2);
%End

    void drawLines(const QPointF *pointPair, ... /TypeHint="QPointF"/);
%MethodCode
        QPointF *pairs = qtgui_inst_array<QPointF>(a0, a1, sipType_QPointF);
        
        if (pairs)
        {
            sipCpp->drawLines(pairs, (1 + PyTuple_Size(a1)) / 2);
            delete[] pairs;
        }
        else
            sipIsErr = 1;
%End

    void drawLines(const QLine *lines /Array/, int lineCount /ArraySize/);
    void drawLines(const QLine *line, ... /TypeHint="QLine"/);
%MethodCode
        QLine *lines = qtgui_inst_array<QLine>(a0, a1, sipType_QLine);
        
        if (lines)
        {
            sipCpp->drawLines(lines, 1 + PyTuple_Size(a1));
            delete[] lines;
        }
        else
            sipIsErr = 1;
%End

    void drawLines(const QPoint *pointPairs /Array/, int lineCount /ArraySize/);
%MethodCode
        sipCpp->drawLines(a0, a1 / 2);
%End

    void drawLines(const QPoint *pointPair, ... /TypeHint="QPoint"/);
%MethodCode
        QPoint *pairs = qtgui_inst_array<QPoint>(a0, a1, sipType_QPoint);
        
        if (pairs)
        {
            sipCpp->drawLines(pairs, (1 + PyTuple_Size(a1)) / 2);
            delete[] pairs;
        }
        else
            sipIsErr = 1;
%End

    void drawRects(const QRectF *rects /Array/, int rectCount /ArraySize/);
    void drawRects(const QRectF *rect, ... /TypeHint="QRectF"/);
%MethodCode
        QRectF *rects = qtgui_inst_array<QRectF>(a0, a1, sipType_QRectF);
        
        if (rects)
        {
            sipCpp->drawRects(rects, 1 + PyTuple_Size(a1));
            delete[] rects;
        }
        else
            sipIsErr = 1;
%End

    void drawRects(const QRect *rects /Array/, int rectCount /ArraySize/);
    void drawRects(const QRect *rect, ... /TypeHint="QRect"/);
%MethodCode
        QRect *rects = qtgui_inst_array<QRect>(a0, a1, sipType_QRect);
        
        if (rects)
        {
            sipCpp->drawRects(rects, 1 + PyTuple_Size(a1));
            delete[] rects;
        }
        else
            sipIsErr = 1;
%End

    void drawEllipse(const QRectF &r);
    void drawEllipse(const QRect &r);
    void drawPolyline(const QPolygonF &polyline);
    void drawPolyline(const QPolygon &polyline);
    void drawPolyline(const QPointF *points /Array/, int pointCount /ArraySize/);
    void drawPolyline(const QPointF *point, ... /TypeHint="QPointF"/);
%MethodCode
        QPointF *points = qtgui_inst_array<QPointF>(a0, a1, sipType_QPointF);
        
        if (points)
        {
            sipCpp->drawPolyline(points, 1 + PyTuple_Size(a1));
            delete[] points;
        }
        else
            sipIsErr = 1;
%End

    void drawPolyline(const QPoint *points /Array/, int pointCount /ArraySize/);
    void drawPolyline(const QPoint *point, ... /TypeHint="QPoint"/);
%MethodCode
        QPoint *points = qtgui_inst_array<QPoint>(a0, a1, sipType_QPoint);
        
        if (points)
        {
            sipCpp->drawPolyline(points, 1 + PyTuple_Size(a1));
            delete[] points;
        }
        else
            sipIsErr = 1;
%End

    void drawPolygon(const QPolygonF &points, Qt::FillRule fillRule = Qt::OddEvenFill);
    void drawPolygon(const QPolygon &points, Qt::FillRule fillRule = Qt::OddEvenFill);
    void drawPolygon(const QPointF *points /Array/, int pointCount /ArraySize/, Qt::FillRule fillRule = Qt::OddEvenFill);
    void drawPolygon(const QPointF *point, ... /TypeHint="QPointF"/);
%MethodCode
        QPointF *points = qtgui_inst_array<QPointF>(a0, a1, sipType_QPointF);
        
        if (points)
        {
            sipCpp->drawPolygon(points, 1 + PyTuple_Size(a1));
            delete[] points;
        }
        else
            sipIsErr = 1;
%End

    void drawPolygon(const QPoint *points /Array/, int pointCount /ArraySize/, Qt::FillRule fillRule = Qt::OddEvenFill);
    void drawPolygon(const QPoint *point, ... /TypeHint="QPoint"/);
%MethodCode
        QPoint *points = qtgui_inst_array<QPoint>(a0, a1, sipType_QPoint);
        
        if (points)
        {
            sipCpp->drawPolygon(points, 1 + PyTuple_Size(a1));
            delete[] points;
        }
        else
            sipIsErr = 1;
%End

    void drawConvexPolygon(const QPolygonF &poly);
    void drawConvexPolygon(const QPolygon &poly);
    void drawConvexPolygon(const QPointF *points /Array/, int pointCount /ArraySize/);
    void drawConvexPolygon(const QPointF *point, ... /TypeHint="QPointF"/);
%MethodCode
        QPointF *points = qtgui_inst_array<QPointF>(a0, a1, sipType_QPointF);
        
        if (points)
        {
            sipCpp->drawConvexPolygon(points, 1 + PyTuple_Size(a1));
            delete[] points;
        }
        else
            sipIsErr = 1;
%End

    void drawConvexPolygon(const QPoint *points /Array/, int pointCount /ArraySize/);
    void drawConvexPolygon(const QPoint *point, ... /TypeHint="QPoint"/);
%MethodCode
        QPoint *points = qtgui_inst_array<QPoint>(a0, a1, sipType_QPoint);
        
        if (points)
        {
            sipCpp->drawConvexPolygon(points, 1 + PyTuple_Size(a1));
            delete[] points;
        }
        else
            sipIsErr = 1;
%End

    void drawArc(const QRectF &rect, int a, int alen);
    void drawPie(const QRectF &rect, int a, int alen);
    void drawChord(const QRectF &rect, int a, int alen);
    void drawTiledPixmap(const QRectF &rectangle, const QPixmap &pixmap, const QPointF &pos = QPointF());
    void drawPicture(const QPointF &p, const QPicture &picture);
    void drawPixmap(const QRectF &targetRect, const QPixmap &pixmap, const QRectF &sourceRect);
    void setLayoutDirection(Qt::LayoutDirection direction);
    Qt::LayoutDirection layoutDirection() const;
    void drawText(const QPointF &p, const QString &s);
    void drawText(const QRectF &rectangle, int flags, const QString &text, QRectF *boundingRect /Out/ = 0);
    void drawText(const QRect &rectangle, int flags, const QString &text, QRect *boundingRect /Out/ = 0);
    void drawText(const QRectF &rectangle, const QString &text, const QTextOption &option = QTextOption());
    QRectF boundingRect(const QRectF &rect, int flags, const QString &text);
    QRect boundingRect(const QRect &rect, int flags, const QString &text);
    QRectF boundingRect(const QRectF &rectangle, const QString &text, const QTextOption &option = QTextOption());
    void fillRect(const QRectF &, const QBrush &);
    void fillRect(const QRect &, const QBrush &);
    void eraseRect(const QRectF &);
    void setRenderHint(QPainter::RenderHint hint, bool on = true);
    QPainter::RenderHints renderHints() const;
    void setRenderHints(QPainter::RenderHints hints, bool on = true);
    QPaintEngine *paintEngine() const;
    void drawLine(const QLineF &l);
    void drawLine(const QLine &line);
    void drawLine(int x1, int y1, int x2, int y2);
    void drawLine(const QPoint &p1, const QPoint &p2);
    void drawLine(const QPointF &p1, const QPointF &p2);
    void drawRect(const QRectF &rect);
    void drawRect(int x, int y, int w, int h);
    void drawRect(const QRect &r);
    void drawPoint(const QPointF &p);
    void drawPoint(int x, int y);
    void drawPoint(const QPoint &p);
    void drawEllipse(int x, int y, int w, int h);
    void drawArc(const QRect &r, int a, int alen);
    void drawArc(int x, int y, int w, int h, int a, int alen);
    void drawPie(const QRect &rect, int a, int alen);
    void drawPie(int x, int y, int w, int h, int a, int alen);
    void drawChord(const QRect &rect, int a, int alen);
    void drawChord(int x, int y, int w, int h, int a, int alen);
    void setClipRect(int x, int y, int width, int height, Qt::ClipOperation operation = Qt::ReplaceClip);
    void setClipRect(const QRect &rectangle, Qt::ClipOperation operation = Qt::ReplaceClip);
    void eraseRect(const QRect &rect);
    void eraseRect(int x, int y, int w, int h);
    void fillRect(int x, int y, int w, int h, const QBrush &b);
    void setBrushOrigin(int x, int y);
    void setBrushOrigin(const QPoint &p);
    void drawTiledPixmap(const QRect &rectangle, const QPixmap &pixmap, const QPoint &pos = QPoint());
    void drawTiledPixmap(int x, int y, int width, int height, const QPixmap &pixmap, int sx = 0, int sy = 0);
    void drawPixmap(const QRect &targetRect, const QPixmap &pixmap, const QRect &sourceRect);
    void drawPixmap(const QPointF &p, const QPixmap &pm);
    void drawPixmap(const QPoint &p, const QPixmap &pm);
    void drawPixmap(const QRect &r, const QPixmap &pm);
    void drawPixmap(int x, int y, const QPixmap &pm);
    void drawPixmap(int x, int y, int w, int h, const QPixmap &pm);
    void drawPixmap(int x, int y, int w, int h, const QPixmap &pm, int sx, int sy, int sw, int sh);
    void drawPixmap(int x, int y, const QPixmap &pm, int sx, int sy, int sw, int sh);
    void drawPixmap(const QPointF &p, const QPixmap &pm, const QRectF &sr);
    void drawPixmap(const QPoint &p, const QPixmap &pm, const QRect &sr);
    void drawImage(const QRectF &r, const QImage &image);
    void drawImage(const QRect &r, const QImage &image);
    void drawImage(const QPointF &p, const QImage &image);
    void drawImage(const QPoint &p, const QImage &image);
    void drawImage(int x, int y, const QImage &image, int sx = 0, int sy = 0, int sw = -1, int sh = -1, Qt::ImageConversionFlags flags = Qt::ImageConversionFlag::AutoColor);
    void drawText(const QPoint &p, const QString &s);
    void drawText(int x, int y, int width, int height, int flags, const QString &text, QRect *boundingRect /Out/ = 0);
    void drawText(int x, int y, const QString &s);
    QRect boundingRect(int x, int y, int w, int h, int flags, const QString &text);
    qreal opacity() const;
    void setOpacity(qreal opacity);
    void translate(qreal dx, qreal dy);
    void translate(const QPoint &offset);
    void setViewport(int x, int y, int w, int h);
    void setWindow(int x, int y, int w, int h);
    bool worldMatrixEnabled() const;
    void setWorldMatrixEnabled(bool enabled);
    void drawPicture(int x, int y, const QPicture &p);
    void drawPicture(const QPoint &pt, const QPicture &p);
    void setTransform(const QTransform &transform, bool combine = false);
    const QTransform &transform() const;
    const QTransform &deviceTransform() const;
    void resetTransform();
    void setWorldTransform(const QTransform &matrix, bool combine = false);
    const QTransform &worldTransform() const;
    QTransform combinedTransform() const;
    bool testRenderHint(QPainter::RenderHint hint) const;
    void drawRoundedRect(const QRectF &rect, qreal xRadius, qreal yRadius, Qt::SizeMode mode = Qt::AbsoluteSize);
    void drawRoundedRect(int x, int y, int w, int h, qreal xRadius, qreal yRadius, Qt::SizeMode mode = Qt::AbsoluteSize);
    void drawRoundedRect(const QRect &rect, qreal xRadius, qreal yRadius, Qt::SizeMode mode = Qt::AbsoluteSize);
    void drawEllipse(const QPointF &center, qreal rx, qreal ry);
    void drawEllipse(const QPoint &center, int rx, int ry);
    void fillRect(const QRectF &, const QColor &color);
    void fillRect(const QRect &, const QColor &color);
    void fillRect(int x, int y, int w, int h, const QColor &b);
    void fillRect(int x, int y, int w, int h, Qt::GlobalColor c);
    void fillRect(const QRect &r, Qt::GlobalColor c);
    void fillRect(const QRectF &r, Qt::GlobalColor c);
    void fillRect(int x, int y, int w, int h, Qt::BrushStyle style);
    void fillRect(const QRect &r, Qt::BrushStyle style);
    void fillRect(const QRectF &r, Qt::BrushStyle style);
    void beginNativePainting();
    void endNativePainting();

    class PixmapFragment
    {
%TypeHeaderCode
#include <qpainter.h>
%End

    public:
        qreal x;
        qreal y;
        qreal sourceLeft;
        qreal sourceTop;
        qreal width;
        qreal height;
        qreal scaleX;
        qreal scaleY;
        qreal rotation;
        qreal opacity;
        static QPainter::PixmapFragment create(const QPointF &pos, const QRectF &sourceRect, qreal scaleX = 1, qreal scaleY = 1, qreal rotation = 0, qreal opacity = 1) /Factory/;
    };

    enum PixmapFragmentHint
    {
        OpaqueHint,
    };

    typedef QFlags<QPainter::PixmapFragmentHint> PixmapFragmentHints;
    void drawPixmapFragments(const QPainter::PixmapFragment *fragments /Array/, int fragmentCount /ArraySize/, const QPixmap &pixmap, QFlags<QPainter::PixmapFragmentHint> hints = 0);
    void drawStaticText(const QPointF &topLeftPosition, const QStaticText &staticText);
    void drawStaticText(const QPoint &p, const QStaticText &staticText);
    void drawStaticText(int x, int y, const QStaticText &staticText);
    QRectF clipBoundingRect() const;
%If (PyQt_RawFont)
    void drawGlyphRun(const QPointF &position, const QGlyphRun &glyphRun);
%End
%If (Qt_5_12_0 -)
    void fillRect(int x, int y, int w, int h, QGradient::Preset preset);
%End
%If (Qt_5_12_0 -)
    void fillRect(const QRect &r, QGradient::Preset preset);
%End
%If (Qt_5_12_0 -)
    void fillRect(const QRectF &r, QGradient::Preset preset);
%End
    void drawImage(const QRectF &targetRect, const QImage &image, const QRectF &sourceRect, Qt::ImageConversionFlags flags = Qt::ImageConversionFlag::AutoColor);
    void drawImage(const QRect &targetRect, const QImage &image, const QRect &sourceRect, Qt::ImageConversionFlags flags = Qt::ImageConversionFlag::AutoColor);
    void drawImage(const QPointF &p, const QImage &image, const QRectF &sr, Qt::ImageConversionFlags flags = Qt::ImageConversionFlag::AutoColor);
    void drawImage(const QPoint &p, const QImage &image, const QRect &sr, Qt::ImageConversionFlags flags = Qt::ImageConversionFlag::AutoColor);

private:
    QPainter(const QPainter &);
};

QFlags<QPainter::RenderHint> operator|(QPainter::RenderHint f1, QFlags<QPainter::RenderHint> f2);
