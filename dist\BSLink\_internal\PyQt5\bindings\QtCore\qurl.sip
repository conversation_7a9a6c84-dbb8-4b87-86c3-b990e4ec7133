// qurl.sip generated by MetaSIP
//
// This file is part of the QtCore Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


// Template definition for QUrlTwoFlags.
template<E1, E2>
class QUrlTwoFlags /PyQtFlagsEnums="E1 E2", TypeHintIn="Union[QUrlTwoFlags, E1, E2]"/
{
public:
    // These are handled by the %ConvertToTypeCode.
    //QUrlTwoFlags(E1);
    //QUrlTwoFlags(E2);
    QUrlTwoFlags();
    QUrlTwoFlags(const QUrlTwoFlags &f /TypeHint="QUrlTwoFlags"/);

    QUrlTwoFlags &operator&=(int mask);
    //QUrlTwoFlags &operator&=(uint mask);
    QUrlTwoFlags &operator|=(QUrlTwoFlags f /TypeHint="QUrlTwoFlags"/);
    //QUrlTwoFlags &operator|=(E1 f);
    //QUrlTwoFlags &operator|=(E2 f);
    QUrlTwoFlags &operator^=(QUrlTwoFlags f /TypeHint="QUrlTwoFlags"/);
    //QUrlTwoFlags &operator^=(E1 f);
    //QUrlTwoFlags &operator^=(E2 f);

    operator int() const;

    // This is required for Python v3.8 and later.
    int __index__() const;
%MethodCode
        sipRes = sipCpp->operator int();
%End

    QUrlTwoFlags operator|(QUrlTwoFlags f /TypeHint="QUrlTwoFlags"/) const;
    //QUrlTwoFlags operator|(E1 f) const;
    //QUrlTwoFlags operator|(E2 f) const;
    QUrlTwoFlags operator^(QUrlTwoFlags f /TypeHint="QUrlTwoFlags"/) const;
    //QUrlTwoFlags operator^(E1 f) const;
    //QUrlTwoFlags operator^(E2 f) const;
    QUrlTwoFlags operator&(int mask) const;
    //QUrlTwoFlags operator&(uint mask) const;
    //QUrlTwoFlags operator&(E1 f) const;
    //QUrlTwoFlags operator&(E2 f) const;
    QUrlTwoFlags operator~() const;
    
    // These are necessary to prevent Python comparing object IDs.
    bool operator==(const QUrlTwoFlags &f /TypeHint="QUrlTwoFlags"/) const;
%MethodCode
        sipRes = (sipCpp->operator int() == a0->operator int());
%End

    bool operator!=(const QUrlTwoFlags &f /TypeHint="QUrlTwoFlags"/) const;
%MethodCode
        sipRes = (sipCpp->operator int() != a0->operator int());
%End

    int __bool__() const;
%MethodCode
        sipRes = (sipCpp->operator int() != 0);
%End

    long __hash__() const;
%MethodCode
        sipRes = sipCpp->operator int();
%End

%ConvertToTypeCode
// Allow an instance of the base enums whenever a QUrlTwoFlags is expected.

if (sipIsErr == NULL)
    return (PyObject_TypeCheck(sipPy, sipTypeAsPyTypeObject(sipType_E1)) ||
            PyObject_TypeCheck(sipPy, sipTypeAsPyTypeObject(sipType_E2)) ||
            sipCanConvertToType(sipPy, sipType_QUrlTwoFlags, SIP_NO_CONVERTORS));

if (PyObject_TypeCheck(sipPy, sipTypeAsPyTypeObject(sipType_E1)) ||
     PyObject_TypeCheck(sipPy, sipTypeAsPyTypeObject(sipType_E2)))
{
    *sipCppPtr = new QUrlTwoFlags(int(SIPLong_AsLong(sipPy)));

    return sipGetState(sipTransferObj);
}

*sipCppPtr = reinterpret_cast<QUrlTwoFlags *>(sipConvertToType(sipPy, sipType_QUrlTwoFlags, sipTransferObj, SIP_NO_CONVERTORS, 0, sipIsErr));

return 0;
%End
};

class QUrl
{
%TypeHeaderCode
#include <qurl.h>
%End

%TypeCode
#include <QHash>
%End

public:
    enum ParsingMode
    {
        TolerantMode,
        StrictMode,
        DecodedMode,
    };

    QUrl();
    QUrl(const QString &url, QUrl::ParsingMode mode = QUrl::TolerantMode);
    QUrl(const QUrl &copy);
    ~QUrl();
    long __hash__() const;
%MethodCode
        sipRes = qHash(*sipCpp);
%End

    SIP_PYOBJECT __repr__() const /TypeHint="str"/;
%MethodCode
        PyObject *uni = qpycore_PyObject_FromQString(sipCpp->toString());
        
        if (uni)
        {
        #if PY_MAJOR_VERSION >= 3
            sipRes = PyUnicode_FromFormat("PyQt5.QtCore.QUrl(%R)", uni);
        #else
            sipRes = PyString_FromFormat("PyQt5.QtCore.QUrl(");
            PyString_ConcatAndDel(&sipRes, PyObject_Repr(uni));
            PyString_ConcatAndDel(&sipRes, PyString_FromString(")"));
        #endif
        
            Py_DECREF(uni);
        }
%End

    enum UrlFormattingOption
    {
        None /PyName=None_/,
        RemoveScheme,
        RemovePassword,
        RemoveUserInfo,
        RemovePort,
        RemoveAuthority,
        RemovePath,
        RemoveQuery,
        RemoveFragment,
        PreferLocalFile,
        StripTrailingSlash,
%If (Qt_5_2_0 -)
        RemoveFilename,
%End
%If (Qt_5_2_0 -)
        NormalizePathSegments,
%End
    };

    typedef QUrlTwoFlags<QUrl::UrlFormattingOption, QUrl::ComponentFormattingOption> FormattingOptions;

    enum ComponentFormattingOption
    {
        PrettyDecoded,
        EncodeSpaces,
        EncodeUnicode,
        EncodeDelimiters,
        EncodeReserved,
        DecodeReserved,
        FullyEncoded,
        FullyDecoded,
    };

    typedef QFlags<QUrl::ComponentFormattingOption> ComponentFormattingOptions;
    QString url(QUrlTwoFlags<QUrl::UrlFormattingOption, QUrl::ComponentFormattingOption> options = QUrl::PrettyDecoded) const;
    void setUrl(const QString &url, QUrl::ParsingMode mode = QUrl::TolerantMode);
    bool isValid() const;
    bool isEmpty() const;
    void clear();
    void setScheme(const QString &scheme);
    QString scheme() const;
    void setAuthority(const QString &authority, QUrl::ParsingMode mode = QUrl::TolerantMode);
    QString authority(QUrl::ComponentFormattingOptions options = QUrl::PrettyDecoded) const;
    void setUserInfo(const QString &userInfo, QUrl::ParsingMode mode = QUrl::TolerantMode);
    QString userInfo(QUrl::ComponentFormattingOptions options = QUrl::PrettyDecoded) const;
%If (Qt_5_2_0 -)
    void setUserName(const QString &userName, QUrl::ParsingMode mode = QUrl::DecodedMode);
%End
%If (- Qt_5_2_0)
    void setUserName(const QString &userName, QUrl::ParsingMode mode = QUrl::TolerantMode);
%End
%If (Qt_5_2_0 -)
    QString userName(QUrl::ComponentFormattingOptions options = QUrl::FullyDecoded) const;
%End
%If (- Qt_5_2_0)
    QString userName(QFlags<QUrl::ComponentFormattingOption> options = QUrl::PrettyDecoded) const;
%End
%If (Qt_5_2_0 -)
    void setPassword(const QString &password, QUrl::ParsingMode mode = QUrl::DecodedMode);
%End
%If (- Qt_5_2_0)
    void setPassword(const QString &password, QUrl::ParsingMode mode = QUrl::TolerantMode);
%End
%If (Qt_5_2_0 -)
    QString password(QUrl::ComponentFormattingOptions options = QUrl::FullyDecoded) const;
%End
%If (- Qt_5_2_0)
    QString password(QFlags<QUrl::ComponentFormattingOption> options = QUrl::PrettyDecoded) const;
%End
%If (Qt_5_2_0 -)
    void setHost(const QString &host, QUrl::ParsingMode mode = QUrl::DecodedMode);
%End
%If (- Qt_5_2_0)
    void setHost(const QString &host, QUrl::ParsingMode mode = QUrl::TolerantMode);
%End
%If (Qt_5_2_0 -)
    QString host(QUrl::ComponentFormattingOptions = QUrl::FullyDecoded) const;
%End
%If (- Qt_5_2_0)
    QString host(QFlags<QUrl::ComponentFormattingOption> options = QUrl::PrettyDecoded) const;
%End
    void setPort(int port);
    int port(int defaultPort = -1) const;
%If (Qt_5_2_0 -)
    void setPath(const QString &path, QUrl::ParsingMode mode = QUrl::DecodedMode);
%End
%If (- Qt_5_2_0)
    void setPath(const QString &path, QUrl::ParsingMode mode = QUrl::TolerantMode);
%End
%If (Qt_5_2_0 -)
    QString path(QUrl::ComponentFormattingOptions options = QUrl::FullyDecoded) const;
%End
%If (- Qt_5_2_0)
    QString path(QFlags<QUrl::ComponentFormattingOption> options = QUrl::PrettyDecoded) const;
%End
    void setFragment(const QString &fragment, QUrl::ParsingMode mode = QUrl::TolerantMode);
    QString fragment(QUrl::ComponentFormattingOptions options = QUrl::PrettyDecoded) const;
    QUrl resolved(const QUrl &relative) const;
    bool isRelative() const;
    bool isParentOf(const QUrl &url) const;
    static QUrl fromLocalFile(const QString &localfile);
    QString toLocalFile() const;
    QString toString(QUrlTwoFlags<QUrl::UrlFormattingOption, QUrl::ComponentFormattingOption> options = QUrl::PrettyDecoded) const;
    QByteArray toEncoded(QUrlTwoFlags<QUrl::UrlFormattingOption, QUrl::ComponentFormattingOption> options = QUrl::FullyEncoded) const;
    static QUrl fromEncoded(const QByteArray &u, QUrl::ParsingMode mode = QUrl::TolerantMode);
    void detach();
    bool isDetached() const;
    bool operator<(const QUrl &url) const;
    bool operator==(const QUrl &url) const;
    bool operator!=(const QUrl &url) const;
    static QString fromPercentEncoding(const QByteArray &);
    static QByteArray toPercentEncoding(const QString &input, const QByteArray &exclude = QByteArray(), const QByteArray &include = QByteArray());
    bool hasQuery() const;
    bool hasFragment() const;
    QString errorString() const;
    static QString fromAce(const QByteArray &);
    static QByteArray toAce(const QString &);
    static QStringList idnWhitelist();
    static void setIdnWhitelist(const QStringList &);
    static QUrl fromUserInput(const QString &userInput);
    void swap(QUrl &other /Constrained/);
%If (Qt_5_2_0 -)
    QString topLevelDomain(QUrl::ComponentFormattingOptions options = QUrl::FullyDecoded) const;
%End
%If (- Qt_5_2_0)
    QString topLevelDomain(QFlags<QUrl::ComponentFormattingOption> options = QUrl::PrettyDecoded) const;
%End
    bool isLocalFile() const;
    QString toDisplayString(QUrlTwoFlags<QUrl::UrlFormattingOption, QUrl::ComponentFormattingOption> options = QUrl::PrettyDecoded) const;
    void setQuery(const QString &query, QUrl::ParsingMode mode = QUrl::TolerantMode);
    void setQuery(const QUrlQuery &query);
    QString query(QUrl::ComponentFormattingOptions options = QUrl::PrettyDecoded) const;
%If (Qt_5_1_0 -)
    static QStringList toStringList(const QList<QUrl> &uris, QUrlTwoFlags<QUrl::UrlFormattingOption, QUrl::ComponentFormattingOption> options = QUrl::PrettyDecoded);
%End
%If (Qt_5_1_0 -)
    static QList<QUrl> fromStringList(const QStringList &uris, QUrl::ParsingMode mode = QUrl::TolerantMode);
%End
%If (Qt_5_2_0 -)
    QUrl adjusted(QUrl::FormattingOptions options) const;
%End
%If (Qt_5_2_0 -)
    QString fileName(QUrl::ComponentFormattingOptions options = QUrl::FullyDecoded) const;
%End
%If (Qt_5_2_0 -)
    bool matches(const QUrl &url, QUrl::FormattingOptions options) const;
%End
%If (Qt_5_4_0 -)

    enum UserInputResolutionOption
    {
        DefaultResolution,
        AssumeLocalFile,
    };

%End
%If (Qt_5_4_0 -)
    typedef QFlags<QUrl::UserInputResolutionOption> UserInputResolutionOptions;
%End
%If (Qt_5_4_0 -)
    static QUrl fromUserInput(const QString &userInput, const QString &workingDirectory, QUrl::UserInputResolutionOptions options = QUrl::DefaultResolution);
%End
};

QFlags<QUrl::ComponentFormattingOption> operator|(QUrl::ComponentFormattingOption f1, QFlags<QUrl::ComponentFormattingOption> f2);
QDataStream &operator<<(QDataStream &, const QUrl & /Constrained/) /ReleaseGIL/;
QDataStream &operator>>(QDataStream &, QUrl & /Constrained/) /ReleaseGIL/;
QUrlTwoFlags<QUrl::UrlFormattingOption, QUrl::ComponentFormattingOption> operator|(QUrl::UrlFormattingOption f1, QUrlTwoFlags<QUrl::UrlFormattingOption, QUrl::ComponentFormattingOption> f2);
QUrlTwoFlags<QUrl::UrlFormattingOption, QUrl::ComponentFormattingOption> operator|(QUrl::ComponentFormattingOption f, QUrlTwoFlags<QUrl::UrlFormattingOption, QUrl::ComponentFormattingOption> i);
