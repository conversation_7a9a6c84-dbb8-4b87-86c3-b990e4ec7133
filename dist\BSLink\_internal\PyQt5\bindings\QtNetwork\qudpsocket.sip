// qudpsocket.sip generated by MetaSIP
//
// This file is part of the QtNetwork Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QUdpSocket : public QAbstractSocket
{
%TypeHeaderCode
#include <qudpsocket.h>
%End

public:
    explicit QUdpSocket(QObject *parent /TransferThis/ = 0);
    virtual ~QUdpSocket();
    bool hasPendingDatagrams() const;
    qint64 pendingDatagramSize() const;
    SIP_PYOBJECT readDatagram(qint64 maxlen, QHostAddress *host /Out/ = 0, quint16 *port = 0) /TypeHint="Py_v3:bytes;str",ReleaseGIL/;
%MethodCode
        // Return the data read or None if there was an error.
        if (a0 < 0)
        {
            PyErr_SetString(PyExc_ValueError, "maximum length of data to be read cannot be negative");
            sipIsErr = 1;
        }
        else
        {
            char *s = new char[a0];
            qint64 len;
        
            Py_BEGIN_ALLOW_THREADS
            len = sipCpp->readDatagram(s, a0, a1, &a2);
            Py_END_ALLOW_THREADS
        
            if (len < 0)
            {
                Py_INCREF(Py_None);
                sipRes = Py_None;
            }
            else
            {
                sipRes = SIPBytes_FromStringAndSize(s, len);
        
                if (!sipRes)
                    sipIsErr = 1;
            }
        
            delete[] s;
        }
%End

    qint64 writeDatagram(const char *data /Array/, qint64 len /ArraySize/, const QHostAddress &host, quint16 port) /ReleaseGIL/;
    qint64 writeDatagram(const QByteArray &datagram, const QHostAddress &host, quint16 port) /ReleaseGIL/;
    bool joinMulticastGroup(const QHostAddress &groupAddress);
    bool joinMulticastGroup(const QHostAddress &groupAddress, const QNetworkInterface &iface);
    bool leaveMulticastGroup(const QHostAddress &groupAddress);
    bool leaveMulticastGroup(const QHostAddress &groupAddress, const QNetworkInterface &iface);
    QNetworkInterface multicastInterface() const;
    void setMulticastInterface(const QNetworkInterface &iface);
%If (Qt_5_8_0 -)
    QNetworkDatagram receiveDatagram(qint64 maxSize = -1) /ReleaseGIL/;
%End
%If (Qt_5_8_0 -)
    qint64 writeDatagram(const QNetworkDatagram &datagram) /ReleaseGIL/;
%End
};
