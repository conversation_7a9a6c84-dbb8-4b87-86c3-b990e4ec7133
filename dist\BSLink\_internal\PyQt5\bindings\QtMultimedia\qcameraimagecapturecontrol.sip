// qcameraimagecapturecontrol.sip generated by MetaSIP
//
// This file is part of the QtMultimedia Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QCameraImageCaptureControl : public QMediaControl
{
%TypeHeaderCode
#include <qcameraimagecapturecontrol.h>
%End

public:
    virtual ~QCameraImageCaptureControl();
    virtual bool isReadyForCapture() const = 0;
    virtual QCameraImageCapture::DriveMode driveMode() const = 0;
    virtual void setDriveMode(QCameraImageCapture::DriveMode mode) = 0;
    virtual int capture(const QString &fileName) = 0;
    virtual void cancelCapture() = 0;

signals:
    void readyForCaptureChanged(bool ready);
    void imageExposed(int requestId);
    void imageCaptured(int requestId, const QImage &preview);
    void imageMetadataAvailable(int id, const QString &key, const QVariant &value);
    void imageAvailable(int requestId, const QVideoFrame &buffer);
    void imageSaved(int requestId, const QString &fileName);
    void error(int id, int error, const QString &errorString);

protected:
    explicit QCameraImageCaptureControl(QObject *parent /TransferThis/ = 0);
};
