// qgeoroute.sip generated by MetaSIP
//
// This file is part of the QtLocation Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%If (Qt_5_5_0 -)

class QGeoRoute
{
%TypeHeaderCode
#include <qgeoroute.h>
%End

public:
    QGeoRoute();
    QGeoRoute(const QGeoRoute &other);
    ~QGeoRoute();
    bool operator==(const QGeoRoute &other) const;
    bool operator!=(const QGeoRoute &other) const;
    void setRouteId(const QString &id);
    QString routeId() const;
    void setRequest(const QGeoRouteRequest &request);
    QGeoRouteRequest request() const;
    void setBounds(const QGeoRectangle &bounds);
    QGeoRectangle bounds() const;
    void setFirstRouteSegment(const QGeoRouteSegment &routeSegment);
    QGeoRouteSegment firstRouteSegment() const;
    void setTravelTime(int secs);
    int travelTime() const;
    void setDistance(qreal distance);
    qreal distance() const;
    void setTravelMode(QGeoRouteRequest::TravelMode mode);
    QGeoRouteRequest::TravelMode travelMode() const;
    void setPath(const QList<QGeoCoordinate> &path);
    QList<QGeoCoordinate> path() const;
%If (Qt_5_12_0 -)
    void setRouteLegs(const QList<QGeoRouteLeg> &legs);
%End
%If (Qt_5_12_0 -)
    QList<QGeoRouteLeg> routeLegs() const;
%End
%If (Qt_5_13_0 -)
    void setExtendedAttributes(const QVariantMap &extendedAttributes);
%End
%If (Qt_5_13_0 -)
    QVariantMap extendedAttributes() const;
%End
};

%End
%If (Qt_5_12_0 -)

class QGeoRouteLeg : public QGeoRoute
{
%TypeHeaderCode
#include <qgeoroute.h>
%End

public:
    QGeoRouteLeg();
    QGeoRouteLeg(const QGeoRouteLeg &other);
    ~QGeoRouteLeg();
    void setLegIndex(int idx);
    int legIndex() const;
    void setOverallRoute(const QGeoRoute &route);
    QGeoRoute overallRoute() const;
};

%End
