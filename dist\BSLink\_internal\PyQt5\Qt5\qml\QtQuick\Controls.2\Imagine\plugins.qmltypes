import QtQuick.tooling 1.2

// This file describes the plugin-supplied types contained in the library.
// It is used for QML tooling purposes only.
//
// This file was auto-generated by:
// 'qmlplugindump -nonrelocatable -dependencies dependencies.json QtQuick.Controls.Imagine 2.15'

Module {
    dependencies: ["QtQuick.Controls 2.0"]
    Component {
        name: "QQuickAnimatedImageSelector"
        prototype: "QQuickImageSelector"
        exports: ["QtQuick.Controls.Imagine.impl/AnimatedImageSelector 2.3"]
        exportMetaObjectRevisions: [0]
    }
    Component { name: "QQuickAttachedObject"; prototype: "QObject" }
    Component {
        name: "QQuickImage"
        defaultProperty: "data"
        prototype: "QQuickImageBase"
        Enum {
            name: "HAlignment"
            values: {
                "AlignLeft": 1,
                "AlignRight": 2,
                "AlignHCenter": 4
            }
        }
        Enum {
            name: "VAlignment"
            values: {
                "AlignTop": 32,
                "AlignBottom": 64,
                "AlignVCenter": 128
            }
        }
        Enum {
            name: "FillMode"
            values: {
                "Stretch": 0,
                "PreserveAspectFit": 1,
                "PreserveAspectCrop": 2,
                "Tile": 3,
                "TileVertically": 4,
                "TileHorizontally": 5,
                "Pad": 6
            }
        }
        Property { name: "fillMode"; type: "FillMode" }
        Property { name: "paintedWidth"; type: "double"; isReadonly: true }
        Property { name: "paintedHeight"; type: "double"; isReadonly: true }
        Property { name: "horizontalAlignment"; type: "HAlignment" }
        Property { name: "verticalAlignment"; type: "VAlignment" }
        Property { name: "mipmap"; revision: 3; type: "bool" }
        Property { name: "autoTransform"; revision: 5; type: "bool" }
        Property { name: "sourceClipRect"; revision: 15; type: "QRectF" }
        Signal { name: "paintedGeometryChanged" }
        Signal {
            name: "horizontalAlignmentChanged"
            Parameter { name: "alignment"; type: "HAlignment" }
        }
        Signal {
            name: "verticalAlignmentChanged"
            Parameter { name: "alignment"; type: "VAlignment" }
        }
        Signal {
            name: "mipmapChanged"
            revision: 3
            Parameter { type: "bool" }
        }
        Signal { name: "autoTransformChanged"; revision: 5 }
    }
    Component {
        name: "QQuickImageBase"
        defaultProperty: "data"
        prototype: "QQuickImplicitSizeItem"
        Enum {
            name: "LoadPixmapOptions"
            values: {
                "NoOption": 0,
                "HandleDPR": 1,
                "UseProviderOptions": 2
            }
        }
        Enum {
            name: "Status"
            values: {
                "Null": 0,
                "Ready": 1,
                "Loading": 2,
                "Error": 3
            }
        }
        Property { name: "status"; type: "Status"; isReadonly: true }
        Property { name: "source"; type: "QUrl" }
        Property { name: "progress"; type: "double"; isReadonly: true }
        Property { name: "asynchronous"; type: "bool" }
        Property { name: "cache"; type: "bool" }
        Property { name: "sourceSize"; type: "QSize" }
        Property { name: "mirror"; type: "bool" }
        Property { name: "currentFrame"; revision: 14; type: "int" }
        Property { name: "frameCount"; revision: 14; type: "int"; isReadonly: true }
        Property { name: "colorSpace"; revision: 15; type: "QColorSpace" }
        Signal {
            name: "sourceChanged"
            Parameter { type: "QUrl" }
        }
        Signal {
            name: "statusChanged"
            Parameter { type: "QQuickImageBase::Status" }
        }
        Signal {
            name: "progressChanged"
            Parameter { name: "progress"; type: "double" }
        }
        Signal { name: "currentFrameChanged"; revision: 14 }
        Signal { name: "frameCountChanged"; revision: 14 }
        Signal { name: "sourceClipRectChanged"; revision: 15 }
        Signal { name: "colorSpaceChanged"; revision: 15 }
    }
    Component {
        name: "QQuickImageSelector"
        prototype: "QObject"
        exports: ["QtQuick.Controls.Imagine.impl/ImageSelector 2.3"]
        exportMetaObjectRevisions: [0]
        Property { name: "source"; type: "QUrl"; isReadonly: true }
        Property { name: "name"; type: "string" }
        Property { name: "path"; type: "string" }
        Property { name: "states"; type: "QVariantList" }
        Property { name: "separator"; type: "string" }
        Property { name: "cache"; type: "bool" }
    }
    Component {
        name: "QQuickImagineStyle"
        prototype: "QQuickAttachedObject"
        exports: ["QtQuick.Controls.Imagine/Imagine 2.3"]
        isCreatable: false
        exportMetaObjectRevisions: [0]
        Property { name: "path"; type: "string" }
        Property { name: "url"; type: "QUrl"; isReadonly: true }
    }
    Component {
        name: "QQuickImplicitSizeItem"
        defaultProperty: "data"
        prototype: "QQuickItem"
        Property { name: "implicitWidth"; type: "double"; isReadonly: true }
        Property { name: "implicitHeight"; type: "double"; isReadonly: true }
    }
    Component {
        name: "QQuickItem"
        defaultProperty: "data"
        prototype: "QObject"
        Enum {
            name: "Flags"
            values: {
                "ItemClipsChildrenToShape": 1,
                "ItemAcceptsInputMethod": 2,
                "ItemIsFocusScope": 4,
                "ItemHasContents": 8,
                "ItemAcceptsDrops": 16
            }
        }
        Enum {
            name: "TransformOrigin"
            values: {
                "TopLeft": 0,
                "Top": 1,
                "TopRight": 2,
                "Left": 3,
                "Center": 4,
                "Right": 5,
                "BottomLeft": 6,
                "Bottom": 7,
                "BottomRight": 8
            }
        }
        Property { name: "parent"; type: "QQuickItem"; isPointer: true }
        Property { name: "data"; type: "QObject"; isList: true; isReadonly: true }
        Property { name: "resources"; type: "QObject"; isList: true; isReadonly: true }
        Property { name: "children"; type: "QQuickItem"; isList: true; isReadonly: true }
        Property { name: "x"; type: "double" }
        Property { name: "y"; type: "double" }
        Property { name: "z"; type: "double" }
        Property { name: "width"; type: "double" }
        Property { name: "height"; type: "double" }
        Property { name: "opacity"; type: "double" }
        Property { name: "enabled"; type: "bool" }
        Property { name: "visible"; type: "bool" }
        Property { name: "visibleChildren"; type: "QQuickItem"; isList: true; isReadonly: true }
        Property { name: "states"; type: "QQuickState"; isList: true; isReadonly: true }
        Property { name: "transitions"; type: "QQuickTransition"; isList: true; isReadonly: true }
        Property { name: "state"; type: "string" }
        Property { name: "childrenRect"; type: "QRectF"; isReadonly: true }
        Property { name: "anchors"; type: "QQuickAnchors"; isReadonly: true; isPointer: true }
        Property { name: "left"; type: "QQuickAnchorLine"; isReadonly: true }
        Property { name: "right"; type: "QQuickAnchorLine"; isReadonly: true }
        Property { name: "horizontalCenter"; type: "QQuickAnchorLine"; isReadonly: true }
        Property { name: "top"; type: "QQuickAnchorLine"; isReadonly: true }
        Property { name: "bottom"; type: "QQuickAnchorLine"; isReadonly: true }
        Property { name: "verticalCenter"; type: "QQuickAnchorLine"; isReadonly: true }
        Property { name: "baseline"; type: "QQuickAnchorLine"; isReadonly: true }
        Property { name: "baselineOffset"; type: "double" }
        Property { name: "clip"; type: "bool" }
        Property { name: "focus"; type: "bool" }
        Property { name: "activeFocus"; type: "bool"; isReadonly: true }
        Property { name: "activeFocusOnTab"; revision: 1; type: "bool" }
        Property { name: "rotation"; type: "double" }
        Property { name: "scale"; type: "double" }
        Property { name: "transformOrigin"; type: "TransformOrigin" }
        Property { name: "transformOriginPoint"; type: "QPointF"; isReadonly: true }
        Property { name: "transform"; type: "QQuickTransform"; isList: true; isReadonly: true }
        Property { name: "smooth"; type: "bool" }
        Property { name: "antialiasing"; type: "bool" }
        Property { name: "implicitWidth"; type: "double" }
        Property { name: "implicitHeight"; type: "double" }
        Property { name: "containmentMask"; revision: 11; type: "QObject"; isPointer: true }
        Property { name: "layer"; type: "QQuickItemLayer"; isReadonly: true; isPointer: true }
        Signal {
            name: "childrenRectChanged"
            Parameter { type: "QRectF" }
        }
        Signal {
            name: "baselineOffsetChanged"
            Parameter { type: "double" }
        }
        Signal {
            name: "stateChanged"
            Parameter { type: "string" }
        }
        Signal {
            name: "focusChanged"
            Parameter { type: "bool" }
        }
        Signal {
            name: "activeFocusChanged"
            Parameter { type: "bool" }
        }
        Signal {
            name: "activeFocusOnTabChanged"
            revision: 1
            Parameter { type: "bool" }
        }
        Signal {
            name: "parentChanged"
            Parameter { type: "QQuickItem"; isPointer: true }
        }
        Signal {
            name: "transformOriginChanged"
            Parameter { type: "TransformOrigin" }
        }
        Signal {
            name: "smoothChanged"
            Parameter { type: "bool" }
        }
        Signal {
            name: "antialiasingChanged"
            Parameter { type: "bool" }
        }
        Signal {
            name: "clipChanged"
            Parameter { type: "bool" }
        }
        Signal {
            name: "windowChanged"
            revision: 1
            Parameter { name: "window"; type: "QQuickWindow"; isPointer: true }
        }
        Signal { name: "containmentMaskChanged"; revision: 11 }
        Method { name: "update" }
        Method {
            name: "grabToImage"
            revision: 4
            type: "bool"
            Parameter { name: "callback"; type: "QJSValue" }
            Parameter { name: "targetSize"; type: "QSize" }
        }
        Method {
            name: "grabToImage"
            revision: 4
            type: "bool"
            Parameter { name: "callback"; type: "QJSValue" }
        }
        Method {
            name: "contains"
            type: "bool"
            Parameter { name: "point"; type: "QPointF" }
        }
        Method {
            name: "mapFromItem"
            Parameter { type: "QQmlV4Function"; isPointer: true }
        }
        Method {
            name: "mapToItem"
            Parameter { type: "QQmlV4Function"; isPointer: true }
        }
        Method {
            name: "mapFromGlobal"
            revision: 7
            Parameter { type: "QQmlV4Function"; isPointer: true }
        }
        Method {
            name: "mapToGlobal"
            revision: 7
            Parameter { type: "QQmlV4Function"; isPointer: true }
        }
        Method { name: "forceActiveFocus" }
        Method {
            name: "forceActiveFocus"
            Parameter { name: "reason"; type: "Qt::FocusReason" }
        }
        Method {
            name: "nextItemInFocusChain"
            revision: 1
            type: "QQuickItem*"
            Parameter { name: "forward"; type: "bool" }
        }
        Method { name: "nextItemInFocusChain"; revision: 1; type: "QQuickItem*" }
        Method {
            name: "childAt"
            type: "QQuickItem*"
            Parameter { name: "x"; type: "double" }
            Parameter { name: "y"; type: "double" }
        }
    }
    Component {
        name: "QQuickNinePatchImage"
        defaultProperty: "data"
        prototype: "QQuickImage"
        exports: ["QtQuick.Controls.Imagine.impl/NinePatchImage 2.3"]
        exportMetaObjectRevisions: [0]
        Property { name: "topPadding"; type: "double"; isReadonly: true }
        Property { name: "leftPadding"; type: "double"; isReadonly: true }
        Property { name: "rightPadding"; type: "double"; isReadonly: true }
        Property { name: "bottomPadding"; type: "double"; isReadonly: true }
        Property { name: "topInset"; type: "double"; isReadonly: true }
        Property { name: "leftInset"; type: "double"; isReadonly: true }
        Property { name: "rightInset"; type: "double"; isReadonly: true }
        Property { name: "bottomInset"; type: "double"; isReadonly: true }
    }
    Component {
        name: "QQuickNinePatchImageSelector"
        prototype: "QQuickImageSelector"
        exports: ["QtQuick.Controls.Imagine.impl/NinePatchImageSelector 2.3"]
        exportMetaObjectRevisions: [0]
    }
}
