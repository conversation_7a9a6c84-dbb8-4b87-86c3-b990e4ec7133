import QtQuick.tooling 1.2

// This file describes the plugin-supplied types contained in the library.
// It is used for QML tooling purposes only.
//
// This file was auto-generated by qmltyperegistrar.

Module {
    dependencies: []
    Component {
        name: "QDoubleValidator"
        prototype: "QValidator"
        Enum {
            name: "Notation"
            values: ["StandardNotation", "ScientificNotation"]
        }
        Property { name: "bottom"; type: "double" }
        Property { name: "top"; type: "double" }
        Property { name: "decimals"; type: "int" }
        Property { name: "notation"; type: "Notation" }
        Signal {
            name: "bottomChanged"
            Parameter { name: "bottom"; type: "double" }
        }
        Signal {
            name: "topChanged"
            Parameter { name: "top"; type: "double" }
        }
        Signal {
            name: "decimalsChanged"
            Parameter { name: "decimals"; type: "int" }
        }
        Signal {
            name: "notationChanged"
            Parameter { name: "notation"; type: "QDoubleValidator::Notation" }
        }
    }
    Component {
        file: "private/qquickforeignutils_p.h"
        name: "QInputMethod"
        prototype: "QObject"
        exports: ["QtQuick/InputMethod 2.0"]
        isCreatable: false
        exportMetaObjectRevisions: [0]
        Enum {
            name: "Action"
            values: ["Click", "ContextMenu"]
        }
        Property { name: "cursorRectangle"; type: "QRectF"; isReadonly: true }
        Property { name: "anchorRectangle"; type: "QRectF"; isReadonly: true }
        Property { name: "keyboardRectangle"; type: "QRectF"; isReadonly: true }
        Property { name: "inputItemClipRectangle"; type: "QRectF"; isReadonly: true }
        Property { name: "visible"; type: "bool"; isReadonly: true }
        Property { name: "animating"; type: "bool"; isReadonly: true }
        Property { name: "locale"; type: "QLocale"; isReadonly: true }
        Property { name: "inputDirection"; type: "Qt::LayoutDirection"; isReadonly: true }
        Signal {
            name: "inputDirectionChanged"
            Parameter { name: "newDirection"; type: "Qt::LayoutDirection" }
        }
        Method { name: "show" }
        Method { name: "hide" }
        Method {
            name: "update"
            Parameter { name: "queries"; type: "Qt::InputMethodQueries" }
        }
        Method { name: "reset" }
        Method { name: "commit" }
        Method {
            name: "invokeAction"
            Parameter { name: "a"; type: "Action" }
            Parameter { name: "cursorPosition"; type: "int" }
        }
    }
    Component {
        name: "QIntValidator"
        prototype: "QValidator"
        Property { name: "bottom"; type: "int" }
        Property { name: "top"; type: "int" }
        Signal {
            name: "bottomChanged"
            Parameter { name: "bottom"; type: "int" }
        }
        Signal {
            name: "topChanged"
            Parameter { name: "top"; type: "int" }
        }
    }
    Component {
        file: "private/qquickforeignutils_p.h"
        name: "QKeySequence"
        exports: ["QtQuick/StandardKey 2.2"]
        isCreatable: false
        exportMetaObjectRevisions: [2]
        Enum {
            name: "StandardKey"
            values: [
                "UnknownKey",
                "HelpContents",
                "WhatsThis",
                "Open",
                "Close",
                "Save",
                "New",
                "Delete",
                "Cut",
                "Copy",
                "Paste",
                "Undo",
                "Redo",
                "Back",
                "Forward",
                "Refresh",
                "ZoomIn",
                "ZoomOut",
                "Print",
                "AddTab",
                "NextChild",
                "PreviousChild",
                "Find",
                "FindNext",
                "FindPrevious",
                "Replace",
                "SelectAll",
                "Bold",
                "Italic",
                "Underline",
                "MoveToNextChar",
                "MoveToPreviousChar",
                "MoveToNextWord",
                "MoveToPreviousWord",
                "MoveToNextLine",
                "MoveToPreviousLine",
                "MoveToNextPage",
                "MoveToPreviousPage",
                "MoveToStartOfLine",
                "MoveToEndOfLine",
                "MoveToStartOfBlock",
                "MoveToEndOfBlock",
                "MoveToStartOfDocument",
                "MoveToEndOfDocument",
                "SelectNextChar",
                "SelectPreviousChar",
                "SelectNextWord",
                "SelectPreviousWord",
                "SelectNextLine",
                "SelectPreviousLine",
                "SelectNextPage",
                "SelectPreviousPage",
                "SelectStartOfLine",
                "SelectEndOfLine",
                "SelectStartOfBlock",
                "SelectEndOfBlock",
                "SelectStartOfDocument",
                "SelectEndOfDocument",
                "DeleteStartOfWord",
                "DeleteEndOfWord",
                "DeleteEndOfLine",
                "InsertParagraphSeparator",
                "InsertLineSeparator",
                "SaveAs",
                "Preferences",
                "Quit",
                "FullScreen",
                "Deselect",
                "DeleteCompleteLine",
                "Backspace",
                "Cancel"
            ]
        }
    }
    Component {
        file: "private/qquickitemsmodule_p.h"
        name: "QPointingDeviceUniqueId"
        exports: ["QtQuick/PointingDeviceUniqueId 2.9"]
        isCreatable: false
        exportMetaObjectRevisions: [9]
        Property { name: "numericId"; type: "qlonglong"; isReadonly: true }
    }
    Component {
        name: "QQmlApplication"
        prototype: "QObject"
        Property { name: "arguments"; type: "QStringList"; isReadonly: true }
        Property { name: "name"; type: "string" }
        Property { name: "version"; type: "string" }
        Property { name: "organization"; type: "string" }
        Property { name: "domain"; type: "string" }
        Signal { name: "aboutToQuit" }
        Method {
            name: "setName"
            Parameter { name: "arg"; type: "string" }
        }
        Method {
            name: "setVersion"
            Parameter { name: "arg"; type: "string" }
        }
        Method {
            name: "setOrganization"
            Parameter { name: "arg"; type: "string" }
        }
        Method {
            name: "setDomain"
            Parameter { name: "arg"; type: "string" }
        }
    }
    Component {
        file: "private/qquickanimation_p.h"
        name: "QQuickAbstractAnimation"
        prototype: "QObject"
        exports: ["QtQuick/Animation 2.0", "QtQuick/Animation 2.12"]
        isCreatable: false
        exportMetaObjectRevisions: [0, 12]
        Enum {
            name: "Loops"
            values: ["Infinite"]
        }
        Property { name: "running"; type: "bool" }
        Property { name: "paused"; type: "bool" }
        Property { name: "alwaysRunToEnd"; type: "bool" }
        Property { name: "loops"; type: "int" }
        Signal { name: "started" }
        Signal { name: "stopped" }
        Signal {
            name: "runningChanged"
            Parameter { type: "bool" }
        }
        Signal {
            name: "pausedChanged"
            Parameter { type: "bool" }
        }
        Signal {
            name: "alwaysRunToEndChanged"
            Parameter { type: "bool" }
        }
        Signal {
            name: "loopCountChanged"
            Parameter { type: "int" }
        }
        Signal { name: "finished"; revision: 12 }
        Method { name: "restart" }
        Method { name: "start" }
        Method { name: "pause" }
        Method { name: "resume" }
        Method { name: "stop" }
        Method { name: "complete" }
        Method { name: "componentFinalized" }
    }
    Component {
        file: "private/qquickaccessibleattached_p.h"
        name: "QQuickAccessibleAttached"
        prototype: "QObject"
        exports: ["QtQuick/Accessible 2.0"]
        isCreatable: false
        exportMetaObjectRevisions: [0]
        attachedType: "QQuickAccessibleAttached"
        Property { name: "role"; type: "QAccessible::Role" }
        Property { name: "name"; type: "string" }
        Property { name: "description"; type: "string" }
        Property { name: "ignored"; type: "bool" }
        Property { name: "checkable"; type: "bool" }
        Property { name: "checked"; type: "bool" }
        Property { name: "editable"; type: "bool" }
        Property { name: "focusable"; type: "bool" }
        Property { name: "focused"; type: "bool" }
        Property { name: "multiLine"; type: "bool" }
        Property { name: "readOnly"; type: "bool" }
        Property { name: "selected"; type: "bool" }
        Property { name: "selectable"; type: "bool" }
        Property { name: "pressed"; type: "bool" }
        Property { name: "checkStateMixed"; type: "bool" }
        Property { name: "defaultButton"; type: "bool" }
        Property { name: "passwordEdit"; type: "bool" }
        Property { name: "selectableText"; type: "bool" }
        Property { name: "searchEdit"; type: "bool" }
        Signal {
            name: "checkableChanged"
            Parameter { name: "arg"; type: "bool" }
        }
        Signal {
            name: "checkedChanged"
            Parameter { name: "arg"; type: "bool" }
        }
        Signal {
            name: "editableChanged"
            Parameter { name: "arg"; type: "bool" }
        }
        Signal {
            name: "focusableChanged"
            Parameter { name: "arg"; type: "bool" }
        }
        Signal {
            name: "focusedChanged"
            Parameter { name: "arg"; type: "bool" }
        }
        Signal {
            name: "multiLineChanged"
            Parameter { name: "arg"; type: "bool" }
        }
        Signal {
            name: "readOnlyChanged"
            Parameter { name: "arg"; type: "bool" }
        }
        Signal {
            name: "selectedChanged"
            Parameter { name: "arg"; type: "bool" }
        }
        Signal {
            name: "selectableChanged"
            Parameter { name: "arg"; type: "bool" }
        }
        Signal {
            name: "pressedChanged"
            Parameter { name: "arg"; type: "bool" }
        }
        Signal {
            name: "checkStateMixedChanged"
            Parameter { name: "arg"; type: "bool" }
        }
        Signal {
            name: "defaultButtonChanged"
            Parameter { name: "arg"; type: "bool" }
        }
        Signal {
            name: "passwordEditChanged"
            Parameter { name: "arg"; type: "bool" }
        }
        Signal {
            name: "selectableTextChanged"
            Parameter { name: "arg"; type: "bool" }
        }
        Signal {
            name: "searchEditChanged"
            Parameter { name: "arg"; type: "bool" }
        }
        Signal { name: "pressAction" }
        Signal { name: "toggleAction" }
        Signal { name: "increaseAction" }
        Signal { name: "decreaseAction" }
        Signal { name: "scrollUpAction" }
        Signal { name: "scrollDownAction" }
        Signal { name: "scrollLeftAction" }
        Signal { name: "scrollRightAction" }
        Signal { name: "previousPageAction" }
        Signal { name: "nextPageAction" }
        Method { name: "valueChanged" }
        Method { name: "cursorPositionChanged" }
        Method {
            name: "setIgnored"
            Parameter { name: "ignored"; type: "bool" }
        }
    }
    Component {
        file: "private/qquickitemanimation_p.h"
        name: "QQuickAnchorAnimation"
        prototype: "QQuickAbstractAnimation"
        exports: [
            "QtQuick/AnchorAnimation 2.0",
            "QtQuick/AnchorAnimation 2.12"
        ]
        exportMetaObjectRevisions: [0, 12]
        Property { name: "targets"; type: "QQuickItem"; isList: true; isReadonly: true }
        Property { name: "duration"; type: "int" }
        Property { name: "easing"; type: "QEasingCurve" }
        Signal {
            name: "durationChanged"
            Parameter { type: "int" }
        }
        Signal {
            name: "easingChanged"
            Parameter { type: "QEasingCurve" }
        }
    }
    Component {
        file: "private/qquickstateoperations_p.h"
        name: "QQuickAnchorChanges"
        prototype: "QQuickStateOperation"
        exports: ["QtQuick/AnchorChanges 2.0"]
        exportMetaObjectRevisions: [0]
        Property { name: "target"; type: "QQuickItem"; isPointer: true }
        Property { name: "anchors"; type: "QQuickAnchorSet"; isReadonly: true; isPointer: true }
    }
    Component {
        file: "private/qquickstateoperations_p.h"
        name: "QQuickAnchorSet"
        prototype: "QObject"
        Property { name: "left"; type: "QQmlScriptString" }
        Property { name: "right"; type: "QQmlScriptString" }
        Property { name: "horizontalCenter"; type: "QQmlScriptString" }
        Property { name: "top"; type: "QQmlScriptString" }
        Property { name: "bottom"; type: "QQmlScriptString" }
        Property { name: "verticalCenter"; type: "QQmlScriptString" }
        Property { name: "baseline"; type: "QQmlScriptString" }
    }
    Component {
        file: "private/qquickanchors_p.h"
        name: "QQuickAnchors"
        prototype: "QObject"
        Enum {
            name: "Anchors"
            alias: "Anchor"
            isFlag: true
            values: [
                "InvalidAnchor",
                "LeftAnchor",
                "RightAnchor",
                "TopAnchor",
                "BottomAnchor",
                "HCenterAnchor",
                "VCenterAnchor",
                "BaselineAnchor",
                "Horizontal_Mask",
                "Vertical_Mask"
            ]
        }
        Property { name: "left"; type: "QQuickAnchorLine" }
        Property { name: "right"; type: "QQuickAnchorLine" }
        Property { name: "horizontalCenter"; type: "QQuickAnchorLine" }
        Property { name: "top"; type: "QQuickAnchorLine" }
        Property { name: "bottom"; type: "QQuickAnchorLine" }
        Property { name: "verticalCenter"; type: "QQuickAnchorLine" }
        Property { name: "baseline"; type: "QQuickAnchorLine" }
        Property { name: "margins"; type: "double" }
        Property { name: "leftMargin"; type: "double" }
        Property { name: "rightMargin"; type: "double" }
        Property { name: "horizontalCenterOffset"; type: "double" }
        Property { name: "topMargin"; type: "double" }
        Property { name: "bottomMargin"; type: "double" }
        Property { name: "verticalCenterOffset"; type: "double" }
        Property { name: "baselineOffset"; type: "double" }
        Property { name: "fill"; type: "QQuickItem"; isPointer: true }
        Property { name: "centerIn"; type: "QQuickItem"; isPointer: true }
        Property { name: "alignWhenCentered"; type: "bool" }
        Signal { name: "centerAlignedChanged" }
    }
    Component {
        file: "private/qquickanimatedimage_p.h"
        name: "QQuickAnimatedImage"
        prototype: "QQuickImage"
        exports: [
            "QtQuick/AnimatedImage 2.0",
            "QtQuick/AnimatedImage 2.1",
            "QtQuick/AnimatedImage 2.11",
            "QtQuick/AnimatedImage 2.14",
            "QtQuick/AnimatedImage 2.15",
            "QtQuick/AnimatedImage 2.3",
            "QtQuick/AnimatedImage 2.4",
            "QtQuick/AnimatedImage 2.5",
            "QtQuick/AnimatedImage 2.7"
        ]
        exportMetaObjectRevisions: [0, 1, 11, 14, 15, 3, 4, 5, 7]
        Property { name: "playing"; type: "bool" }
        Property { name: "paused"; type: "bool" }
        Property { name: "currentFrame"; type: "int" }
        Property { name: "frameCount"; type: "int"; isReadonly: true }
        Property { name: "speed"; revision: 11; type: "double" }
        Property { name: "sourceSize"; type: "QSize"; isReadonly: true }
        Signal { name: "frameChanged" }
        Signal { name: "currentFrameChanged" }
        Signal { name: "speedChanged"; revision: 11 }
        Method { name: "movieUpdate" }
        Method { name: "movieRequestFinished" }
        Method { name: "playingStatusChanged" }
        Method { name: "onCacheChanged" }
    }
    Component {
        file: "private/qquickanimatedsprite_p.h"
        name: "QQuickAnimatedSprite"
        defaultProperty: "data"
        prototype: "QQuickItem"
        exports: [
            "QtQuick/AnimatedSprite 2.0",
            "QtQuick/AnimatedSprite 2.1",
            "QtQuick/AnimatedSprite 2.11",
            "QtQuick/AnimatedSprite 2.12",
            "QtQuick/AnimatedSprite 2.15",
            "QtQuick/AnimatedSprite 2.4",
            "QtQuick/AnimatedSprite 2.7"
        ]
        exportMetaObjectRevisions: [0, 1, 11, 12, 15, 4, 7]
        Enum {
            name: "LoopParameters"
            values: ["Infinite"]
        }
        Enum {
            name: "FinishBehavior"
            values: ["FinishAtInitialFrame", "FinishAtFinalFrame"]
        }
        Property { name: "running"; type: "bool" }
        Property { name: "interpolate"; type: "bool" }
        Property { name: "source"; type: "QUrl" }
        Property { name: "reverse"; type: "bool" }
        Property { name: "frameSync"; type: "bool" }
        Property { name: "frameCount"; type: "int" }
        Property { name: "frameHeight"; type: "int" }
        Property { name: "frameWidth"; type: "int" }
        Property { name: "frameX"; type: "int" }
        Property { name: "frameY"; type: "int" }
        Property { name: "frameRate"; type: "double" }
        Property { name: "frameDuration"; type: "int" }
        Property { name: "loops"; type: "int" }
        Property { name: "paused"; type: "bool" }
        Property { name: "currentFrame"; type: "int" }
        Property { name: "finishBehavior"; revision: 15; type: "FinishBehavior" }
        Signal {
            name: "pausedChanged"
            Parameter { name: "arg"; type: "bool" }
        }
        Signal {
            name: "runningChanged"
            Parameter { name: "arg"; type: "bool" }
        }
        Signal {
            name: "interpolateChanged"
            Parameter { name: "arg"; type: "bool" }
        }
        Signal {
            name: "sourceChanged"
            Parameter { name: "arg"; type: "QUrl" }
        }
        Signal {
            name: "reverseChanged"
            Parameter { name: "arg"; type: "bool" }
        }
        Signal {
            name: "frameSyncChanged"
            Parameter { name: "arg"; type: "bool" }
        }
        Signal {
            name: "frameCountChanged"
            Parameter { name: "arg"; type: "int" }
        }
        Signal {
            name: "frameHeightChanged"
            Parameter { name: "arg"; type: "int" }
        }
        Signal {
            name: "frameWidthChanged"
            Parameter { name: "arg"; type: "int" }
        }
        Signal {
            name: "frameXChanged"
            Parameter { name: "arg"; type: "int" }
        }
        Signal {
            name: "frameYChanged"
            Parameter { name: "arg"; type: "int" }
        }
        Signal {
            name: "frameRateChanged"
            Parameter { name: "arg"; type: "double" }
        }
        Signal {
            name: "frameDurationChanged"
            Parameter { name: "arg"; type: "int" }
        }
        Signal {
            name: "loopsChanged"
            Parameter { name: "arg"; type: "int" }
        }
        Signal {
            name: "currentFrameChanged"
            Parameter { name: "arg"; type: "int" }
        }
        Signal {
            name: "finishBehaviorChanged"
            revision: 15
            Parameter { name: "arg"; type: "FinishBehavior" }
        }
        Signal { name: "finished"; revision: 12 }
        Method { name: "start" }
        Method { name: "stop" }
        Method { name: "restart" }
        Method {
            name: "advance"
            Parameter { name: "frames"; type: "int" }
        }
        Method { name: "advance" }
        Method { name: "pause" }
        Method { name: "resume" }
        Method {
            name: "setRunning"
            Parameter { name: "arg"; type: "bool" }
        }
        Method {
            name: "setPaused"
            Parameter { name: "arg"; type: "bool" }
        }
        Method {
            name: "setInterpolate"
            Parameter { name: "arg"; type: "bool" }
        }
        Method {
            name: "setSource"
            Parameter { name: "arg"; type: "QUrl" }
        }
        Method {
            name: "setReverse"
            Parameter { name: "arg"; type: "bool" }
        }
        Method {
            name: "setFrameSync"
            Parameter { name: "arg"; type: "bool" }
        }
        Method {
            name: "setFrameCount"
            Parameter { name: "arg"; type: "int" }
        }
        Method {
            name: "setFrameHeight"
            Parameter { name: "arg"; type: "int" }
        }
        Method {
            name: "setFrameWidth"
            Parameter { name: "arg"; type: "int" }
        }
        Method {
            name: "setFrameX"
            Parameter { name: "arg"; type: "int" }
        }
        Method {
            name: "setFrameY"
            Parameter { name: "arg"; type: "int" }
        }
        Method {
            name: "setFrameRate"
            Parameter { name: "arg"; type: "double" }
        }
        Method {
            name: "setFrameDuration"
            Parameter { name: "arg"; type: "int" }
        }
        Method { name: "resetFrameRate" }
        Method { name: "resetFrameDuration" }
        Method {
            name: "setLoops"
            Parameter { name: "arg"; type: "int" }
        }
        Method {
            name: "setCurrentFrame"
            Parameter { name: "arg"; type: "int" }
        }
        Method { name: "createEngine" }
        Method { name: "reset" }
    }
    Component {
        file: "private/qquickanimationcontroller_p.h"
        name: "QQuickAnimationController"
        defaultProperty: "animation"
        prototype: "QObject"
        exports: ["QtQuick/AnimationController 2.0"]
        exportMetaObjectRevisions: [0]
        Property { name: "progress"; type: "double" }
        Property { name: "animation"; type: "QQuickAbstractAnimation"; isPointer: true }
        Method { name: "reload" }
        Method { name: "completeToBeginning" }
        Method { name: "completeToEnd" }
        Method { name: "componentFinalized" }
        Method { name: "updateProgress" }
    }
    Component {
        file: "private/qquickanimation_p.h"
        name: "QQuickAnimationGroup"
        defaultProperty: "animations"
        prototype: "QQuickAbstractAnimation"
        Property { name: "animations"; type: "QQuickAbstractAnimation"; isList: true; isReadonly: true }
    }
    Component {
        file: "private/qquickanimator_p.h"
        name: "QQuickAnimator"
        prototype: "QQuickAbstractAnimation"
        exports: ["QtQuick/Animator 2.12", "QtQuick/Animator 2.2"]
        isCreatable: false
        exportMetaObjectRevisions: [12, 2]
        Property { name: "target"; type: "QQuickItem"; isPointer: true }
        Property { name: "easing"; type: "QEasingCurve" }
        Property { name: "duration"; type: "int" }
        Property { name: "to"; type: "double" }
        Property { name: "from"; type: "double" }
        Signal {
            name: "targetItemChanged"
            Parameter { type: "QQuickItem"; isPointer: true }
        }
        Signal {
            name: "durationChanged"
            Parameter { name: "duration"; type: "int" }
        }
        Signal {
            name: "easingChanged"
            Parameter { name: "curve"; type: "QEasingCurve" }
        }
        Signal {
            name: "toChanged"
            Parameter { name: "to"; type: "double" }
        }
        Signal {
            name: "fromChanged"
            Parameter { name: "from"; type: "double" }
        }
    }
    Component {
        file: "private/qquickapplication_p.h"
        name: "QQuickApplication"
        prototype: "QQmlApplication"
        exports: ["QtQuick/Application 2.0"]
        isCreatable: false
        exportMetaObjectRevisions: [0]
        Property { name: "active"; type: "bool"; isReadonly: true }
        Property { name: "layoutDirection"; type: "Qt::LayoutDirection"; isReadonly: true }
        Property { name: "supportsMultipleWindows"; type: "bool"; isReadonly: true }
        Property { name: "state"; type: "Qt::ApplicationState"; isReadonly: true }
        Property { name: "font"; type: "QFont"; isReadonly: true }
        Property { name: "displayName"; type: "string" }
        Property { name: "screens"; type: "QQuickScreenInfo"; isList: true; isReadonly: true }
        Signal {
            name: "stateChanged"
            Parameter { name: "state"; type: "Qt::ApplicationState" }
        }
        Method { name: "updateScreens" }
    }
    Component {
        file: "private/qquickpositioners_p.h"
        name: "QQuickBasePositioner"
        prototype: "QQuickImplicitSizeItem"
        exports: [
            "QtQuick/Positioner 2.0",
            "QtQuick/Positioner 2.1",
            "QtQuick/Positioner 2.11",
            "QtQuick/Positioner 2.4",
            "QtQuick/Positioner 2.6",
            "QtQuick/Positioner 2.7",
            "QtQuick/Positioner 2.9"
        ]
        isCreatable: false
        exportMetaObjectRevisions: [0, 1, 11, 4, 6, 7, 9]
        attachedType: "QQuickPositionerAttached"
        Property { name: "spacing"; type: "double" }
        Property { name: "populate"; type: "QQuickTransition"; isPointer: true }
        Property { name: "move"; type: "QQuickTransition"; isPointer: true }
        Property { name: "add"; type: "QQuickTransition"; isPointer: true }
        Property { name: "padding"; revision: 6; type: "double" }
        Property { name: "topPadding"; revision: 6; type: "double" }
        Property { name: "leftPadding"; revision: 6; type: "double" }
        Property { name: "rightPadding"; revision: 6; type: "double" }
        Property { name: "bottomPadding"; revision: 6; type: "double" }
        Signal { name: "paddingChanged"; revision: 6 }
        Signal { name: "topPaddingChanged"; revision: 6 }
        Signal { name: "leftPaddingChanged"; revision: 6 }
        Signal { name: "rightPaddingChanged"; revision: 6 }
        Signal { name: "bottomPaddingChanged"; revision: 6 }
        Signal { name: "positioningComplete"; revision: 9 }
        Method { name: "prePositioning" }
        Method { name: "forceLayout"; revision: 9 }
    }
    Component {
        file: "private/qquickbehavior_p.h"
        name: "QQuickBehavior"
        defaultProperty: "animation"
        prototype: "QObject"
        exports: [
            "QtQuick/Behavior 2.0",
            "QtQuick/Behavior 2.13",
            "QtQuick/Behavior 2.15"
        ]
        exportMetaObjectRevisions: [0, 13, 15]
        Property { name: "animation"; type: "QQuickAbstractAnimation"; isPointer: true }
        Property { name: "enabled"; type: "bool" }
        Property { name: "targetValue"; revision: 13; type: "QVariant"; isReadonly: true }
        Property { name: "targetProperty"; revision: 15; type: "QQmlProperty"; isReadonly: true }
        Method { name: "componentFinalized" }
    }
    Component {
        file: "private/qquickborderimage_p.h"
        name: "QQuickBorderImage"
        prototype: "QQuickImageBase"
        exports: [
            "QtQuick/BorderImage 2.0",
            "QtQuick/BorderImage 2.1",
            "QtQuick/BorderImage 2.11",
            "QtQuick/BorderImage 2.14",
            "QtQuick/BorderImage 2.15",
            "QtQuick/BorderImage 2.4",
            "QtQuick/BorderImage 2.7"
        ]
        exportMetaObjectRevisions: [0, 1, 11, 14, 15, 4, 7]
        Enum {
            name: "TileMode"
            values: ["Stretch", "Repeat", "Round"]
        }
        Property { name: "border"; type: "QQuickScaleGrid"; isReadonly: true; isPointer: true }
        Property { name: "horizontalTileMode"; type: "TileMode" }
        Property { name: "verticalTileMode"; type: "TileMode" }
        Property { name: "sourceSize"; type: "QSize"; isReadonly: true }
        Method { name: "doUpdate" }
        Method { name: "requestFinished" }
        Method { name: "sciRequestFinished" }
    }
    Component {
        file: "private/qquickshadereffectmesh_p.h"
        name: "QQuickBorderImageMesh"
        prototype: "QQuickShaderEffectMesh"
        exports: ["QtQuick/BorderImageMesh 2.8"]
        exportMetaObjectRevisions: [8]
        Enum {
            name: "TileMode"
            values: ["Stretch", "Repeat", "Round"]
        }
        Property { name: "border"; type: "QQuickScaleGrid"; isReadonly: true; isPointer: true }
        Property { name: "size"; type: "QSize" }
        Property { name: "horizontalTileMode"; type: "TileMode" }
        Property { name: "verticalTileMode"; type: "TileMode" }
    }
    Component {
        file: "private/qquickcanvasitem_p.h"
        name: "QQuickCanvasItem"
        defaultProperty: "data"
        prototype: "QQuickItem"
        exports: [
            "QtQuick/Canvas 2.0",
            "QtQuick/Canvas 2.1",
            "QtQuick/Canvas 2.11",
            "QtQuick/Canvas 2.4",
            "QtQuick/Canvas 2.7"
        ]
        exportMetaObjectRevisions: [0, 1, 11, 4, 7]
        Enum {
            name: "RenderTarget"
            values: ["Image", "FramebufferObject"]
        }
        Enum {
            name: "RenderStrategy"
            values: ["Immediate", "Threaded", "Cooperative"]
        }
        Property { name: "available"; type: "bool"; isReadonly: true }
        Property { name: "contextType"; type: "string" }
        Property { name: "context"; type: "QJSValue"; isReadonly: true }
        Property { name: "canvasSize"; type: "QSizeF" }
        Property { name: "tileSize"; type: "QSize" }
        Property { name: "canvasWindow"; type: "QRectF" }
        Property { name: "renderTarget"; type: "RenderTarget" }
        Property { name: "renderStrategy"; type: "RenderStrategy" }
        Signal {
            name: "paint"
            Parameter { name: "region"; type: "QRect" }
        }
        Signal { name: "painted" }
        Signal { name: "imageLoaded" }
        Method {
            name: "loadImage"
            Parameter { name: "url"; type: "QUrl" }
        }
        Method {
            name: "unloadImage"
            Parameter { name: "url"; type: "QUrl" }
        }
        Method {
            name: "isImageLoaded"
            type: "bool"
            Parameter { name: "url"; type: "QUrl" }
        }
        Method {
            name: "isImageLoading"
            type: "bool"
            Parameter { name: "url"; type: "QUrl" }
        }
        Method {
            name: "isImageError"
            type: "bool"
            Parameter { name: "url"; type: "QUrl" }
        }
        Method { name: "sceneGraphInitialized" }
        Method { name: "checkAnimationCallbacks" }
        Method { name: "invalidateSceneGraph" }
        Method { name: "schedulePolish" }
        Method {
            name: "getContext"
            Parameter { name: "args"; type: "QQmlV4Function"; isPointer: true }
        }
        Method {
            name: "requestAnimationFrame"
            Parameter { name: "args"; type: "QQmlV4Function"; isPointer: true }
        }
        Method {
            name: "cancelRequestAnimationFrame"
            Parameter { name: "args"; type: "QQmlV4Function"; isPointer: true }
        }
        Method { name: "requestPaint" }
        Method {
            name: "markDirty"
            Parameter { name: "dirtyRect"; type: "QRectF" }
        }
        Method { name: "markDirty" }
        Method {
            name: "save"
            type: "bool"
            Parameter { name: "filename"; type: "string" }
        }
        Method {
            name: "toDataURL"
            type: "string"
            Parameter { name: "type"; type: "string" }
        }
        Method { name: "toDataURL"; type: "string" }
        Method { name: "delayedCreate" }
    }
    Component {
        file: "private/qquickevents_p_p.h"
        name: "QQuickCloseEvent"
        prototype: "QObject"
        Property { name: "accepted"; type: "bool" }
    }
    Component {
        file: "private/qquickanimation_p.h"
        name: "QQuickColorAnimation"
        prototype: "QQuickPropertyAnimation"
        exports: ["QtQuick/ColorAnimation 2.0", "QtQuick/ColorAnimation 2.12"]
        exportMetaObjectRevisions: [0, 12]
        Property { name: "from"; type: "QColor" }
        Property { name: "to"; type: "QColor" }
    }
    Component {
        file: "private/qquickvaluetypes_p.h"
        name: "QQuickColorSpaceValueType"
        exports: ["QtQuick/ColorSpace 2.15"]
        isCreatable: false
        exportMetaObjectRevisions: [15]
        Enum {
            name: "NamedColorSpace"
            values: [
                "Unknown",
                "SRgb",
                "SRgbLinear",
                "AdobeRgb",
                "DisplayP3",
                "ProPhotoRgb"
            ]
        }
        Enum {
            name: "Primaries"
            values: ["Custom", "SRgb", "AdobeRgb", "DciP3D65", "ProPhotoRgb"]
        }
        Enum {
            name: "TransferFunction"
            values: ["Custom", "Linear", "Gamma", "SRgb", "ProPhotoRgb"]
        }
        Property { name: "namedColorSpace"; type: "NamedColorSpace" }
        Property { name: "primaries"; type: "Primaries" }
        Property { name: "transferFunction"; type: "TransferFunction" }
        Property { name: "gamma"; type: "float" }
    }
    Component {
        file: "private/qquickpositioners_p.h"
        name: "QQuickColumn"
        prototype: "QQuickBasePositioner"
        exports: [
            "QtQuick/Column 2.0",
            "QtQuick/Column 2.1",
            "QtQuick/Column 2.11",
            "QtQuick/Column 2.4",
            "QtQuick/Column 2.6",
            "QtQuick/Column 2.7",
            "QtQuick/Column 2.9"
        ]
        exportMetaObjectRevisions: [0, 1, 11, 4, 6, 7, 9]
    }
    Component {
        file: "private/qquickpath_p.h"
        name: "QQuickCurve"
        prototype: "QQuickPathElement"
        Property { name: "x"; type: "double" }
        Property { name: "y"; type: "double" }
        Property { name: "relativeX"; type: "double" }
        Property { name: "relativeY"; type: "double" }
    }
    Component {
        file: "private/qquickvalidator_p.h"
        name: "QQuickDoubleValidator"
        prototype: "QDoubleValidator"
        exports: ["QtQuick/DoubleValidator 2.0"]
        exportMetaObjectRevisions: [0]
        Property { name: "locale"; type: "string" }
        Signal { name: "localeNameChanged" }
    }
    Component {
        file: "private/qquickdrag_p.h"
        name: "QQuickDrag"
        prototype: "QObject"
        exports: ["QtQuick/Drag 2.0"]
        isCreatable: false
        exportMetaObjectRevisions: [0]
        attachedType: "QQuickDragAttached"
        Enum {
            name: "DragType"
            values: ["None", "Automatic", "Internal"]
        }
        Enum {
            name: "Axis"
            values: ["XAxis", "YAxis", "XAndYAxis", "XandYAxis"]
        }
        Property { name: "target"; type: "QQuickItem"; isPointer: true }
        Property { name: "axis"; type: "Axis" }
        Property { name: "minimumX"; type: "double" }
        Property { name: "maximumX"; type: "double" }
        Property { name: "minimumY"; type: "double" }
        Property { name: "maximumY"; type: "double" }
        Property { name: "active"; type: "bool"; isReadonly: true }
        Property { name: "filterChildren"; type: "bool" }
        Property { name: "smoothed"; type: "bool" }
        Property { name: "threshold"; type: "double" }
    }
    Component {
        file: "private/qquickdrag_p.h"
        name: "QQuickDragAttached"
        prototype: "QObject"
        Property { name: "active"; type: "bool" }
        Property { name: "source"; type: "QObject"; isPointer: true }
        Property { name: "target"; type: "QObject"; isReadonly: true; isPointer: true }
        Property { name: "hotSpot"; type: "QPointF" }
        Property { name: "imageSource"; type: "QUrl" }
        Property { name: "keys"; type: "QStringList" }
        Property { name: "mimeData"; type: "QVariantMap" }
        Property { name: "supportedActions"; type: "Qt::DropActions" }
        Property { name: "proposedAction"; type: "Qt::DropAction" }
        Property { name: "dragType"; type: "QQuickDrag::DragType" }
        Signal { name: "dragStarted" }
        Signal {
            name: "dragFinished"
            Parameter { name: "dropAction"; type: "Qt::DropAction" }
        }
        Method {
            name: "start"
            Parameter { type: "QQmlV4Function"; isPointer: true }
        }
        Method {
            name: "startDrag"
            Parameter { type: "QQmlV4Function"; isPointer: true }
        }
        Method { name: "cancel" }
        Method { name: "drop"; type: "int" }
    }
    Component {
        file: "private/qquickdragaxis_p.h"
        name: "QQuickDragAxis"
        prototype: "QObject"
        exports: ["QtQuick/DragAxis 2.12"]
        isCreatable: false
        exportMetaObjectRevisions: [12]
        Property { name: "minimum"; type: "double" }
        Property { name: "maximum"; type: "double" }
        Property { name: "enabled"; type: "bool" }
    }
    Component {
        file: "private/qquickdraghandler_p.h"
        name: "QQuickDragHandler"
        prototype: "QQuickMultiPointHandler"
        exports: [
            "QtQuick/DragHandler 2.12",
            "QtQuick/DragHandler 2.14",
            "QtQuick/DragHandler 2.15"
        ]
        exportMetaObjectRevisions: [12, 14, 15]
        Enum {
            name: "SnapMode"
            values: [
                "NoSnap",
                "SnapAuto",
                "SnapIfPressedOutsideTarget",
                "SnapAlways"
            ]
        }
        Property { name: "xAxis"; type: "QQuickDragAxis"; isReadonly: true; isPointer: true }
        Property { name: "yAxis"; type: "QQuickDragAxis"; isReadonly: true; isPointer: true }
        Property { name: "translation"; type: "QVector2D"; isReadonly: true }
        Property { name: "snapMode"; revision: 14; type: "SnapMode" }
        Signal { name: "snapModeChanged"; revision: 14 }
    }
    Component {
        file: "private/qquickdroparea_p.h"
        name: "QQuickDropArea"
        defaultProperty: "data"
        prototype: "QQuickItem"
        exports: [
            "QtQuick/DropArea 2.0",
            "QtQuick/DropArea 2.1",
            "QtQuick/DropArea 2.11",
            "QtQuick/DropArea 2.4",
            "QtQuick/DropArea 2.7"
        ]
        exportMetaObjectRevisions: [0, 1, 11, 4, 7]
        Property { name: "containsDrag"; type: "bool"; isReadonly: true }
        Property { name: "keys"; type: "QStringList" }
        Property { name: "drag"; type: "QQuickDropAreaDrag"; isReadonly: true; isPointer: true }
        Signal { name: "sourceChanged" }
        Signal {
            name: "entered"
            Parameter { name: "drag"; type: "QQuickDropEvent"; isPointer: true }
        }
        Signal { name: "exited" }
        Signal {
            name: "positionChanged"
            Parameter { name: "drag"; type: "QQuickDropEvent"; isPointer: true }
        }
        Signal {
            name: "dropped"
            Parameter { name: "drop"; type: "QQuickDropEvent"; isPointer: true }
        }
    }
    Component {
        file: "private/qquickdroparea_p.h"
        name: "QQuickDropAreaDrag"
        prototype: "QObject"
        Property { name: "x"; type: "double"; isReadonly: true }
        Property { name: "y"; type: "double"; isReadonly: true }
        Property { name: "source"; type: "QObject"; isReadonly: true; isPointer: true }
        Signal { name: "positionChanged" }
    }
    Component {
        file: "private/qquickdroparea_p.h"
        name: "QQuickDropEvent"
        prototype: "QObject"
        Property { name: "x"; type: "double"; isReadonly: true }
        Property { name: "y"; type: "double"; isReadonly: true }
        Property { name: "source"; type: "QObject"; isReadonly: true; isPointer: true }
        Property { name: "keys"; type: "QStringList"; isReadonly: true }
        Property { name: "supportedActions"; type: "Qt::DropActions"; isReadonly: true }
        Property { name: "proposedAction"; type: "Qt::DropActions"; isReadonly: true }
        Property { name: "action"; type: "Qt::DropAction" }
        Property { name: "accepted"; type: "bool" }
        Property { name: "hasColor"; type: "bool"; isReadonly: true }
        Property { name: "hasHtml"; type: "bool"; isReadonly: true }
        Property { name: "hasText"; type: "bool"; isReadonly: true }
        Property { name: "hasUrls"; type: "bool"; isReadonly: true }
        Property { name: "colorData"; type: "QVariant"; isReadonly: true }
        Property { name: "html"; type: "string"; isReadonly: true }
        Property { name: "text"; type: "string"; isReadonly: true }
        Property { name: "urls"; type: "QList<QUrl>"; isReadonly: true }
        Property { name: "formats"; type: "QStringList"; isReadonly: true }
        Method {
            name: "getDataAsString"
            Parameter { type: "QQmlV4Function"; isPointer: true }
        }
        Method {
            name: "getDataAsArrayBuffer"
            Parameter { type: "QQmlV4Function"; isPointer: true }
        }
        Method {
            name: "acceptProposedAction"
            Parameter { type: "QQmlV4Function"; isPointer: true }
        }
        Method {
            name: "accept"
            Parameter { type: "QQmlV4Function"; isPointer: true }
        }
    }
    Component {
        file: "private/qquickitem_p.h"
        name: "QQuickEnterKeyAttached"
        prototype: "QObject"
        exports: ["QtQuick/EnterKey 2.6"]
        isCreatable: false
        exportMetaObjectRevisions: [6]
        attachedType: "QQuickEnterKeyAttached"
        Property { name: "type"; type: "Qt::EnterKeyType" }
    }
    Component {
        file: "private/qquickevents_p_p.h"
        name: "QQuickEventPoint"
        prototype: "QObject"
        exports: ["QtQuick/EventPoint 2.12"]
        isCreatable: false
        exportMetaObjectRevisions: [12]
        Enum {
            name: "States"
            alias: "State"
            isFlag: true
            values: ["Pressed", "Updated", "Stationary", "Released"]
        }
        Enum {
            name: "GrabTransition"
            values: [
                "GrabPassive",
                "UngrabPassive",
                "CancelGrabPassive",
                "OverrideGrabPassive",
                "GrabExclusive",
                "UngrabExclusive",
                "CancelGrabExclusive"
            ]
        }
        Property { name: "event"; type: "QQuickPointerEvent"; isReadonly: true; isPointer: true }
        Property { name: "position"; type: "QPointF"; isReadonly: true }
        Property { name: "scenePosition"; type: "QPointF"; isReadonly: true }
        Property { name: "scenePressPosition"; type: "QPointF"; isReadonly: true }
        Property { name: "sceneGrabPosition"; type: "QPointF"; isReadonly: true }
        Property { name: "state"; type: "State"; isReadonly: true }
        Property { name: "pointId"; type: "int"; isReadonly: true }
        Property { name: "timeHeld"; type: "double"; isReadonly: true }
        Property { name: "velocity"; type: "QVector2D"; isReadonly: true }
        Property { name: "accepted"; type: "bool" }
        Property { name: "exclusiveGrabber"; type: "QObject"; isPointer: true }
    }
    Component {
        file: "private/qquickevents_p_p.h"
        name: "QQuickEventTabletPoint"
        prototype: "QQuickEventPoint"
        exports: ["QtQuick/EventTabletPoint 2.15"]
        isCreatable: false
        exportMetaObjectRevisions: [15]
        Property { name: "rotation"; type: "double"; isReadonly: true }
        Property { name: "pressure"; type: "double"; isReadonly: true }
        Property { name: "tangentialPressure"; type: "double"; isReadonly: true }
        Property { name: "tilt"; type: "QVector2D"; isReadonly: true }
    }
    Component {
        file: "private/qquickevents_p_p.h"
        name: "QQuickEventTouchPoint"
        prototype: "QQuickEventPoint"
        exports: ["QtQuick/EventTouchPoint 2.12"]
        isCreatable: false
        exportMetaObjectRevisions: [12]
        Property { name: "rotation"; type: "double"; isReadonly: true }
        Property { name: "pressure"; type: "double"; isReadonly: true }
        Property { name: "ellipseDiameters"; type: "QSizeF"; isReadonly: true }
        Property { name: "uniqueId"; type: "QPointingDeviceUniqueId"; isReadonly: true }
    }
    Component {
        file: "private/qquickflickable_p.h"
        name: "QQuickFlickable"
        defaultProperty: "flickableData"
        prototype: "QQuickItem"
        exports: [
            "QtQuick/Flickable 2.0",
            "QtQuick/Flickable 2.1",
            "QtQuick/Flickable 2.10",
            "QtQuick/Flickable 2.11",
            "QtQuick/Flickable 2.12",
            "QtQuick/Flickable 2.4",
            "QtQuick/Flickable 2.7",
            "QtQuick/Flickable 2.9"
        ]
        exportMetaObjectRevisions: [0, 1, 10, 11, 12, 4, 7, 9]
        Enum {
            name: "BoundsBehavior"
            alias: "BoundsBehaviorFlag"
            isFlag: true
            values: [
                "StopAtBounds",
                "DragOverBounds",
                "OvershootBounds",
                "DragAndOvershootBounds"
            ]
        }
        Enum {
            name: "BoundsMovement"
            values: ["FollowBoundsBehavior"]
        }
        Enum {
            name: "FlickableDirection"
            values: [
                "AutoFlickDirection",
                "HorizontalFlick",
                "VerticalFlick",
                "HorizontalAndVerticalFlick",
                "AutoFlickIfNeeded"
            ]
        }
        Property { name: "contentWidth"; type: "double" }
        Property { name: "contentHeight"; type: "double" }
        Property { name: "contentX"; type: "double" }
        Property { name: "contentY"; type: "double" }
        Property { name: "contentItem"; type: "QQuickItem"; isReadonly: true; isPointer: true }
        Property { name: "topMargin"; type: "double" }
        Property { name: "bottomMargin"; type: "double" }
        Property { name: "originY"; type: "double"; isReadonly: true }
        Property { name: "leftMargin"; type: "double" }
        Property { name: "rightMargin"; type: "double" }
        Property { name: "originX"; type: "double"; isReadonly: true }
        Property { name: "horizontalVelocity"; type: "double"; isReadonly: true }
        Property { name: "verticalVelocity"; type: "double"; isReadonly: true }
        Property { name: "boundsBehavior"; type: "BoundsBehavior" }
        Property { name: "boundsMovement"; revision: 10; type: "BoundsMovement" }
        Property { name: "rebound"; type: "QQuickTransition"; isPointer: true }
        Property { name: "maximumFlickVelocity"; type: "double" }
        Property { name: "flickDeceleration"; type: "double" }
        Property { name: "moving"; type: "bool"; isReadonly: true }
        Property { name: "movingHorizontally"; type: "bool"; isReadonly: true }
        Property { name: "movingVertically"; type: "bool"; isReadonly: true }
        Property { name: "flicking"; type: "bool"; isReadonly: true }
        Property { name: "flickingHorizontally"; type: "bool"; isReadonly: true }
        Property { name: "flickingVertically"; type: "bool"; isReadonly: true }
        Property { name: "dragging"; type: "bool"; isReadonly: true }
        Property { name: "draggingHorizontally"; type: "bool"; isReadonly: true }
        Property { name: "draggingVertically"; type: "bool"; isReadonly: true }
        Property { name: "flickableDirection"; type: "FlickableDirection" }
        Property { name: "interactive"; type: "bool" }
        Property { name: "pressDelay"; type: "int" }
        Property { name: "atXEnd"; type: "bool"; isReadonly: true }
        Property { name: "atYEnd"; type: "bool"; isReadonly: true }
        Property { name: "atXBeginning"; type: "bool"; isReadonly: true }
        Property { name: "atYBeginning"; type: "bool"; isReadonly: true }
        Property {
            name: "visibleArea"
            type: "QQuickFlickableVisibleArea"
            isReadonly: true
            isPointer: true
        }
        Property { name: "pixelAligned"; type: "bool" }
        Property { name: "synchronousDrag"; revision: 12; type: "bool" }
        Property { name: "horizontalOvershoot"; revision: 9; type: "double"; isReadonly: true }
        Property { name: "verticalOvershoot"; revision: 9; type: "double"; isReadonly: true }
        Property { name: "flickableData"; type: "QObject"; isList: true; isReadonly: true }
        Property { name: "flickableChildren"; type: "QQuickItem"; isList: true; isReadonly: true }
        Signal { name: "isAtBoundaryChanged" }
        Signal { name: "boundsMovementChanged"; revision: 10 }
        Signal { name: "movementStarted" }
        Signal { name: "movementEnded" }
        Signal { name: "flickStarted" }
        Signal { name: "flickEnded" }
        Signal { name: "dragStarted" }
        Signal { name: "dragEnded" }
        Signal { name: "synchronousDragChanged"; revision: 12 }
        Signal { name: "horizontalOvershootChanged"; revision: 9 }
        Signal { name: "verticalOvershootChanged"; revision: 9 }
        Method { name: "movementStarting" }
        Method { name: "movementEnding" }
        Method {
            name: "movementEnding"
            Parameter { name: "hMovementEnding"; type: "bool" }
            Parameter { name: "vMovementEnding"; type: "bool" }
        }
        Method { name: "velocityTimelineCompleted" }
        Method { name: "timelineCompleted" }
        Method {
            name: "resizeContent"
            Parameter { name: "w"; type: "double" }
            Parameter { name: "h"; type: "double" }
            Parameter { name: "center"; type: "QPointF" }
        }
        Method { name: "returnToBounds" }
        Method {
            name: "flick"
            Parameter { name: "xVelocity"; type: "double" }
            Parameter { name: "yVelocity"; type: "double" }
        }
        Method { name: "cancelFlick" }
    }
    Component {
        file: "private/qquickflickable_p_p.h"
        name: "QQuickFlickableVisibleArea"
        prototype: "QObject"
        Property { name: "xPosition"; type: "double"; isReadonly: true }
        Property { name: "yPosition"; type: "double"; isReadonly: true }
        Property { name: "widthRatio"; type: "double"; isReadonly: true }
        Property { name: "heightRatio"; type: "double"; isReadonly: true }
        Signal {
            name: "xPositionChanged"
            Parameter { name: "xPosition"; type: "double" }
        }
        Signal {
            name: "yPositionChanged"
            Parameter { name: "yPosition"; type: "double" }
        }
        Signal {
            name: "widthRatioChanged"
            Parameter { name: "widthRatio"; type: "double" }
        }
        Signal {
            name: "heightRatioChanged"
            Parameter { name: "heightRatio"; type: "double" }
        }
    }
    Component {
        file: "private/qquickflipable_p.h"
        name: "QQuickFlipable"
        defaultProperty: "data"
        prototype: "QQuickItem"
        exports: [
            "QtQuick/Flipable 2.0",
            "QtQuick/Flipable 2.1",
            "QtQuick/Flipable 2.11",
            "QtQuick/Flipable 2.4",
            "QtQuick/Flipable 2.7"
        ]
        exportMetaObjectRevisions: [0, 1, 11, 4, 7]
        Enum {
            name: "Side"
            values: ["Front", "Back"]
        }
        Property { name: "front"; type: "QQuickItem"; isPointer: true }
        Property { name: "back"; type: "QQuickItem"; isPointer: true }
        Property { name: "side"; type: "Side"; isReadonly: true }
        Method { name: "retransformBack" }
    }
    Component {
        file: "private/qquickpositioners_p.h"
        name: "QQuickFlow"
        prototype: "QQuickBasePositioner"
        exports: [
            "QtQuick/Flow 2.0",
            "QtQuick/Flow 2.1",
            "QtQuick/Flow 2.11",
            "QtQuick/Flow 2.4",
            "QtQuick/Flow 2.6",
            "QtQuick/Flow 2.7",
            "QtQuick/Flow 2.9"
        ]
        exportMetaObjectRevisions: [0, 1, 11, 4, 6, 7, 9]
        Enum {
            name: "Flow"
            values: ["LeftToRight", "TopToBottom"]
        }
        Property { name: "flow"; type: "Flow" }
        Property { name: "layoutDirection"; type: "Qt::LayoutDirection" }
        Property { name: "effectiveLayoutDirection"; type: "Qt::LayoutDirection"; isReadonly: true }
    }
    Component {
        file: "private/qquickfocusscope_p.h"
        name: "QQuickFocusScope"
        defaultProperty: "data"
        prototype: "QQuickItem"
        exports: [
            "QtQuick/FocusScope 2.0",
            "QtQuick/FocusScope 2.1",
            "QtQuick/FocusScope 2.11",
            "QtQuick/FocusScope 2.4",
            "QtQuick/FocusScope 2.7"
        ]
        exportMetaObjectRevisions: [0, 1, 11, 4, 7]
    }
    Component {
        file: "private/qquickfontloader_p.h"
        name: "QQuickFontLoader"
        prototype: "QObject"
        exports: ["QtQuick/FontLoader 2.0"]
        exportMetaObjectRevisions: [0]
        Enum {
            name: "Status"
            values: ["Null", "Ready", "Loading", "Error"]
        }
        Property { name: "source"; type: "QUrl" }
        Property { name: "name"; type: "string" }
        Property { name: "status"; type: "Status"; isReadonly: true }
        Method {
            name: "updateFontInfo"
            Parameter { type: "string" }
            Parameter { type: "QQuickFontLoader::Status" }
        }
    }
    Component {
        file: "private/qquickfontmetrics_p.h"
        name: "QQuickFontMetrics"
        prototype: "QObject"
        exports: ["QtQuick/FontMetrics 2.4"]
        exportMetaObjectRevisions: [4]
        Property { name: "font"; type: "QFont" }
        Property { name: "ascent"; type: "double"; isReadonly: true }
        Property { name: "descent"; type: "double"; isReadonly: true }
        Property { name: "height"; type: "double"; isReadonly: true }
        Property { name: "leading"; type: "double"; isReadonly: true }
        Property { name: "lineSpacing"; type: "double"; isReadonly: true }
        Property { name: "minimumLeftBearing"; type: "double"; isReadonly: true }
        Property { name: "minimumRightBearing"; type: "double"; isReadonly: true }
        Property { name: "maximumCharacterWidth"; type: "double"; isReadonly: true }
        Property { name: "xHeight"; type: "double"; isReadonly: true }
        Property { name: "averageCharacterWidth"; type: "double"; isReadonly: true }
        Property { name: "underlinePosition"; type: "double"; isReadonly: true }
        Property { name: "overlinePosition"; type: "double"; isReadonly: true }
        Property { name: "strikeOutPosition"; type: "double"; isReadonly: true }
        Property { name: "lineWidth"; type: "double"; isReadonly: true }
        Signal {
            name: "fontChanged"
            Parameter { name: "font"; type: "QFont" }
        }
        Method {
            name: "advanceWidth"
            type: "double"
            Parameter { name: "text"; type: "string" }
        }
        Method {
            name: "boundingRect"
            type: "QRectF"
            Parameter { name: "text"; type: "string" }
        }
        Method {
            name: "tightBoundingRect"
            type: "QRectF"
            Parameter { name: "text"; type: "string" }
        }
        Method {
            name: "elidedText"
            type: "string"
            Parameter { name: "text"; type: "string" }
            Parameter { name: "mode"; type: "Qt::TextElideMode" }
            Parameter { name: "width"; type: "double" }
            Parameter { name: "flags"; type: "int" }
        }
        Method {
            name: "elidedText"
            type: "string"
            Parameter { name: "text"; type: "string" }
            Parameter { name: "mode"; type: "Qt::TextElideMode" }
            Parameter { name: "width"; type: "double" }
        }
    }
    Component {
        file: "private/qquickvaluetypes_p.h"
        name: "QQuickFontValueType"
        exports: ["QtQuick/Font 2.0"]
        isCreatable: false
        exportMetaObjectRevisions: [0]
        Enum {
            name: "FontWeight"
            values: [
                "Thin",
                "ExtraLight",
                "Light",
                "Normal",
                "Medium",
                "DemiBold",
                "Bold",
                "ExtraBold",
                "Black"
            ]
        }
        Enum {
            name: "Capitalization"
            values: [
                "MixedCase",
                "AllUppercase",
                "AllLowercase",
                "SmallCaps",
                "Capitalize"
            ]
        }
        Enum {
            name: "HintingPreference"
            values: [
                "PreferDefaultHinting",
                "PreferNoHinting",
                "PreferVerticalHinting",
                "PreferFullHinting"
            ]
        }
        Property { name: "family"; type: "string" }
        Property { name: "styleName"; type: "string" }
        Property { name: "bold"; type: "bool" }
        Property { name: "weight"; type: "FontWeight" }
        Property { name: "italic"; type: "bool" }
        Property { name: "underline"; type: "bool" }
        Property { name: "overline"; type: "bool" }
        Property { name: "strikeout"; type: "bool" }
        Property { name: "pointSize"; type: "double" }
        Property { name: "pixelSize"; type: "int" }
        Property { name: "capitalization"; type: "Capitalization" }
        Property { name: "letterSpacing"; type: "double" }
        Property { name: "wordSpacing"; type: "double" }
        Property { name: "hintingPreference"; type: "HintingPreference" }
        Property { name: "kerning"; type: "bool" }
        Property { name: "preferShaping"; type: "bool" }
        Method { name: "toString"; type: "string" }
    }
    Component {
        file: "private/qquickmultipointtoucharea_p.h"
        name: "QQuickGrabGestureEvent"
        prototype: "QObject"
        exports: ["QtQuick/GestureEvent 2.0"]
        isCreatable: false
        exportMetaObjectRevisions: [0]
        Property { name: "touchPoints"; type: "QObject"; isList: true; isReadonly: true }
        Property { name: "dragThreshold"; type: "double"; isReadonly: true }
        Method { name: "grab" }
    }
    Component {
        file: "private/qquickrectangle_p.h"
        name: "QQuickGradient"
        defaultProperty: "stops"
        prototype: "QObject"
        exports: ["QtQuick/Gradient 2.0", "QtQuick/Gradient 2.12"]
        exportMetaObjectRevisions: [0, 12]
        Enum {
            name: "Orientation"
            values: ["Vertical", "Horizontal"]
        }
        Property { name: "stops"; type: "QQuickGradientStop"; isList: true; isReadonly: true }
        Property { name: "orientation"; revision: 12; type: "Orientation" }
        Signal { name: "updated" }
    }
    Component {
        file: "private/qquickrectangle_p.h"
        name: "QQuickGradientStop"
        prototype: "QObject"
        exports: ["QtQuick/GradientStop 2.0"]
        exportMetaObjectRevisions: [0]
        Property { name: "position"; type: "double" }
        Property { name: "color"; type: "QColor" }
    }
    Component {
        file: "private/qquickgraphicsinfo_p.h"
        name: "QQuickGraphicsInfo"
        prototype: "QObject"
        exports: ["QtQuick/GraphicsInfo 2.8"]
        isCreatable: false
        exportMetaObjectRevisions: [8]
        attachedType: "QQuickGraphicsInfo"
        Enum {
            name: "GraphicsApi"
            values: [
                "Unknown",
                "Software",
                "OpenGL",
                "Direct3D12",
                "OpenVG",
                "OpenGLRhi",
                "Direct3D11Rhi",
                "VulkanRhi",
                "MetalRhi",
                "NullRhi"
            ]
        }
        Enum {
            name: "ShaderType"
            values: ["UnknownShadingLanguage", "GLSL", "HLSL", "RhiShader"]
        }
        Enum {
            name: "ShaderCompilationType"
            values: ["RuntimeCompilation", "OfflineCompilation"]
        }
        Enum {
            name: "ShaderSourceType"
            values: [
                "ShaderSourceString",
                "ShaderSourceFile",
                "ShaderByteCode"
            ]
        }
        Enum {
            name: "OpenGLContextProfile"
            values: [
                "OpenGLNoProfile",
                "OpenGLCoreProfile",
                "OpenGLCompatibilityProfile"
            ]
        }
        Enum {
            name: "RenderableType"
            values: [
                "SurfaceFormatUnspecified",
                "SurfaceFormatOpenGL",
                "SurfaceFormatOpenGLES"
            ]
        }
        Property { name: "api"; type: "GraphicsApi"; isReadonly: true }
        Property { name: "shaderType"; type: "ShaderType"; isReadonly: true }
        Property { name: "shaderCompilationType"; type: "ShaderCompilationType"; isReadonly: true }
        Property { name: "shaderSourceType"; type: "ShaderSourceType"; isReadonly: true }
        Property { name: "majorVersion"; type: "int"; isReadonly: true }
        Property { name: "minorVersion"; type: "int"; isReadonly: true }
        Property { name: "profile"; type: "OpenGLContextProfile"; isReadonly: true }
        Property { name: "renderableType"; type: "RenderableType"; isReadonly: true }
        Method { name: "updateInfo" }
        Method {
            name: "setWindow"
            Parameter { name: "window"; type: "QQuickWindow"; isPointer: true }
        }
    }
    Component {
        file: "private/qquickpositioners_p.h"
        name: "QQuickGrid"
        prototype: "QQuickBasePositioner"
        exports: [
            "QtQuick/Grid 2.0",
            "QtQuick/Grid 2.1",
            "QtQuick/Grid 2.11",
            "QtQuick/Grid 2.4",
            "QtQuick/Grid 2.6",
            "QtQuick/Grid 2.7",
            "QtQuick/Grid 2.9"
        ]
        exportMetaObjectRevisions: [0, 1, 11, 4, 6, 7, 9]
        Enum {
            name: "Flow"
            values: ["LeftToRight", "TopToBottom"]
        }
        Enum {
            name: "HAlignment"
            values: ["AlignLeft", "AlignRight", "AlignHCenter"]
        }
        Enum {
            name: "VAlignment"
            values: ["AlignTop", "AlignBottom", "AlignVCenter"]
        }
        Property { name: "rows"; type: "int" }
        Property { name: "columns"; type: "int" }
        Property { name: "rowSpacing"; type: "double" }
        Property { name: "columnSpacing"; type: "double" }
        Property { name: "flow"; type: "Flow" }
        Property { name: "layoutDirection"; type: "Qt::LayoutDirection" }
        Property { name: "effectiveLayoutDirection"; type: "Qt::LayoutDirection"; isReadonly: true }
        Property { name: "horizontalItemAlignment"; revision: 1; type: "HAlignment" }
        Property {
            name: "effectiveHorizontalItemAlignment"
            revision: 1
            type: "HAlignment"
            isReadonly: true
        }
        Property { name: "verticalItemAlignment"; revision: 1; type: "VAlignment" }
        Signal {
            name: "horizontalAlignmentChanged"
            revision: 1
            Parameter { name: "alignment"; type: "HAlignment" }
        }
        Signal {
            name: "effectiveHorizontalAlignmentChanged"
            revision: 1
            Parameter { name: "alignment"; type: "HAlignment" }
        }
        Signal {
            name: "verticalAlignmentChanged"
            revision: 1
            Parameter { name: "alignment"; type: "VAlignment" }
        }
    }
    Component {
        file: "private/qquickshadereffectmesh_p.h"
        name: "QQuickGridMesh"
        prototype: "QQuickShaderEffectMesh"
        exports: ["QtQuick/GridMesh 2.0"]
        exportMetaObjectRevisions: [0]
        Property { name: "resolution"; type: "QSize" }
    }
    Component {
        file: "private/qquickgridview_p.h"
        name: "QQuickGridView"
        defaultProperty: "data"
        prototype: "QQuickItemView"
        exports: [
            "QtQuick/GridView 2.0",
            "QtQuick/GridView 2.1",
            "QtQuick/GridView 2.10",
            "QtQuick/GridView 2.11",
            "QtQuick/GridView 2.12",
            "QtQuick/GridView 2.13",
            "QtQuick/GridView 2.15",
            "QtQuick/GridView 2.3",
            "QtQuick/GridView 2.4",
            "QtQuick/GridView 2.7",
            "QtQuick/GridView 2.9"
        ]
        exportMetaObjectRevisions: [0, 1, 10, 11, 12, 13, 15, 3, 4, 7, 9]
        attachedType: "QQuickGridViewAttached"
        Enum {
            name: "Flow"
            values: ["FlowLeftToRight", "FlowTopToBottom"]
        }
        Enum {
            name: "SnapMode"
            values: ["NoSnap", "SnapToRow", "SnapOneRow"]
        }
        Property { name: "flow"; type: "Flow" }
        Property { name: "cellWidth"; type: "double" }
        Property { name: "cellHeight"; type: "double" }
        Property { name: "snapMode"; type: "SnapMode" }
        Signal { name: "highlightMoveDurationChanged" }
        Method { name: "moveCurrentIndexUp" }
        Method { name: "moveCurrentIndexDown" }
        Method { name: "moveCurrentIndexLeft" }
        Method { name: "moveCurrentIndexRight" }
    }
    Component { name: "QQuickGridViewAttached"; prototype: "QQuickItemViewAttached" }
    Component {
        file: "private/qquickhoverhandler_p.h"
        name: "QQuickHoverHandler"
        prototype: "QQuickSinglePointHandler"
        exports: ["QtQuick/HoverHandler 2.12", "QtQuick/HoverHandler 2.15"]
        exportMetaObjectRevisions: [12, 15]
        Property { name: "hovered"; type: "bool"; isReadonly: true }
    }
    Component {
        file: "private/qquickimage_p.h"
        name: "QQuickImage"
        prototype: "QQuickImageBase"
        exports: [
            "QtQuick/Image 2.0",
            "QtQuick/Image 2.1",
            "QtQuick/Image 2.11",
            "QtQuick/Image 2.14",
            "QtQuick/Image 2.15",
            "QtQuick/Image 2.3",
            "QtQuick/Image 2.4",
            "QtQuick/Image 2.5",
            "QtQuick/Image 2.7"
        ]
        exportMetaObjectRevisions: [0, 1, 11, 14, 15, 3, 4, 5, 7]
        Enum {
            name: "HAlignment"
            values: ["AlignLeft", "AlignRight", "AlignHCenter"]
        }
        Enum {
            name: "VAlignment"
            values: ["AlignTop", "AlignBottom", "AlignVCenter"]
        }
        Enum {
            name: "FillMode"
            values: [
                "Stretch",
                "PreserveAspectFit",
                "PreserveAspectCrop",
                "Tile",
                "TileVertically",
                "TileHorizontally",
                "Pad"
            ]
        }
        Property { name: "fillMode"; type: "FillMode" }
        Property { name: "paintedWidth"; type: "double"; isReadonly: true }
        Property { name: "paintedHeight"; type: "double"; isReadonly: true }
        Property { name: "horizontalAlignment"; type: "HAlignment" }
        Property { name: "verticalAlignment"; type: "VAlignment" }
        Property { name: "mipmap"; revision: 3; type: "bool" }
        Property { name: "autoTransform"; revision: 5; type: "bool" }
        Property { name: "sourceClipRect"; revision: 15; type: "QRectF" }
        Signal { name: "paintedGeometryChanged" }
        Signal {
            name: "horizontalAlignmentChanged"
            Parameter { name: "alignment"; type: "HAlignment" }
        }
        Signal {
            name: "verticalAlignmentChanged"
            Parameter { name: "alignment"; type: "VAlignment" }
        }
        Signal {
            name: "mipmapChanged"
            revision: 3
            Parameter { type: "bool" }
        }
        Signal { name: "autoTransformChanged"; revision: 5 }
        Method { name: "invalidateSceneGraph" }
    }
    Component {
        file: "private/qquickimagebase_p.h"
        name: "QQuickImageBase"
        prototype: "QQuickImplicitSizeItem"
        exports: ["QtQuick/ImageBase 2.14", "QtQuick/ImageBase 2.15"]
        isCreatable: false
        exportMetaObjectRevisions: [14, 15]
        Enum {
            name: "LoadPixmapOptions"
            alias: "LoadPixmapOption"
            isFlag: true
            values: ["NoOption", "HandleDPR", "UseProviderOptions"]
        }
        Enum {
            name: "Status"
            values: ["Null", "Ready", "Loading", "Error"]
        }
        Property { name: "status"; type: "Status"; isReadonly: true }
        Property { name: "source"; type: "QUrl" }
        Property { name: "progress"; type: "double"; isReadonly: true }
        Property { name: "asynchronous"; type: "bool" }
        Property { name: "cache"; type: "bool" }
        Property { name: "sourceSize"; type: "QSize" }
        Property { name: "mirror"; type: "bool" }
        Property { name: "currentFrame"; revision: 14; type: "int" }
        Property { name: "frameCount"; revision: 14; type: "int"; isReadonly: true }
        Property { name: "colorSpace"; revision: 15; type: "QColorSpace" }
        Signal {
            name: "sourceChanged"
            Parameter { type: "QUrl" }
        }
        Signal {
            name: "statusChanged"
            Parameter { type: "QQuickImageBase::Status" }
        }
        Signal {
            name: "progressChanged"
            Parameter { name: "progress"; type: "double" }
        }
        Signal { name: "currentFrameChanged"; revision: 14 }
        Signal { name: "frameCountChanged"; revision: 14 }
        Signal { name: "sourceClipRectChanged"; revision: 15 }
        Signal { name: "colorSpaceChanged"; revision: 15 }
        Method { name: "requestFinished" }
        Method {
            name: "requestProgress"
            Parameter { type: "qlonglong" }
            Parameter { type: "qlonglong" }
        }
    }
    Component {
        file: "qquickitem.h"
        name: "QQuickImplicitSizeItem"
        defaultProperty: "data"
        prototype: "QQuickItem"
        Property { name: "implicitWidth"; type: "double"; isReadonly: true }
        Property { name: "implicitHeight"; type: "double"; isReadonly: true }
    }
    Component {
        file: "private/qquickvalidator_p.h"
        name: "QQuickIntValidator"
        prototype: "QIntValidator"
        exports: ["QtQuick/IntValidator 2.0"]
        exportMetaObjectRevisions: [0]
        Property { name: "locale"; type: "string" }
        Signal { name: "localeNameChanged" }
    }
    Component {
        file: "qquickitem.h"
        name: "QQuickItem"
        defaultProperty: "data"
        prototype: "QObject"
        exports: [
            "QtQuick/Item 2.0",
            "QtQuick/Item 2.1",
            "QtQuick/Item 2.11",
            "QtQuick/Item 2.4",
            "QtQuick/Item 2.7"
        ]
        exportMetaObjectRevisions: [0, 1, 11, 4, 7]
        Enum {
            name: "Flags"
            alias: "Flag"
            isFlag: true
            values: [
                "ItemClipsChildrenToShape",
                "ItemAcceptsInputMethod",
                "ItemIsFocusScope",
                "ItemHasContents",
                "ItemAcceptsDrops"
            ]
        }
        Enum {
            name: "TransformOrigin"
            values: [
                "TopLeft",
                "Top",
                "TopRight",
                "Left",
                "Center",
                "Right",
                "BottomLeft",
                "Bottom",
                "BottomRight"
            ]
        }
        Property { name: "parent"; type: "QQuickItem"; isPointer: true }
        Property { name: "data"; type: "QObject"; isList: true; isReadonly: true }
        Property { name: "resources"; type: "QObject"; isList: true; isReadonly: true }
        Property { name: "children"; type: "QQuickItem"; isList: true; isReadonly: true }
        Property { name: "x"; type: "double" }
        Property { name: "y"; type: "double" }
        Property { name: "z"; type: "double" }
        Property { name: "width"; type: "double" }
        Property { name: "height"; type: "double" }
        Property { name: "opacity"; type: "double" }
        Property { name: "enabled"; type: "bool" }
        Property { name: "visible"; type: "bool" }
        Property { name: "visibleChildren"; type: "QQuickItem"; isList: true; isReadonly: true }
        Property { name: "states"; type: "QQuickState"; isList: true; isReadonly: true }
        Property { name: "transitions"; type: "QQuickTransition"; isList: true; isReadonly: true }
        Property { name: "state"; type: "string" }
        Property { name: "childrenRect"; type: "QRectF"; isReadonly: true }
        Property { name: "anchors"; type: "QQuickAnchors"; isReadonly: true; isPointer: true }
        Property { name: "left"; type: "QQuickAnchorLine"; isReadonly: true }
        Property { name: "right"; type: "QQuickAnchorLine"; isReadonly: true }
        Property { name: "horizontalCenter"; type: "QQuickAnchorLine"; isReadonly: true }
        Property { name: "top"; type: "QQuickAnchorLine"; isReadonly: true }
        Property { name: "bottom"; type: "QQuickAnchorLine"; isReadonly: true }
        Property { name: "verticalCenter"; type: "QQuickAnchorLine"; isReadonly: true }
        Property { name: "baseline"; type: "QQuickAnchorLine"; isReadonly: true }
        Property { name: "baselineOffset"; type: "double" }
        Property { name: "clip"; type: "bool" }
        Property { name: "focus"; type: "bool" }
        Property { name: "activeFocus"; type: "bool"; isReadonly: true }
        Property { name: "activeFocusOnTab"; revision: 1; type: "bool" }
        Property { name: "rotation"; type: "double" }
        Property { name: "scale"; type: "double" }
        Property { name: "transformOrigin"; type: "TransformOrigin" }
        Property { name: "transformOriginPoint"; type: "QPointF"; isReadonly: true }
        Property { name: "transform"; type: "QQuickTransform"; isList: true; isReadonly: true }
        Property { name: "smooth"; type: "bool" }
        Property { name: "antialiasing"; type: "bool" }
        Property { name: "implicitWidth"; type: "double" }
        Property { name: "implicitHeight"; type: "double" }
        Property { name: "containmentMask"; revision: 11; type: "QObject"; isPointer: true }
        Property { name: "layer"; type: "QQuickItemLayer"; isReadonly: true; isPointer: true }
        Signal {
            name: "childrenRectChanged"
            Parameter { type: "QRectF" }
        }
        Signal {
            name: "baselineOffsetChanged"
            Parameter { type: "double" }
        }
        Signal {
            name: "stateChanged"
            Parameter { type: "string" }
        }
        Signal {
            name: "focusChanged"
            Parameter { type: "bool" }
        }
        Signal {
            name: "activeFocusChanged"
            Parameter { type: "bool" }
        }
        Signal {
            name: "activeFocusOnTabChanged"
            revision: 1
            Parameter { type: "bool" }
        }
        Signal {
            name: "parentChanged"
            Parameter { type: "QQuickItem"; isPointer: true }
        }
        Signal {
            name: "transformOriginChanged"
            Parameter { type: "TransformOrigin" }
        }
        Signal {
            name: "smoothChanged"
            Parameter { type: "bool" }
        }
        Signal {
            name: "antialiasingChanged"
            Parameter { type: "bool" }
        }
        Signal {
            name: "clipChanged"
            Parameter { type: "bool" }
        }
        Signal {
            name: "windowChanged"
            revision: 1
            Parameter { name: "window"; type: "QQuickWindow"; isPointer: true }
        }
        Signal { name: "containmentMaskChanged"; revision: 11 }
        Method { name: "update" }
        Method {
            name: "_q_resourceObjectDeleted"
            Parameter { type: "QObject"; isPointer: true }
        }
        Method {
            name: "_q_createJSWrapper"
            type: "qulonglong"
            Parameter { type: "QV4::ExecutionEngine"; isPointer: true }
        }
        Method {
            name: "grabToImage"
            revision: 4
            type: "bool"
            Parameter { name: "callback"; type: "QJSValue" }
            Parameter { name: "targetSize"; type: "QSize" }
        }
        Method {
            name: "grabToImage"
            revision: 4
            type: "bool"
            Parameter { name: "callback"; type: "QJSValue" }
        }
        Method {
            name: "contains"
            type: "bool"
            Parameter { name: "point"; type: "QPointF" }
        }
        Method {
            name: "mapFromItem"
            Parameter { type: "QQmlV4Function"; isPointer: true }
        }
        Method {
            name: "mapToItem"
            Parameter { type: "QQmlV4Function"; isPointer: true }
        }
        Method {
            name: "mapFromGlobal"
            revision: 7
            Parameter { type: "QQmlV4Function"; isPointer: true }
        }
        Method {
            name: "mapToGlobal"
            revision: 7
            Parameter { type: "QQmlV4Function"; isPointer: true }
        }
        Method { name: "forceActiveFocus" }
        Method {
            name: "forceActiveFocus"
            Parameter { name: "reason"; type: "Qt::FocusReason" }
        }
        Method {
            name: "nextItemInFocusChain"
            revision: 1
            type: "QQuickItem*"
            Parameter { name: "forward"; type: "bool" }
        }
        Method { name: "nextItemInFocusChain"; revision: 1; type: "QQuickItem*" }
        Method {
            name: "childAt"
            type: "QQuickItem*"
            Parameter { name: "x"; type: "double" }
            Parameter { name: "y"; type: "double" }
        }
    }
    Component {
        file: "qquickitemgrabresult.h"
        name: "QQuickItemGrabResult"
        prototype: "QObject"
        Property { name: "image"; type: "QImage"; isReadonly: true }
        Property { name: "url"; type: "QUrl"; isReadonly: true }
        Signal { name: "ready" }
        Method { name: "setup" }
        Method { name: "render" }
        Method {
            name: "saveToFile"
            type: "bool"
            Parameter { name: "fileName"; type: "string" }
        }
        Method {
            name: "saveToFile"
            type: "bool"
            Parameter { name: "fileName"; type: "string" }
        }
    }
    Component {
        file: "private/qquickitem_p.h"
        name: "QQuickItemLayer"
        prototype: "QObject"
        Property { name: "enabled"; type: "bool" }
        Property { name: "textureSize"; type: "QSize" }
        Property { name: "sourceRect"; type: "QRectF" }
        Property { name: "mipmap"; type: "bool" }
        Property { name: "smooth"; type: "bool" }
        Property { name: "wrapMode"; type: "QQuickShaderEffectSource::WrapMode" }
        Property { name: "format"; type: "QQuickShaderEffectSource::Format" }
        Property { name: "samplerName"; type: "QByteArray" }
        Property { name: "effect"; type: "QQmlComponent"; isPointer: true }
        Property { name: "textureMirroring"; type: "QQuickShaderEffectSource::TextureMirroring" }
        Property { name: "samples"; type: "int" }
        Signal {
            name: "enabledChanged"
            Parameter { name: "enabled"; type: "bool" }
        }
        Signal {
            name: "sizeChanged"
            Parameter { name: "size"; type: "QSize" }
        }
        Signal {
            name: "mipmapChanged"
            Parameter { name: "mipmap"; type: "bool" }
        }
        Signal {
            name: "wrapModeChanged"
            Parameter { name: "mode"; type: "QQuickShaderEffectSource::WrapMode" }
        }
        Signal {
            name: "nameChanged"
            Parameter { name: "name"; type: "QByteArray" }
        }
        Signal {
            name: "effectChanged"
            Parameter { name: "component"; type: "QQmlComponent"; isPointer: true }
        }
        Signal {
            name: "smoothChanged"
            Parameter { name: "smooth"; type: "bool" }
        }
        Signal {
            name: "formatChanged"
            Parameter { name: "format"; type: "QQuickShaderEffectSource::Format" }
        }
        Signal {
            name: "sourceRectChanged"
            Parameter { name: "sourceRect"; type: "QRectF" }
        }
        Signal {
            name: "textureMirroringChanged"
            Parameter { name: "mirroring"; type: "QQuickShaderEffectSource::TextureMirroring" }
        }
        Signal {
            name: "samplesChanged"
            Parameter { name: "count"; type: "int" }
        }
    }
    Component {
        file: "private/qquickitemview_p.h"
        name: "QQuickItemView"
        defaultProperty: "flickableData"
        prototype: "QQuickFlickable"
        exports: [
            "QtQuick/ItemView 2.1",
            "QtQuick/ItemView 2.10",
            "QtQuick/ItemView 2.11",
            "QtQuick/ItemView 2.12",
            "QtQuick/ItemView 2.13",
            "QtQuick/ItemView 2.15",
            "QtQuick/ItemView 2.3",
            "QtQuick/ItemView 2.4",
            "QtQuick/ItemView 2.7",
            "QtQuick/ItemView 2.9"
        ]
        isCreatable: false
        exportMetaObjectRevisions: [1, 10, 11, 12, 13, 15, 3, 4, 7, 9]
        Enum {
            name: "LayoutDirection"
            values: [
                "LeftToRight",
                "RightToLeft",
                "VerticalTopToBottom",
                "VerticalBottomToTop"
            ]
        }
        Enum {
            name: "VerticalLayoutDirection"
            values: ["TopToBottom", "BottomToTop"]
        }
        Enum {
            name: "HighlightRangeMode"
            values: ["NoHighlightRange", "ApplyRange", "StrictlyEnforceRange"]
        }
        Enum {
            name: "PositionMode"
            values: [
                "Beginning",
                "Center",
                "End",
                "Visible",
                "Contain",
                "SnapPosition"
            ]
        }
        Property { name: "model"; type: "QVariant" }
        Property { name: "delegate"; type: "QQmlComponent"; isPointer: true }
        Property { name: "count"; type: "int"; isReadonly: true }
        Property { name: "currentIndex"; type: "int" }
        Property { name: "currentItem"; type: "QQuickItem"; isReadonly: true; isPointer: true }
        Property { name: "keyNavigationWraps"; type: "bool" }
        Property { name: "keyNavigationEnabled"; revision: 7; type: "bool" }
        Property { name: "cacheBuffer"; type: "int" }
        Property { name: "displayMarginBeginning"; revision: 3; type: "int" }
        Property { name: "displayMarginEnd"; revision: 3; type: "int" }
        Property { name: "layoutDirection"; type: "Qt::LayoutDirection" }
        Property { name: "effectiveLayoutDirection"; type: "Qt::LayoutDirection"; isReadonly: true }
        Property { name: "verticalLayoutDirection"; type: "VerticalLayoutDirection" }
        Property { name: "header"; type: "QQmlComponent"; isPointer: true }
        Property { name: "headerItem"; type: "QQuickItem"; isReadonly: true; isPointer: true }
        Property { name: "footer"; type: "QQmlComponent"; isPointer: true }
        Property { name: "footerItem"; type: "QQuickItem"; isReadonly: true; isPointer: true }
        Property { name: "populate"; type: "QQuickTransition"; isPointer: true }
        Property { name: "add"; type: "QQuickTransition"; isPointer: true }
        Property { name: "addDisplaced"; type: "QQuickTransition"; isPointer: true }
        Property { name: "move"; type: "QQuickTransition"; isPointer: true }
        Property { name: "moveDisplaced"; type: "QQuickTransition"; isPointer: true }
        Property { name: "remove"; type: "QQuickTransition"; isPointer: true }
        Property { name: "removeDisplaced"; type: "QQuickTransition"; isPointer: true }
        Property { name: "displaced"; type: "QQuickTransition"; isPointer: true }
        Property { name: "highlight"; type: "QQmlComponent"; isPointer: true }
        Property { name: "highlightItem"; type: "QQuickItem"; isReadonly: true; isPointer: true }
        Property { name: "highlightFollowsCurrentItem"; type: "bool" }
        Property { name: "highlightRangeMode"; type: "HighlightRangeMode" }
        Property { name: "preferredHighlightBegin"; type: "double" }
        Property { name: "preferredHighlightEnd"; type: "double" }
        Property { name: "highlightMoveDuration"; type: "int" }
        Property { name: "reuseItems"; revision: 15; type: "bool" }
        Signal { name: "keyNavigationEnabledChanged"; revision: 7 }
        Signal { name: "populateTransitionChanged" }
        Signal { name: "addTransitionChanged" }
        Signal { name: "addDisplacedTransitionChanged" }
        Signal { name: "moveTransitionChanged" }
        Signal { name: "moveDisplacedTransitionChanged" }
        Signal { name: "removeTransitionChanged" }
        Signal { name: "removeDisplacedTransitionChanged" }
        Signal { name: "displacedTransitionChanged" }
        Signal { name: "reuseItemsChanged"; revision: 15 }
        Method { name: "destroyRemoved" }
        Method {
            name: "createdItem"
            Parameter { name: "index"; type: "int" }
            Parameter { name: "item"; type: "QObject"; isPointer: true }
        }
        Method {
            name: "initItem"
            Parameter { name: "index"; type: "int" }
            Parameter { name: "item"; type: "QObject"; isPointer: true }
        }
        Method {
            name: "modelUpdated"
            Parameter { name: "changeSet"; type: "QQmlChangeSet" }
            Parameter { name: "reset"; type: "bool" }
        }
        Method {
            name: "destroyingItem"
            Parameter { name: "item"; type: "QObject"; isPointer: true }
        }
        Method {
            name: "onItemPooled"
            revision: 15
            Parameter { name: "modelIndex"; type: "int" }
            Parameter { name: "object"; type: "QObject"; isPointer: true }
        }
        Method {
            name: "onItemReused"
            revision: 15
            Parameter { name: "modelIndex"; type: "int" }
            Parameter { name: "object"; type: "QObject"; isPointer: true }
        }
        Method { name: "animStopped" }
        Method { name: "trackedPositionChanged" }
        Method {
            name: "positionViewAtIndex"
            Parameter { name: "index"; type: "int" }
            Parameter { name: "mode"; type: "int" }
        }
        Method {
            name: "indexAt"
            type: "int"
            Parameter { name: "x"; type: "double" }
            Parameter { name: "y"; type: "double" }
        }
        Method {
            name: "itemAt"
            type: "QQuickItem*"
            Parameter { name: "x"; type: "double" }
            Parameter { name: "y"; type: "double" }
        }
        Method {
            name: "itemAtIndex"
            revision: 13
            type: "QQuickItem*"
            Parameter { name: "index"; type: "int" }
        }
        Method { name: "positionViewAtBeginning" }
        Method { name: "positionViewAtEnd" }
        Method { name: "forceLayout"; revision: 1 }
    }
    Component {
        name: "QQuickItemViewAttached"
        prototype: "QObject"
        Property { name: "view"; type: "QQuickItemView"; isReadonly: true; isPointer: true }
        Property { name: "isCurrentItem"; type: "bool"; isReadonly: true }
        Property { name: "delayRemove"; type: "bool" }
        Property { name: "section"; type: "string"; isReadonly: true }
        Property { name: "previousSection"; type: "string"; isReadonly: true }
        Property { name: "nextSection"; type: "string"; isReadonly: true }
        Signal { name: "currentItemChanged" }
        Signal { name: "add" }
        Signal { name: "remove" }
        Signal { name: "prevSectionChanged" }
        Signal { name: "pooled" }
        Signal { name: "reused" }
    }
    Component {
        file: "private/qquickevents_p_p.h"
        name: "QQuickKeyEvent"
        prototype: "QObject"
        Property { name: "key"; type: "int"; isReadonly: true }
        Property { name: "text"; type: "string"; isReadonly: true }
        Property { name: "modifiers"; type: "int"; isReadonly: true }
        Property { name: "isAutoRepeat"; type: "bool"; isReadonly: true }
        Property { name: "count"; type: "int"; isReadonly: true }
        Property { name: "nativeScanCode"; type: "uint"; isReadonly: true }
        Property { name: "accepted"; type: "bool" }
        Method {
            name: "matches"
            revision: 2
            type: "bool"
            Parameter { name: "key"; type: "QKeySequence::StandardKey" }
        }
    }
    Component {
        file: "private/qquickitem_p.h"
        name: "QQuickKeyNavigationAttached"
        prototype: "QObject"
        exports: ["QtQuick/KeyNavigation 2.0"]
        isCreatable: false
        exportMetaObjectRevisions: [0]
        attachedType: "QQuickKeyNavigationAttached"
        Enum {
            name: "Priority"
            values: ["BeforeItem", "AfterItem"]
        }
        Property { name: "left"; type: "QQuickItem"; isPointer: true }
        Property { name: "right"; type: "QQuickItem"; isPointer: true }
        Property { name: "up"; type: "QQuickItem"; isPointer: true }
        Property { name: "down"; type: "QQuickItem"; isPointer: true }
        Property { name: "tab"; type: "QQuickItem"; isPointer: true }
        Property { name: "backtab"; type: "QQuickItem"; isPointer: true }
        Property { name: "priority"; type: "Priority" }
    }
    Component {
        file: "private/qquickitem_p.h"
        name: "QQuickKeysAttached"
        prototype: "QObject"
        exports: ["QtQuick/Keys 2.0"]
        isCreatable: false
        exportMetaObjectRevisions: [0]
        attachedType: "QQuickKeysAttached"
        Enum {
            name: "Priority"
            values: ["BeforeItem", "AfterItem"]
        }
        Property { name: "enabled"; type: "bool" }
        Property { name: "forwardTo"; type: "QQuickItem"; isList: true; isReadonly: true }
        Property { name: "priority"; type: "Priority" }
        Signal {
            name: "pressed"
            Parameter { name: "event"; type: "QQuickKeyEvent"; isPointer: true }
        }
        Signal {
            name: "released"
            Parameter { name: "event"; type: "QQuickKeyEvent"; isPointer: true }
        }
        Signal {
            name: "shortcutOverride"
            Parameter { name: "event"; type: "QQuickKeyEvent"; isPointer: true }
        }
        Signal {
            name: "digit0Pressed"
            Parameter { name: "event"; type: "QQuickKeyEvent"; isPointer: true }
        }
        Signal {
            name: "digit1Pressed"
            Parameter { name: "event"; type: "QQuickKeyEvent"; isPointer: true }
        }
        Signal {
            name: "digit2Pressed"
            Parameter { name: "event"; type: "QQuickKeyEvent"; isPointer: true }
        }
        Signal {
            name: "digit3Pressed"
            Parameter { name: "event"; type: "QQuickKeyEvent"; isPointer: true }
        }
        Signal {
            name: "digit4Pressed"
            Parameter { name: "event"; type: "QQuickKeyEvent"; isPointer: true }
        }
        Signal {
            name: "digit5Pressed"
            Parameter { name: "event"; type: "QQuickKeyEvent"; isPointer: true }
        }
        Signal {
            name: "digit6Pressed"
            Parameter { name: "event"; type: "QQuickKeyEvent"; isPointer: true }
        }
        Signal {
            name: "digit7Pressed"
            Parameter { name: "event"; type: "QQuickKeyEvent"; isPointer: true }
        }
        Signal {
            name: "digit8Pressed"
            Parameter { name: "event"; type: "QQuickKeyEvent"; isPointer: true }
        }
        Signal {
            name: "digit9Pressed"
            Parameter { name: "event"; type: "QQuickKeyEvent"; isPointer: true }
        }
        Signal {
            name: "leftPressed"
            Parameter { name: "event"; type: "QQuickKeyEvent"; isPointer: true }
        }
        Signal {
            name: "rightPressed"
            Parameter { name: "event"; type: "QQuickKeyEvent"; isPointer: true }
        }
        Signal {
            name: "upPressed"
            Parameter { name: "event"; type: "QQuickKeyEvent"; isPointer: true }
        }
        Signal {
            name: "downPressed"
            Parameter { name: "event"; type: "QQuickKeyEvent"; isPointer: true }
        }
        Signal {
            name: "tabPressed"
            Parameter { name: "event"; type: "QQuickKeyEvent"; isPointer: true }
        }
        Signal {
            name: "backtabPressed"
            Parameter { name: "event"; type: "QQuickKeyEvent"; isPointer: true }
        }
        Signal {
            name: "asteriskPressed"
            Parameter { name: "event"; type: "QQuickKeyEvent"; isPointer: true }
        }
        Signal {
            name: "numberSignPressed"
            Parameter { name: "event"; type: "QQuickKeyEvent"; isPointer: true }
        }
        Signal {
            name: "escapePressed"
            Parameter { name: "event"; type: "QQuickKeyEvent"; isPointer: true }
        }
        Signal {
            name: "returnPressed"
            Parameter { name: "event"; type: "QQuickKeyEvent"; isPointer: true }
        }
        Signal {
            name: "enterPressed"
            Parameter { name: "event"; type: "QQuickKeyEvent"; isPointer: true }
        }
        Signal {
            name: "deletePressed"
            Parameter { name: "event"; type: "QQuickKeyEvent"; isPointer: true }
        }
        Signal {
            name: "spacePressed"
            Parameter { name: "event"; type: "QQuickKeyEvent"; isPointer: true }
        }
        Signal {
            name: "backPressed"
            Parameter { name: "event"; type: "QQuickKeyEvent"; isPointer: true }
        }
        Signal {
            name: "cancelPressed"
            Parameter { name: "event"; type: "QQuickKeyEvent"; isPointer: true }
        }
        Signal {
            name: "selectPressed"
            Parameter { name: "event"; type: "QQuickKeyEvent"; isPointer: true }
        }
        Signal {
            name: "yesPressed"
            Parameter { name: "event"; type: "QQuickKeyEvent"; isPointer: true }
        }
        Signal {
            name: "noPressed"
            Parameter { name: "event"; type: "QQuickKeyEvent"; isPointer: true }
        }
        Signal {
            name: "context1Pressed"
            Parameter { name: "event"; type: "QQuickKeyEvent"; isPointer: true }
        }
        Signal {
            name: "context2Pressed"
            Parameter { name: "event"; type: "QQuickKeyEvent"; isPointer: true }
        }
        Signal {
            name: "context3Pressed"
            Parameter { name: "event"; type: "QQuickKeyEvent"; isPointer: true }
        }
        Signal {
            name: "context4Pressed"
            Parameter { name: "event"; type: "QQuickKeyEvent"; isPointer: true }
        }
        Signal {
            name: "callPressed"
            Parameter { name: "event"; type: "QQuickKeyEvent"; isPointer: true }
        }
        Signal {
            name: "hangupPressed"
            Parameter { name: "event"; type: "QQuickKeyEvent"; isPointer: true }
        }
        Signal {
            name: "flipPressed"
            Parameter { name: "event"; type: "QQuickKeyEvent"; isPointer: true }
        }
        Signal {
            name: "menuPressed"
            Parameter { name: "event"; type: "QQuickKeyEvent"; isPointer: true }
        }
        Signal {
            name: "volumeUpPressed"
            Parameter { name: "event"; type: "QQuickKeyEvent"; isPointer: true }
        }
        Signal {
            name: "volumeDownPressed"
            Parameter { name: "event"; type: "QQuickKeyEvent"; isPointer: true }
        }
    }
    Component {
        file: "private/qquickitem_p.h"
        name: "QQuickLayoutMirroringAttached"
        prototype: "QObject"
        exports: ["QtQuick/LayoutMirroring 2.0"]
        isCreatable: false
        exportMetaObjectRevisions: [0]
        attachedType: "QQuickLayoutMirroringAttached"
        Property { name: "enabled"; type: "bool" }
        Property { name: "childrenInherit"; type: "bool" }
    }
    Component {
        file: "private/qquicklistview_p.h"
        name: "QQuickListView"
        defaultProperty: "data"
        prototype: "QQuickItemView"
        exports: [
            "QtQuick/ListView 2.0",
            "QtQuick/ListView 2.1",
            "QtQuick/ListView 2.10",
            "QtQuick/ListView 2.11",
            "QtQuick/ListView 2.12",
            "QtQuick/ListView 2.13",
            "QtQuick/ListView 2.15",
            "QtQuick/ListView 2.3",
            "QtQuick/ListView 2.4",
            "QtQuick/ListView 2.7",
            "QtQuick/ListView 2.9"
        ]
        exportMetaObjectRevisions: [0, 1, 10, 11, 12, 13, 15, 3, 4, 7, 9]
        attachedType: "QQuickListViewAttached"
        Enum {
            name: "Orientation"
            values: ["Horizontal", "Vertical"]
        }
        Enum {
            name: "SnapMode"
            values: ["NoSnap", "SnapToItem", "SnapOneItem"]
        }
        Enum {
            name: "HeaderPositioning"
            values: ["InlineHeader", "OverlayHeader", "PullBackHeader"]
        }
        Enum {
            name: "FooterPositioning"
            values: ["InlineFooter", "OverlayFooter", "PullBackFooter"]
        }
        Property { name: "highlightMoveVelocity"; type: "double" }
        Property { name: "highlightResizeVelocity"; type: "double" }
        Property { name: "highlightResizeDuration"; type: "int" }
        Property { name: "spacing"; type: "double" }
        Property { name: "orientation"; type: "Orientation" }
        Property { name: "section"; type: "QQuickViewSection"; isReadonly: true; isPointer: true }
        Property { name: "currentSection"; type: "string"; isReadonly: true }
        Property { name: "snapMode"; type: "SnapMode" }
        Property { name: "headerPositioning"; revision: 4; type: "HeaderPositioning" }
        Property { name: "footerPositioning"; revision: 4; type: "FooterPositioning" }
        Signal { name: "headerPositioningChanged"; revision: 4 }
        Signal { name: "footerPositioningChanged"; revision: 4 }
        Method { name: "incrementCurrentIndex" }
        Method { name: "decrementCurrentIndex" }
    }
    Component { name: "QQuickListViewAttached"; prototype: "QQuickItemViewAttached" }
    Component {
        file: "private/qquickloader_p.h"
        name: "QQuickLoader"
        prototype: "QQuickImplicitSizeItem"
        exports: [
            "QtQuick/Loader 2.0",
            "QtQuick/Loader 2.1",
            "QtQuick/Loader 2.11",
            "QtQuick/Loader 2.4",
            "QtQuick/Loader 2.7"
        ]
        exportMetaObjectRevisions: [0, 1, 11, 4, 7]
        Enum {
            name: "Status"
            values: ["Null", "Ready", "Loading", "Error"]
        }
        Property { name: "active"; type: "bool" }
        Property { name: "source"; type: "QUrl" }
        Property { name: "sourceComponent"; type: "QQmlComponent"; isPointer: true }
        Property { name: "item"; type: "QObject"; isReadonly: true; isPointer: true }
        Property { name: "status"; type: "Status"; isReadonly: true }
        Property { name: "progress"; type: "double"; isReadonly: true }
        Property { name: "asynchronous"; type: "bool" }
        Signal { name: "loaded" }
        Method { name: "_q_sourceLoaded" }
        Method { name: "_q_updateSize" }
        Method {
            name: "setSource"
            Parameter { type: "QQmlV4Function"; isPointer: true }
        }
    }
    Component {
        file: "private/qquicktranslate_p.h"
        name: "QQuickMatrix4x4"
        prototype: "QQuickTransform"
        exports: ["QtQuick/Matrix4x4 2.3"]
        exportMetaObjectRevisions: [3]
        Property { name: "matrix"; type: "QMatrix4x4" }
    }
    Component {
        file: "private/qquickmousearea_p.h"
        name: "QQuickMouseArea"
        defaultProperty: "data"
        prototype: "QQuickItem"
        exports: [
            "QtQuick/MouseArea 2.0",
            "QtQuick/MouseArea 2.1",
            "QtQuick/MouseArea 2.11",
            "QtQuick/MouseArea 2.4",
            "QtQuick/MouseArea 2.5",
            "QtQuick/MouseArea 2.7",
            "QtQuick/MouseArea 2.9"
        ]
        exportMetaObjectRevisions: [0, 1, 11, 4, 5, 7, 9]
        Property { name: "mouseX"; type: "double"; isReadonly: true }
        Property { name: "mouseY"; type: "double"; isReadonly: true }
        Property { name: "containsMouse"; type: "bool"; isReadonly: true }
        Property { name: "pressed"; type: "bool"; isReadonly: true }
        Property { name: "enabled"; type: "bool" }
        Property { name: "scrollGestureEnabled"; revision: 5; type: "bool" }
        Property { name: "pressedButtons"; type: "Qt::MouseButtons"; isReadonly: true }
        Property { name: "acceptedButtons"; type: "Qt::MouseButtons" }
        Property { name: "hoverEnabled"; type: "bool" }
        Property { name: "drag"; type: "QQuickDrag"; isReadonly: true; isPointer: true }
        Property { name: "preventStealing"; type: "bool" }
        Property { name: "propagateComposedEvents"; type: "bool" }
        Property { name: "cursorShape"; type: "Qt::CursorShape" }
        Property { name: "containsPress"; revision: 4; type: "bool"; isReadonly: true }
        Property { name: "pressAndHoldInterval"; revision: 9; type: "int" }
        Signal { name: "hoveredChanged" }
        Signal { name: "scrollGestureEnabledChanged"; revision: 5 }
        Signal {
            name: "positionChanged"
            Parameter { name: "mouse"; type: "QQuickMouseEvent"; isPointer: true }
        }
        Signal {
            name: "mouseXChanged"
            Parameter { name: "mouse"; type: "QQuickMouseEvent"; isPointer: true }
        }
        Signal {
            name: "mouseYChanged"
            Parameter { name: "mouse"; type: "QQuickMouseEvent"; isPointer: true }
        }
        Signal {
            name: "pressed"
            Parameter { name: "mouse"; type: "QQuickMouseEvent"; isPointer: true }
        }
        Signal {
            name: "pressAndHold"
            Parameter { name: "mouse"; type: "QQuickMouseEvent"; isPointer: true }
        }
        Signal {
            name: "released"
            Parameter { name: "mouse"; type: "QQuickMouseEvent"; isPointer: true }
        }
        Signal {
            name: "clicked"
            Parameter { name: "mouse"; type: "QQuickMouseEvent"; isPointer: true }
        }
        Signal {
            name: "doubleClicked"
            Parameter { name: "mouse"; type: "QQuickMouseEvent"; isPointer: true }
        }
        Signal {
            name: "wheel"
            Parameter { name: "wheel"; type: "QQuickWheelEvent"; isPointer: true }
        }
        Signal { name: "entered" }
        Signal { name: "exited" }
        Signal { name: "canceled" }
        Signal { name: "containsPressChanged"; revision: 4 }
        Signal { name: "pressAndHoldIntervalChanged"; revision: 9 }
    }
    Component {
        file: "private/qquickevents_p_p.h"
        name: "QQuickMouseEvent"
        prototype: "QObject"
        Property { name: "x"; type: "double"; isReadonly: true }
        Property { name: "y"; type: "double"; isReadonly: true }
        Property { name: "button"; type: "int"; isReadonly: true }
        Property { name: "buttons"; type: "int"; isReadonly: true }
        Property { name: "modifiers"; type: "int"; isReadonly: true }
        Property { name: "source"; revision: 7; type: "int"; isReadonly: true }
        Property { name: "wasHeld"; type: "bool"; isReadonly: true }
        Property { name: "isClick"; type: "bool"; isReadonly: true }
        Property { name: "accepted"; type: "bool" }
        Property { name: "flags"; revision: 11; type: "int"; isReadonly: true }
    }
    Component {
        file: "private/qquickpointerhandler_p.h"
        name: "QQuickMultiPointHandler"
        prototype: "QQuickPointerDeviceHandler"
        Property { name: "minimumPointCount"; type: "int" }
        Property { name: "maximumPointCount"; type: "int" }
        Property { name: "centroid"; type: "QQuickHandlerPoint"; isReadonly: true }
    }
    Component {
        file: "private/qquickmultipointtoucharea_p.h"
        name: "QQuickMultiPointTouchArea"
        defaultProperty: "data"
        prototype: "QQuickItem"
        exports: [
            "QtQuick/MultiPointTouchArea 2.0",
            "QtQuick/MultiPointTouchArea 2.1",
            "QtQuick/MultiPointTouchArea 2.11",
            "QtQuick/MultiPointTouchArea 2.4",
            "QtQuick/MultiPointTouchArea 2.7"
        ]
        exportMetaObjectRevisions: [0, 1, 11, 4, 7]
        Property { name: "touchPoints"; type: "QQuickTouchPoint"; isList: true; isReadonly: true }
        Property { name: "minimumTouchPoints"; type: "int" }
        Property { name: "maximumTouchPoints"; type: "int" }
        Property { name: "mouseEnabled"; type: "bool" }
        Signal {
            name: "pressed"
            Parameter { name: "touchPoints"; type: "QList<QObject*>" }
        }
        Signal {
            name: "updated"
            Parameter { name: "touchPoints"; type: "QList<QObject*>" }
        }
        Signal {
            name: "released"
            Parameter { name: "touchPoints"; type: "QList<QObject*>" }
        }
        Signal {
            name: "canceled"
            Parameter { name: "touchPoints"; type: "QList<QObject*>" }
        }
        Signal {
            name: "gestureStarted"
            Parameter { name: "gesture"; type: "QQuickGrabGestureEvent"; isPointer: true }
        }
        Signal {
            name: "touchUpdated"
            Parameter { name: "touchPoints"; type: "QList<QObject*>" }
        }
    }
    Component {
        file: "private/qquickanimation_p.h"
        name: "QQuickNumberAnimation"
        prototype: "QQuickPropertyAnimation"
        exports: [
            "QtQuick/NumberAnimation 2.0",
            "QtQuick/NumberAnimation 2.12"
        ]
        exportMetaObjectRevisions: [0, 12]
        Property { name: "from"; type: "double" }
        Property { name: "to"; type: "double" }
    }
    Component {
        file: "private/qquickanimator_p.h"
        name: "QQuickOpacityAnimator"
        prototype: "QQuickAnimator"
        exports: [
            "QtQuick/OpacityAnimator 2.12",
            "QtQuick/OpacityAnimator 2.2"
        ]
        exportMetaObjectRevisions: [12, 2]
    }
    Component {
        file: "private/qquickopenglinfo_p.h"
        name: "QQuickOpenGLInfo"
        prototype: "QObject"
        exports: ["QtQuick/OpenGLInfo 2.4"]
        isCreatable: false
        exportMetaObjectRevisions: [4]
        attachedType: "QQuickOpenGLInfo"
        Enum {
            name: "ContextProfile"
            values: ["NoProfile", "CoreProfile", "CompatibilityProfile"]
        }
        Enum {
            name: "RenderableType"
            values: ["Unspecified", "OpenGL", "OpenGLES"]
        }
        Property { name: "majorVersion"; type: "int"; isReadonly: true }
        Property { name: "minorVersion"; type: "int"; isReadonly: true }
        Property { name: "profile"; type: "ContextProfile"; isReadonly: true }
        Property { name: "renderableType"; type: "RenderableType"; isReadonly: true }
        Method { name: "updateFormat" }
        Method {
            name: "setWindow"
            Parameter { name: "window"; type: "QQuickWindow"; isPointer: true }
        }
    }
    Component {
        file: "qquickpainteditem.h"
        name: "QQuickPaintedItem"
        defaultProperty: "data"
        prototype: "QQuickItem"
        exports: [
            "QtQuick/PaintedItem 2.0",
            "QtQuick/PaintedItem 2.1",
            "QtQuick/PaintedItem 2.11",
            "QtQuick/PaintedItem 2.4",
            "QtQuick/PaintedItem 2.7"
        ]
        isCreatable: false
        exportMetaObjectRevisions: [0, 1, 11, 4, 7]
        Enum {
            name: "RenderTarget"
            values: [
                "Image",
                "FramebufferObject",
                "InvertedYFramebufferObject"
            ]
        }
        Enum {
            name: "PerformanceHints"
            alias: "PerformanceHint"
            isFlag: true
            values: ["FastFBOResizing"]
        }
        Property { name: "contentsSize"; type: "QSize" }
        Property { name: "fillColor"; type: "QColor" }
        Property { name: "contentsScale"; type: "double" }
        Property { name: "renderTarget"; type: "RenderTarget" }
        Property { name: "textureSize"; type: "QSize" }
        Method { name: "invalidateSceneGraph" }
    }
    Component {
        file: "private/qquickanimation_p.h"
        name: "QQuickParallelAnimation"
        defaultProperty: "animations"
        prototype: "QQuickAnimationGroup"
        exports: [
            "QtQuick/ParallelAnimation 2.0",
            "QtQuick/ParallelAnimation 2.12"
        ]
        exportMetaObjectRevisions: [0, 12]
    }
    Component {
        file: "private/qquickitemanimation_p.h"
        name: "QQuickParentAnimation"
        defaultProperty: "animations"
        prototype: "QQuickAnimationGroup"
        exports: [
            "QtQuick/ParentAnimation 2.0",
            "QtQuick/ParentAnimation 2.12"
        ]
        exportMetaObjectRevisions: [0, 12]
        Property { name: "target"; type: "QQuickItem"; isPointer: true }
        Property { name: "newParent"; type: "QQuickItem"; isPointer: true }
        Property { name: "via"; type: "QQuickItem"; isPointer: true }
    }
    Component {
        file: "private/qquickstateoperations_p.h"
        name: "QQuickParentChange"
        prototype: "QQuickStateOperation"
        exports: ["QtQuick/ParentChange 2.0"]
        exportMetaObjectRevisions: [0]
        Property { name: "target"; type: "QQuickItem"; isPointer: true }
        Property { name: "parent"; type: "QQuickItem"; isPointer: true }
        Property { name: "x"; type: "QQmlScriptString" }
        Property { name: "y"; type: "QQmlScriptString" }
        Property { name: "width"; type: "QQmlScriptString" }
        Property { name: "height"; type: "QQmlScriptString" }
        Property { name: "scale"; type: "QQmlScriptString" }
        Property { name: "rotation"; type: "QQmlScriptString" }
    }
    Component {
        file: "private/qquickpath_p.h"
        name: "QQuickPath"
        defaultProperty: "pathElements"
        prototype: "QObject"
        exports: ["QtQuick/Path 2.0", "QtQuick/Path 2.14"]
        exportMetaObjectRevisions: [0, 14]
        Property { name: "pathElements"; type: "QQuickPathElement"; isList: true; isReadonly: true }
        Property { name: "startX"; type: "double" }
        Property { name: "startY"; type: "double" }
        Property { name: "closed"; type: "bool"; isReadonly: true }
        Property { name: "scale"; revision: 14; type: "QSizeF" }
        Signal { name: "changed" }
        Signal { name: "scaleChanged"; revision: 14 }
        Method { name: "processPath" }
        Method {
            name: "pointAtPercent"
            revision: 14
            type: "QPointF"
            Parameter { name: "t"; type: "double" }
        }
    }
    Component {
        file: "private/qquickpath_p.h"
        name: "QQuickPathAngleArc"
        prototype: "QQuickCurve"
        exports: ["QtQuick/PathAngleArc 2.11"]
        exportMetaObjectRevisions: [11]
        Property { name: "centerX"; type: "double" }
        Property { name: "centerY"; type: "double" }
        Property { name: "radiusX"; type: "double" }
        Property { name: "radiusY"; type: "double" }
        Property { name: "startAngle"; type: "double" }
        Property { name: "sweepAngle"; type: "double" }
        Property { name: "moveToStart"; type: "bool" }
    }
    Component {
        file: "private/qquickitemanimation_p.h"
        name: "QQuickPathAnimation"
        prototype: "QQuickAbstractAnimation"
        exports: ["QtQuick/PathAnimation 2.0", "QtQuick/PathAnimation 2.12"]
        exportMetaObjectRevisions: [0, 12]
        Enum {
            name: "Orientation"
            values: [
                "Fixed",
                "RightFirst",
                "LeftFirst",
                "BottomFirst",
                "TopFirst"
            ]
        }
        Property { name: "duration"; type: "int" }
        Property { name: "easing"; type: "QEasingCurve" }
        Property { name: "path"; type: "QQuickPath"; isPointer: true }
        Property { name: "target"; type: "QQuickItem"; isPointer: true }
        Property { name: "orientation"; type: "Orientation" }
        Property { name: "anchorPoint"; type: "QPointF" }
        Property { name: "orientationEntryDuration"; type: "int" }
        Property { name: "orientationExitDuration"; type: "int" }
        Property { name: "endRotation"; type: "double" }
        Signal {
            name: "durationChanged"
            Parameter { type: "int" }
        }
        Signal {
            name: "easingChanged"
            Parameter { type: "QEasingCurve" }
        }
        Signal {
            name: "orientationChanged"
            Parameter { type: "Orientation" }
        }
        Signal {
            name: "anchorPointChanged"
            Parameter { type: "QPointF" }
        }
        Signal {
            name: "orientationEntryDurationChanged"
            Parameter { type: "double" }
        }
        Signal {
            name: "orientationExitDurationChanged"
            Parameter { type: "double" }
        }
        Signal {
            name: "endRotationChanged"
            Parameter { type: "double" }
        }
    }
    Component {
        file: "private/qquickpath_p.h"
        name: "QQuickPathArc"
        prototype: "QQuickCurve"
        exports: ["QtQuick/PathArc 2.0", "QtQuick/PathArc 2.9"]
        exportMetaObjectRevisions: [0, 9]
        Enum {
            name: "ArcDirection"
            values: ["Clockwise", "Counterclockwise"]
        }
        Property { name: "radiusX"; type: "double" }
        Property { name: "radiusY"; type: "double" }
        Property { name: "useLargeArc"; type: "bool" }
        Property { name: "direction"; type: "ArcDirection" }
        Property { name: "xAxisRotation"; revision: 9; type: "double" }
        Signal { name: "xAxisRotationChanged"; revision: 9 }
    }
    Component {
        file: "private/qquickpath_p.h"
        name: "QQuickPathAttribute"
        prototype: "QQuickPathElement"
        exports: ["QtQuick/PathAttribute 2.0"]
        exportMetaObjectRevisions: [0]
        Property { name: "name"; type: "string" }
        Property { name: "value"; type: "double" }
    }
    Component {
        file: "private/qquickpath_p.h"
        name: "QQuickPathCatmullRomCurve"
        prototype: "QQuickCurve"
        exports: ["QtQuick/PathCurve 2.0"]
        exportMetaObjectRevisions: [0]
    }
    Component {
        file: "private/qquickpath_p.h"
        name: "QQuickPathCubic"
        prototype: "QQuickCurve"
        exports: ["QtQuick/PathCubic 2.0"]
        exportMetaObjectRevisions: [0]
        Property { name: "control1X"; type: "double" }
        Property { name: "control1Y"; type: "double" }
        Property { name: "control2X"; type: "double" }
        Property { name: "control2Y"; type: "double" }
        Property { name: "relativeControl1X"; type: "double" }
        Property { name: "relativeControl1Y"; type: "double" }
        Property { name: "relativeControl2X"; type: "double" }
        Property { name: "relativeControl2Y"; type: "double" }
    }
    Component {
        file: "private/qquickpath_p.h"
        name: "QQuickPathElement"
        prototype: "QObject"
        Signal { name: "changed" }
    }
    Component {
        file: "private/qquickpathinterpolator_p.h"
        name: "QQuickPathInterpolator"
        prototype: "QObject"
        exports: ["QtQuick/PathInterpolator 2.0"]
        exportMetaObjectRevisions: [0]
        Property { name: "path"; type: "QQuickPath"; isPointer: true }
        Property { name: "progress"; type: "double" }
        Property { name: "x"; type: "double"; isReadonly: true }
        Property { name: "y"; type: "double"; isReadonly: true }
        Property { name: "angle"; type: "double"; isReadonly: true }
        Method { name: "_q_pathUpdated" }
    }
    Component {
        file: "private/qquickpath_p.h"
        name: "QQuickPathLine"
        prototype: "QQuickCurve"
        exports: ["QtQuick/PathLine 2.0"]
        exportMetaObjectRevisions: [0]
    }
    Component {
        file: "private/qquickpath_p.h"
        name: "QQuickPathMove"
        prototype: "QQuickCurve"
        exports: ["QtQuick/PathMove 2.9"]
        exportMetaObjectRevisions: [9]
    }
    Component {
        file: "private/qquickpath_p.h"
        name: "QQuickPathMultiline"
        prototype: "QQuickCurve"
        exports: ["QtQuick/PathMultiline 2.14"]
        exportMetaObjectRevisions: [14]
        Property { name: "start"; type: "QPointF"; isReadonly: true }
        Property { name: "paths"; type: "QVariant" }
    }
    Component {
        file: "private/qquickpath_p.h"
        name: "QQuickPathPercent"
        prototype: "QQuickPathElement"
        exports: ["QtQuick/PathPercent 2.0"]
        exportMetaObjectRevisions: [0]
        Property { name: "value"; type: "double" }
    }
    Component {
        file: "private/qquickpath_p.h"
        name: "QQuickPathPolyline"
        prototype: "QQuickCurve"
        exports: ["QtQuick/PathPolyline 2.14"]
        exportMetaObjectRevisions: [14]
        Property { name: "start"; type: "QPointF"; isReadonly: true }
        Property { name: "path"; type: "QVariant" }
    }
    Component {
        file: "private/qquickpath_p.h"
        name: "QQuickPathQuad"
        prototype: "QQuickCurve"
        exports: ["QtQuick/PathQuad 2.0"]
        exportMetaObjectRevisions: [0]
        Property { name: "controlX"; type: "double" }
        Property { name: "controlY"; type: "double" }
        Property { name: "relativeControlX"; type: "double" }
        Property { name: "relativeControlY"; type: "double" }
    }
    Component {
        file: "private/qquickpath_p.h"
        name: "QQuickPathSvg"
        prototype: "QQuickCurve"
        exports: ["QtQuick/PathSvg 2.0"]
        exportMetaObjectRevisions: [0]
        Property { name: "path"; type: "string" }
    }
    Component {
        file: "private/qquickpath_p.h"
        name: "QQuickPathText"
        prototype: "QQuickPathElement"
        exports: ["QtQuick/PathText 2.15"]
        exportMetaObjectRevisions: [15]
        Property { name: "x"; type: "double" }
        Property { name: "y"; type: "double" }
        Property { name: "width"; type: "double"; isReadonly: true }
        Property { name: "height"; type: "double"; isReadonly: true }
        Property { name: "text"; type: "string" }
        Property { name: "font"; type: "QFont" }
        Method { name: "invalidate" }
    }
    Component {
        file: "private/qquickpathview_p.h"
        name: "QQuickPathView"
        defaultProperty: "data"
        prototype: "QQuickItem"
        exports: [
            "QtQuick/PathView 2.0",
            "QtQuick/PathView 2.1",
            "QtQuick/PathView 2.11",
            "QtQuick/PathView 2.13",
            "QtQuick/PathView 2.4",
            "QtQuick/PathView 2.7"
        ]
        exportMetaObjectRevisions: [0, 1, 11, 13, 4, 7]
        attachedType: "QQuickPathViewAttached"
        Enum {
            name: "HighlightRangeMode"
            values: ["NoHighlightRange", "ApplyRange", "StrictlyEnforceRange"]
        }
        Enum {
            name: "SnapMode"
            values: ["NoSnap", "SnapToItem", "SnapOneItem"]
        }
        Enum {
            name: "MovementDirection"
            values: ["Shortest", "Negative", "Positive"]
        }
        Enum {
            name: "PositionMode"
            values: ["Beginning", "Center", "End", "Contain", "SnapPosition"]
        }
        Property { name: "model"; type: "QVariant" }
        Property { name: "path"; type: "QQuickPath"; isPointer: true }
        Property { name: "currentIndex"; type: "int" }
        Property { name: "currentItem"; type: "QQuickItem"; isReadonly: true; isPointer: true }
        Property { name: "offset"; type: "double" }
        Property { name: "highlight"; type: "QQmlComponent"; isPointer: true }
        Property { name: "highlightItem"; type: "QQuickItem"; isReadonly: true; isPointer: true }
        Property { name: "preferredHighlightBegin"; type: "double" }
        Property { name: "preferredHighlightEnd"; type: "double" }
        Property { name: "highlightRangeMode"; type: "HighlightRangeMode" }
        Property { name: "highlightMoveDuration"; type: "int" }
        Property { name: "dragMargin"; type: "double" }
        Property { name: "maximumFlickVelocity"; type: "double" }
        Property { name: "flickDeceleration"; type: "double" }
        Property { name: "interactive"; type: "bool" }
        Property { name: "moving"; type: "bool"; isReadonly: true }
        Property { name: "flicking"; type: "bool"; isReadonly: true }
        Property { name: "dragging"; type: "bool"; isReadonly: true }
        Property { name: "count"; type: "int"; isReadonly: true }
        Property { name: "delegate"; type: "QQmlComponent"; isPointer: true }
        Property { name: "pathItemCount"; type: "int" }
        Property { name: "snapMode"; type: "SnapMode" }
        Property { name: "movementDirection"; revision: 7; type: "MovementDirection" }
        Property { name: "cacheItemCount"; type: "int" }
        Signal { name: "snapPositionChanged" }
        Signal { name: "movementStarted" }
        Signal { name: "movementEnded" }
        Signal { name: "movementDirectionChanged"; revision: 7 }
        Signal { name: "flickStarted" }
        Signal { name: "flickEnded" }
        Signal { name: "dragStarted" }
        Signal { name: "dragEnded" }
        Method { name: "incrementCurrentIndex" }
        Method { name: "decrementCurrentIndex" }
        Method { name: "refill" }
        Method { name: "ticked" }
        Method { name: "movementEnding" }
        Method {
            name: "modelUpdated"
            Parameter { name: "changeSet"; type: "QQmlChangeSet" }
            Parameter { name: "reset"; type: "bool" }
        }
        Method {
            name: "createdItem"
            Parameter { name: "index"; type: "int" }
            Parameter { name: "item"; type: "QObject"; isPointer: true }
        }
        Method {
            name: "initItem"
            Parameter { name: "index"; type: "int" }
            Parameter { name: "item"; type: "QObject"; isPointer: true }
        }
        Method {
            name: "destroyingItem"
            Parameter { name: "item"; type: "QObject"; isPointer: true }
        }
        Method { name: "pathUpdated" }
        Method {
            name: "positionViewAtIndex"
            Parameter { name: "index"; type: "int" }
            Parameter { name: "mode"; type: "int" }
        }
        Method {
            name: "indexAt"
            type: "int"
            Parameter { name: "x"; type: "double" }
            Parameter { name: "y"; type: "double" }
        }
        Method {
            name: "itemAt"
            type: "QQuickItem*"
            Parameter { name: "x"; type: "double" }
            Parameter { name: "y"; type: "double" }
        }
        Method {
            name: "itemAtIndex"
            revision: 13
            type: "QQuickItem*"
            Parameter { name: "index"; type: "int" }
        }
    }
    Component {
        name: "QQuickPathViewAttached"
        prototype: "QObject"
        Property { name: "view"; type: "QQuickPathView"; isReadonly: true; isPointer: true }
        Property { name: "isCurrentItem"; type: "bool"; isReadonly: true }
        Property { name: "onPath"; type: "bool"; isReadonly: true }
        Signal { name: "currentItemChanged" }
        Signal { name: "pathChanged" }
    }
    Component {
        file: "private/qquickanimation_p.h"
        name: "QQuickPauseAnimation"
        prototype: "QQuickAbstractAnimation"
        exports: ["QtQuick/PauseAnimation 2.0", "QtQuick/PauseAnimation 2.12"]
        exportMetaObjectRevisions: [0, 12]
        Property { name: "duration"; type: "int" }
        Signal {
            name: "durationChanged"
            Parameter { type: "int" }
        }
    }
    Component {
        file: "private/qquickrectangle_p.h"
        name: "QQuickPen"
        prototype: "QObject"
        Property { name: "width"; type: "double" }
        Property { name: "color"; type: "QColor" }
        Property { name: "pixelAligned"; type: "bool" }
        Signal { name: "penChanged" }
    }
    Component {
        file: "private/qquickpincharea_p.h"
        name: "QQuickPinch"
        prototype: "QObject"
        exports: ["QtQuick/Pinch 2.0"]
        exportMetaObjectRevisions: [0]
        Enum {
            name: "Axis"
            values: ["NoDrag", "XAxis", "YAxis", "XAndYAxis", "XandYAxis"]
        }
        Property { name: "target"; type: "QQuickItem"; isPointer: true }
        Property { name: "minimumScale"; type: "double" }
        Property { name: "maximumScale"; type: "double" }
        Property { name: "minimumRotation"; type: "double" }
        Property { name: "maximumRotation"; type: "double" }
        Property { name: "dragAxis"; type: "Axis" }
        Property { name: "minimumX"; type: "double" }
        Property { name: "maximumX"; type: "double" }
        Property { name: "minimumY"; type: "double" }
        Property { name: "maximumY"; type: "double" }
        Property { name: "active"; type: "bool"; isReadonly: true }
    }
    Component {
        file: "private/qquickpincharea_p.h"
        name: "QQuickPinchArea"
        defaultProperty: "data"
        prototype: "QQuickItem"
        exports: [
            "QtQuick/PinchArea 2.0",
            "QtQuick/PinchArea 2.1",
            "QtQuick/PinchArea 2.11",
            "QtQuick/PinchArea 2.4",
            "QtQuick/PinchArea 2.5",
            "QtQuick/PinchArea 2.7"
        ]
        exportMetaObjectRevisions: [0, 1, 11, 4, 5, 7]
        Property { name: "enabled"; type: "bool" }
        Property { name: "pinch"; type: "QQuickPinch"; isReadonly: true; isPointer: true }
        Signal {
            name: "pinchStarted"
            Parameter { name: "pinch"; type: "QQuickPinchEvent"; isPointer: true }
        }
        Signal {
            name: "pinchUpdated"
            Parameter { name: "pinch"; type: "QQuickPinchEvent"; isPointer: true }
        }
        Signal {
            name: "pinchFinished"
            Parameter { name: "pinch"; type: "QQuickPinchEvent"; isPointer: true }
        }
        Signal {
            name: "smartZoom"
            revision: 5
            Parameter { name: "pinch"; type: "QQuickPinchEvent"; isPointer: true }
        }
    }
    Component {
        file: "private/qquickpincharea_p.h"
        name: "QQuickPinchEvent"
        prototype: "QObject"
        Property { name: "center"; type: "QPointF"; isReadonly: true }
        Property { name: "startCenter"; type: "QPointF"; isReadonly: true }
        Property { name: "previousCenter"; type: "QPointF"; isReadonly: true }
        Property { name: "scale"; type: "double"; isReadonly: true }
        Property { name: "previousScale"; type: "double"; isReadonly: true }
        Property { name: "angle"; type: "double"; isReadonly: true }
        Property { name: "previousAngle"; type: "double"; isReadonly: true }
        Property { name: "rotation"; type: "double"; isReadonly: true }
        Property { name: "point1"; type: "QPointF"; isReadonly: true }
        Property { name: "startPoint1"; type: "QPointF"; isReadonly: true }
        Property { name: "point2"; type: "QPointF"; isReadonly: true }
        Property { name: "startPoint2"; type: "QPointF"; isReadonly: true }
        Property { name: "pointCount"; type: "int"; isReadonly: true }
        Property { name: "accepted"; type: "bool" }
    }
    Component {
        file: "private/qquickpinchhandler_p.h"
        name: "QQuickPinchHandler"
        prototype: "QQuickMultiPointHandler"
        exports: ["QtQuick/PinchHandler 2.12", "QtQuick/PinchHandler 2.15"]
        exportMetaObjectRevisions: [12, 15]
        Property { name: "minimumScale"; type: "double" }
        Property { name: "maximumScale"; type: "double" }
        Property { name: "minimumRotation"; type: "double" }
        Property { name: "maximumRotation"; type: "double" }
        Property { name: "scale"; type: "double"; isReadonly: true }
        Property { name: "activeScale"; type: "double"; isReadonly: true }
        Property { name: "rotation"; type: "double"; isReadonly: true }
        Property { name: "translation"; type: "QVector2D"; isReadonly: true }
        Property { name: "minimumX"; type: "double" }
        Property { name: "maximumX"; type: "double" }
        Property { name: "minimumY"; type: "double" }
        Property { name: "maximumY"; type: "double" }
        Property { name: "xAxis"; type: "QQuickDragAxis"; isReadonly: true; isPointer: true }
        Property { name: "yAxis"; type: "QQuickDragAxis"; isReadonly: true; isPointer: true }
        Signal { name: "updated" }
    }
    Component {
        file: "private/qquickpointhandler_p.h"
        name: "QQuickPointHandler"
        prototype: "QQuickSinglePointHandler"
        exports: ["QtQuick/PointHandler 2.12", "QtQuick/PointHandler 2.15"]
        exportMetaObjectRevisions: [12, 15]
        Property { name: "translation"; type: "QVector2D"; isReadonly: true }
    }
    Component {
        file: "private/qquickevents_p_p.h"
        name: "QQuickPointerDevice"
        prototype: "QObject"
        exports: ["QtQuick/PointerDevice 2.12"]
        isCreatable: false
        exportMetaObjectRevisions: [12]
        Enum {
            name: "DeviceTypes"
            alias: "DeviceType"
            isFlag: true
            values: [
                "UnknownDevice",
                "Mouse",
                "TouchScreen",
                "TouchPad",
                "Puck",
                "Stylus",
                "Airbrush",
                "AllDevices"
            ]
        }
        Enum {
            name: "PointerTypes"
            alias: "PointerType"
            isFlag: true
            values: [
                "GenericPointer",
                "Finger",
                "Pen",
                "Eraser",
                "Cursor",
                "AllPointerTypes"
            ]
        }
        Enum {
            name: "Capabilities"
            alias: "CapabilityFlag"
            isFlag: true
            values: [
                "Position",
                "Area",
                "Pressure",
                "Velocity",
                "MouseEmulation",
                "Scroll",
                "Hover",
                "Rotation",
                "XTilt",
                "YTilt"
            ]
        }
        Property { name: "type"; type: "DeviceType"; isReadonly: true }
        Property { name: "pointerType"; type: "PointerType"; isReadonly: true }
        Property { name: "capabilities"; type: "Capabilities"; isReadonly: true }
        Property { name: "maximumTouchPoints"; type: "int"; isReadonly: true }
        Property { name: "buttonCount"; type: "int"; isReadonly: true }
        Property { name: "name"; type: "string"; isReadonly: true }
        Property { name: "uniqueId"; type: "QPointingDeviceUniqueId"; isReadonly: true }
    }
    Component {
        file: "private/qquickpointerhandler_p.h"
        name: "QQuickPointerDeviceHandler"
        prototype: "QQuickPointerHandler"
        Property { name: "acceptedDevices"; type: "QQuickPointerDevice::DeviceTypes" }
        Property { name: "acceptedPointerTypes"; type: "QQuickPointerDevice::PointerTypes" }
        Property { name: "acceptedButtons"; type: "Qt::MouseButtons" }
        Property { name: "acceptedModifiers"; type: "Qt::KeyboardModifiers" }
        Method {
            name: "setAcceptedDevices"
            Parameter { name: "acceptedDevices"; type: "QQuickPointerDevice::DeviceTypes" }
        }
        Method {
            name: "setAcceptedPointerTypes"
            Parameter { name: "acceptedPointerTypes"; type: "QQuickPointerDevice::PointerTypes" }
        }
        Method {
            name: "setAcceptedButtons"
            Parameter { name: "buttons"; type: "Qt::MouseButtons" }
        }
        Method {
            name: "setAcceptedModifiers"
            Parameter { name: "acceptedModifiers"; type: "Qt::KeyboardModifiers" }
        }
    }
    Component {
        file: "private/qquickevents_p_p.h"
        name: "QQuickPointerEvent"
        prototype: "QObject"
        exports: ["QtQuick/PointerEvent 2.12"]
        isCreatable: false
        exportMetaObjectRevisions: [12]
        Property { name: "device"; type: "QQuickPointerDevice"; isReadonly: true; isPointer: true }
        Property { name: "modifiers"; type: "Qt::KeyboardModifiers"; isReadonly: true }
        Property { name: "button"; type: "Qt::MouseButtons"; isReadonly: true }
        Property { name: "buttons"; type: "Qt::MouseButtons"; isReadonly: true }
    }
    Component {
        file: "private/qquickpointerhandler_p.h"
        name: "QQuickPointerHandler"
        prototype: "QObject"
        exports: [
            "QtQuick/PointerHandler 2.12",
            "QtQuick/PointerHandler 2.15"
        ]
        isCreatable: false
        exportMetaObjectRevisions: [12, 15]
        Enum {
            name: "GrabPermissions"
            alias: "GrabPermission"
            isFlag: true
            values: [
                "TakeOverForbidden",
                "CanTakeOverFromHandlersOfSameType",
                "CanTakeOverFromHandlersOfDifferentType",
                "CanTakeOverFromItems",
                "CanTakeOverFromAnything",
                "ApprovesTakeOverByHandlersOfSameType",
                "ApprovesTakeOverByHandlersOfDifferentType",
                "ApprovesTakeOverByItems",
                "ApprovesCancellation",
                "ApprovesTakeOverByAnything"
            ]
        }
        Property { name: "enabled"; type: "bool" }
        Property { name: "active"; type: "bool"; isReadonly: true }
        Property { name: "target"; type: "QQuickItem"; isPointer: true }
        Property { name: "parent"; type: "QQuickItem"; isReadonly: true; isPointer: true }
        Property { name: "grabPermissions"; type: "GrabPermissions" }
        Property { name: "margin"; type: "double" }
        Property { name: "dragThreshold"; revision: 15; type: "int" }
        Property { name: "cursorShape"; revision: 15; type: "Qt::CursorShape" }
        Signal { name: "dragThresholdChanged"; revision: 15 }
        Signal {
            name: "grabChanged"
            Parameter { name: "transition"; type: "QQuickEventPoint::GrabTransition" }
            Parameter { name: "point"; type: "QQuickEventPoint"; isPointer: true }
        }
        Signal { name: "grabPermissionChanged" }
        Signal {
            name: "canceled"
            Parameter { name: "point"; type: "QQuickEventPoint"; isPointer: true }
        }
        Signal { name: "cursorShapeChanged"; revision: 15 }
    }
    Component {
        file: "private/qquickevents_p_p.h"
        name: "QQuickPointerMouseEvent"
        prototype: "QQuickSinglePointEvent"
        exports: ["QtQuick/PointerMouseEvent 2.12"]
        isCreatable: false
        exportMetaObjectRevisions: [12]
    }
    Component {
        file: "private/qquickevents_p_p.h"
        name: "QQuickPointerScrollEvent"
        prototype: "QQuickSinglePointEvent"
        exports: ["QtQuick/PointerScrollEvent 2.14"]
        isCreatable: false
        exportMetaObjectRevisions: [14]
        Property { name: "angleDelta"; type: "QVector2D"; isReadonly: true }
        Property { name: "pixelDelta"; type: "QVector2D"; isReadonly: true }
        Property { name: "hasAngleDelta"; type: "bool"; isReadonly: true }
        Property { name: "hasPixelDelta"; type: "bool"; isReadonly: true }
        Property { name: "inverted"; type: "bool"; isReadonly: true }
    }
    Component {
        file: "private/qquickevents_p_p.h"
        name: "QQuickPointerTouchEvent"
        prototype: "QQuickPointerEvent"
        exports: ["QtQuick/PointerTouchEvent 2.12"]
        isCreatable: false
        exportMetaObjectRevisions: [12]
    }
    Component {
        name: "QQuickPositionerAttached"
        prototype: "QObject"
        Property { name: "index"; type: "int"; isReadonly: true }
        Property { name: "isFirstItem"; type: "bool"; isReadonly: true }
        Property { name: "isLastItem"; type: "bool"; isReadonly: true }
    }
    Component {
        file: "private/qquickanimation_p.h"
        name: "QQuickPropertyAction"
        prototype: "QQuickAbstractAnimation"
        exports: ["QtQuick/PropertyAction 2.0", "QtQuick/PropertyAction 2.12"]
        exportMetaObjectRevisions: [0, 12]
        Property { name: "target"; type: "QObject"; isPointer: true }
        Property { name: "property"; type: "string" }
        Property { name: "properties"; type: "string" }
        Property { name: "targets"; type: "QObject"; isList: true; isReadonly: true }
        Property { name: "exclude"; type: "QObject"; isList: true; isReadonly: true }
        Property { name: "value"; type: "QVariant" }
        Signal {
            name: "valueChanged"
            Parameter { type: "QVariant" }
        }
        Signal {
            name: "propertiesChanged"
            Parameter { type: "string" }
        }
    }
    Component {
        file: "private/qquickanimation_p.h"
        name: "QQuickPropertyAnimation"
        prototype: "QQuickAbstractAnimation"
        exports: [
            "QtQuick/PropertyAnimation 2.0",
            "QtQuick/PropertyAnimation 2.12"
        ]
        exportMetaObjectRevisions: [0, 12]
        Property { name: "duration"; type: "int" }
        Property { name: "from"; type: "QVariant" }
        Property { name: "to"; type: "QVariant" }
        Property { name: "easing"; type: "QEasingCurve" }
        Property { name: "target"; type: "QObject"; isPointer: true }
        Property { name: "property"; type: "string" }
        Property { name: "properties"; type: "string" }
        Property { name: "targets"; type: "QObject"; isList: true; isReadonly: true }
        Property { name: "exclude"; type: "QObject"; isList: true; isReadonly: true }
        Signal {
            name: "durationChanged"
            Parameter { type: "int" }
        }
        Signal {
            name: "easingChanged"
            Parameter { type: "QEasingCurve" }
        }
        Signal {
            name: "propertiesChanged"
            Parameter { type: "string" }
        }
    }
    Component {
        file: "private/qquickpropertychanges_p.h"
        name: "QQuickPropertyChanges"
        prototype: "QQuickStateOperation"
        exports: ["QtQuick/PropertyChanges 2.0"]
        exportMetaObjectRevisions: [0]
        Property { name: "target"; type: "QObject"; isPointer: true }
        Property { name: "restoreEntryValues"; type: "bool" }
        Property { name: "explicit"; type: "bool" }
    }
    Component {
        file: "private/qquickrectangle_p.h"
        name: "QQuickRectangle"
        defaultProperty: "data"
        prototype: "QQuickItem"
        exports: [
            "QtQuick/Rectangle 2.0",
            "QtQuick/Rectangle 2.1",
            "QtQuick/Rectangle 2.11",
            "QtQuick/Rectangle 2.4",
            "QtQuick/Rectangle 2.7"
        ]
        exportMetaObjectRevisions: [0, 1, 11, 4, 7]
        Property { name: "color"; type: "QColor" }
        Property { name: "gradient"; type: "QJSValue" }
        Property { name: "border"; type: "QQuickPen"; isReadonly: true; isPointer: true }
        Property { name: "radius"; type: "double" }
        Method { name: "doUpdate" }
    }
    Component {
        file: "private/qquickrepeater_p.h"
        name: "QQuickRepeater"
        defaultProperty: "delegate"
        prototype: "QQuickItem"
        exports: [
            "QtQuick/Repeater 2.0",
            "QtQuick/Repeater 2.1",
            "QtQuick/Repeater 2.11",
            "QtQuick/Repeater 2.4",
            "QtQuick/Repeater 2.7"
        ]
        exportMetaObjectRevisions: [0, 1, 11, 4, 7]
        Property { name: "model"; type: "QVariant" }
        Property { name: "delegate"; type: "QQmlComponent"; isPointer: true }
        Property { name: "count"; type: "int"; isReadonly: true }
        Signal {
            name: "itemAdded"
            Parameter { name: "index"; type: "int" }
            Parameter { name: "item"; type: "QQuickItem"; isPointer: true }
        }
        Signal {
            name: "itemRemoved"
            Parameter { name: "index"; type: "int" }
            Parameter { name: "item"; type: "QQuickItem"; isPointer: true }
        }
        Method {
            name: "createdItem"
            Parameter { name: "index"; type: "int" }
            Parameter { name: "item"; type: "QObject"; isPointer: true }
        }
        Method {
            name: "initItem"
            Parameter { type: "int" }
            Parameter { name: "item"; type: "QObject"; isPointer: true }
        }
        Method {
            name: "modelUpdated"
            Parameter { name: "changeSet"; type: "QQmlChangeSet" }
            Parameter { name: "reset"; type: "bool" }
        }
        Method {
            name: "itemAt"
            type: "QQuickItem*"
            Parameter { name: "index"; type: "int" }
        }
    }
    Component {
        file: "private/qquicktranslate_p.h"
        name: "QQuickRotation"
        prototype: "QQuickTransform"
        exports: ["QtQuick/Rotation 2.0"]
        exportMetaObjectRevisions: [0]
        Property { name: "origin"; type: "QVector3D" }
        Property { name: "angle"; type: "double" }
        Property { name: "axis"; type: "QVector3D" }
    }
    Component {
        file: "private/qquickanimation_p.h"
        name: "QQuickRotationAnimation"
        prototype: "QQuickPropertyAnimation"
        exports: [
            "QtQuick/RotationAnimation 2.0",
            "QtQuick/RotationAnimation 2.12"
        ]
        exportMetaObjectRevisions: [0, 12]
        Enum {
            name: "RotationDirection"
            values: ["Numerical", "Shortest", "Clockwise", "Counterclockwise"]
        }
        Property { name: "from"; type: "double" }
        Property { name: "to"; type: "double" }
        Property { name: "direction"; type: "RotationDirection" }
    }
    Component {
        file: "private/qquickanimator_p.h"
        name: "QQuickRotationAnimator"
        prototype: "QQuickAnimator"
        exports: [
            "QtQuick/RotationAnimator 2.12",
            "QtQuick/RotationAnimator 2.2"
        ]
        exportMetaObjectRevisions: [12, 2]
        Enum {
            name: "RotationDirection"
            values: ["Numerical", "Shortest", "Clockwise", "Counterclockwise"]
        }
        Property { name: "direction"; type: "RotationDirection" }
        Signal {
            name: "directionChanged"
            Parameter { name: "dir"; type: "RotationDirection" }
        }
    }
    Component {
        file: "private/qquickpositioners_p.h"
        name: "QQuickRow"
        prototype: "QQuickBasePositioner"
        exports: [
            "QtQuick/Row 2.0",
            "QtQuick/Row 2.1",
            "QtQuick/Row 2.11",
            "QtQuick/Row 2.4",
            "QtQuick/Row 2.6",
            "QtQuick/Row 2.7",
            "QtQuick/Row 2.9"
        ]
        exportMetaObjectRevisions: [0, 1, 11, 4, 6, 7, 9]
        Property { name: "layoutDirection"; type: "Qt::LayoutDirection" }
        Property { name: "effectiveLayoutDirection"; type: "Qt::LayoutDirection"; isReadonly: true }
    }
    Component {
        file: "private/qquicktranslate_p.h"
        name: "QQuickScale"
        prototype: "QQuickTransform"
        exports: ["QtQuick/Scale 2.0"]
        exportMetaObjectRevisions: [0]
        Property { name: "origin"; type: "QVector3D" }
        Property { name: "xScale"; type: "double" }
        Property { name: "yScale"; type: "double" }
        Property { name: "zScale"; type: "double" }
        Signal { name: "scaleChanged" }
    }
    Component {
        file: "private/qquickanimator_p.h"
        name: "QQuickScaleAnimator"
        prototype: "QQuickAnimator"
        exports: ["QtQuick/ScaleAnimator 2.12", "QtQuick/ScaleAnimator 2.2"]
        exportMetaObjectRevisions: [12, 2]
    }
    Component {
        file: "private/qquickscalegrid_p_p.h"
        name: "QQuickScaleGrid"
        prototype: "QObject"
        Property { name: "left"; type: "int" }
        Property { name: "top"; type: "int" }
        Property { name: "right"; type: "int" }
        Property { name: "bottom"; type: "int" }
        Signal { name: "borderChanged" }
        Signal { name: "leftBorderChanged" }
        Signal { name: "topBorderChanged" }
        Signal { name: "rightBorderChanged" }
        Signal { name: "bottomBorderChanged" }
    }
    Component {
        file: "private/qquickanimation_p.h"
        name: "QQuickScriptAction"
        prototype: "QQuickAbstractAnimation"
        exports: ["QtQuick/ScriptAction 2.0", "QtQuick/ScriptAction 2.12"]
        exportMetaObjectRevisions: [0, 12]
        Property { name: "script"; type: "QQmlScriptString" }
        Property { name: "scriptName"; type: "string" }
    }
    Component {
        file: "private/qquickanimation_p.h"
        name: "QQuickSequentialAnimation"
        defaultProperty: "animations"
        prototype: "QQuickAnimationGroup"
        exports: [
            "QtQuick/SequentialAnimation 2.0",
            "QtQuick/SequentialAnimation 2.12"
        ]
        exportMetaObjectRevisions: [0, 12]
    }
    Component {
        file: "private/qquickshadereffect_p.h"
        name: "QQuickShaderEffect"
        defaultProperty: "data"
        prototype: "QQuickItem"
        exports: [
            "QtQuick/ShaderEffect 2.0",
            "QtQuick/ShaderEffect 2.1",
            "QtQuick/ShaderEffect 2.11",
            "QtQuick/ShaderEffect 2.4",
            "QtQuick/ShaderEffect 2.7"
        ]
        exportMetaObjectRevisions: [0, 1, 11, 4, 7]
        Enum {
            name: "CullMode"
            values: ["NoCulling", "BackFaceCulling", "FrontFaceCulling"]
        }
        Enum {
            name: "Status"
            values: ["Compiled", "Uncompiled", "Error"]
        }
        Property { name: "fragmentShader"; type: "QByteArray" }
        Property { name: "vertexShader"; type: "QByteArray" }
        Property { name: "blending"; type: "bool" }
        Property { name: "mesh"; type: "QVariant" }
        Property { name: "cullMode"; type: "CullMode" }
        Property { name: "log"; type: "string"; isReadonly: true }
        Property { name: "status"; type: "Status"; isReadonly: true }
        Property { name: "supportsAtlasTextures"; revision: 4; type: "bool" }
    }
    Component {
        file: "private/qquickshadereffectmesh_p.h"
        name: "QQuickShaderEffectMesh"
        prototype: "QObject"
        exports: ["QtQuick/ShaderEffectMesh 2.0"]
        isCreatable: false
        exportMetaObjectRevisions: [0]
        Signal { name: "geometryChanged" }
    }
    Component {
        file: "private/qquickshadereffectsource_p.h"
        name: "QQuickShaderEffectSource"
        defaultProperty: "data"
        prototype: "QQuickItem"
        exports: [
            "QtQuick/ShaderEffectSource 2.0",
            "QtQuick/ShaderEffectSource 2.1",
            "QtQuick/ShaderEffectSource 2.11",
            "QtQuick/ShaderEffectSource 2.4",
            "QtQuick/ShaderEffectSource 2.6",
            "QtQuick/ShaderEffectSource 2.7",
            "QtQuick/ShaderEffectSource 2.9"
        ]
        exportMetaObjectRevisions: [0, 1, 11, 4, 6, 7, 9]
        Enum {
            name: "WrapMode"
            values: [
                "ClampToEdge",
                "RepeatHorizontally",
                "RepeatVertically",
                "Repeat"
            ]
        }
        Enum {
            name: "Format"
            values: ["Alpha", "RGB", "RGBA"]
        }
        Enum {
            name: "TextureMirroring"
            values: ["NoMirroring", "MirrorHorizontally", "MirrorVertically"]
        }
        Property { name: "wrapMode"; type: "WrapMode" }
        Property { name: "sourceItem"; type: "QQuickItem"; isPointer: true }
        Property { name: "sourceRect"; type: "QRectF" }
        Property { name: "textureSize"; type: "QSize" }
        Property { name: "format"; type: "Format" }
        Property { name: "live"; type: "bool" }
        Property { name: "hideSource"; type: "bool" }
        Property { name: "mipmap"; type: "bool" }
        Property { name: "recursive"; type: "bool" }
        Property { name: "textureMirroring"; revision: 6; type: "TextureMirroring" }
        Property { name: "samples"; revision: 9; type: "int" }
        Signal { name: "scheduledUpdateCompleted" }
        Method {
            name: "sourceItemDestroyed"
            Parameter { name: "item"; type: "QObject"; isPointer: true }
        }
        Method { name: "invalidateSceneGraph" }
        Method {
            name: "sourceItemParentChanged"
            Parameter { name: "parent"; type: "QQuickItem"; isPointer: true }
        }
        Method { name: "scheduleUpdate" }
    }
    Component {
        file: "private/qquickshortcut_p.h"
        name: "QQuickShortcut"
        prototype: "QObject"
        exports: [
            "QtQuick/Shortcut 2.5",
            "QtQuick/Shortcut 2.6",
            "QtQuick/Shortcut 2.9"
        ]
        exportMetaObjectRevisions: [5, 6, 9]
        Property { name: "sequence"; type: "QVariant" }
        Property { name: "sequences"; revision: 9; type: "QVariantList" }
        Property { name: "nativeText"; revision: 6; type: "string"; isReadonly: true }
        Property { name: "portableText"; revision: 6; type: "string"; isReadonly: true }
        Property { name: "enabled"; type: "bool" }
        Property { name: "autoRepeat"; type: "bool" }
        Property { name: "context"; type: "Qt::ShortcutContext" }
        Signal { name: "sequencesChanged"; revision: 9 }
        Signal { name: "activated" }
        Signal { name: "activatedAmbiguously" }
    }
    Component {
        file: "private/qquickevents_p_p.h"
        name: "QQuickSinglePointEvent"
        prototype: "QQuickPointerEvent"
    }
    Component {
        file: "private/qquickpointerhandler_p.h"
        name: "QQuickSinglePointHandler"
        prototype: "QQuickPointerDeviceHandler"
        Property { name: "point"; type: "QQuickHandlerPoint"; isReadonly: true }
    }
    Component {
        file: "private/qquicksmoothedanimation_p.h"
        name: "QQuickSmoothedAnimation"
        prototype: "QQuickNumberAnimation"
        exports: [
            "QtQuick/SmoothedAnimation 2.0",
            "QtQuick/SmoothedAnimation 2.12"
        ]
        exportMetaObjectRevisions: [0, 12]
        Enum {
            name: "ReversingMode"
            values: ["Eased", "Immediate", "Sync"]
        }
        Property { name: "velocity"; type: "double" }
        Property { name: "reversingMode"; type: "ReversingMode" }
        Property { name: "maximumEasingTime"; type: "double" }
    }
    Component {
        file: "private/qquickspringanimation_p.h"
        name: "QQuickSpringAnimation"
        prototype: "QQuickNumberAnimation"
        exports: [
            "QtQuick/SpringAnimation 2.0",
            "QtQuick/SpringAnimation 2.12"
        ]
        exportMetaObjectRevisions: [0, 12]
        Property { name: "velocity"; type: "double" }
        Property { name: "spring"; type: "double" }
        Property { name: "damping"; type: "double" }
        Property { name: "epsilon"; type: "double" }
        Property { name: "modulus"; type: "double" }
        Property { name: "mass"; type: "double" }
        Signal { name: "syncChanged" }
    }
    Component {
        file: "private/qquicksprite_p.h"
        name: "QQuickSprite"
        prototype: "QQuickStochasticState"
        exports: ["QtQuick/Sprite 2.0"]
        exportMetaObjectRevisions: [0]
        Property { name: "source"; type: "QUrl" }
        Property { name: "reverse"; type: "bool" }
        Property { name: "frameSync"; type: "bool" }
        Property { name: "frames"; type: "int" }
        Property { name: "frameCount"; type: "int" }
        Property { name: "frameHeight"; type: "int" }
        Property { name: "frameWidth"; type: "int" }
        Property { name: "frameX"; type: "int" }
        Property { name: "frameY"; type: "int" }
        Property { name: "frameRate"; type: "double" }
        Property { name: "frameRateVariation"; type: "double" }
        Property { name: "frameDuration"; type: "int" }
        Property { name: "frameDurationVariation"; type: "int" }
        Signal {
            name: "sourceChanged"
            Parameter { name: "arg"; type: "QUrl" }
        }
        Signal {
            name: "frameHeightChanged"
            Parameter { name: "arg"; type: "int" }
        }
        Signal {
            name: "frameWidthChanged"
            Parameter { name: "arg"; type: "int" }
        }
        Signal {
            name: "reverseChanged"
            Parameter { name: "arg"; type: "bool" }
        }
        Signal {
            name: "frameCountChanged"
            Parameter { name: "arg"; type: "int" }
        }
        Signal {
            name: "frameXChanged"
            Parameter { name: "arg"; type: "int" }
        }
        Signal {
            name: "frameYChanged"
            Parameter { name: "arg"; type: "int" }
        }
        Signal {
            name: "frameRateChanged"
            Parameter { name: "arg"; type: "double" }
        }
        Signal {
            name: "frameRateVariationChanged"
            Parameter { name: "arg"; type: "double" }
        }
        Signal {
            name: "frameDurationChanged"
            Parameter { name: "arg"; type: "int" }
        }
        Signal {
            name: "frameDurationVariationChanged"
            Parameter { name: "arg"; type: "int" }
        }
        Signal {
            name: "frameSyncChanged"
            Parameter { name: "arg"; type: "bool" }
        }
        Method {
            name: "setSource"
            Parameter { name: "arg"; type: "QUrl" }
        }
        Method {
            name: "setFrameHeight"
            Parameter { name: "arg"; type: "int" }
        }
        Method {
            name: "setFrameWidth"
            Parameter { name: "arg"; type: "int" }
        }
        Method {
            name: "setReverse"
            Parameter { name: "arg"; type: "bool" }
        }
        Method {
            name: "setFrames"
            Parameter { name: "arg"; type: "int" }
        }
        Method {
            name: "setFrameCount"
            Parameter { name: "arg"; type: "int" }
        }
        Method {
            name: "setFrameX"
            Parameter { name: "arg"; type: "int" }
        }
        Method {
            name: "setFrameY"
            Parameter { name: "arg"; type: "int" }
        }
        Method {
            name: "setFrameRate"
            Parameter { name: "arg"; type: "double" }
        }
        Method {
            name: "setFrameRateVariation"
            Parameter { name: "arg"; type: "double" }
        }
        Method {
            name: "setFrameDuration"
            Parameter { name: "arg"; type: "int" }
        }
        Method {
            name: "setFrameDurationVariation"
            Parameter { name: "arg"; type: "int" }
        }
        Method {
            name: "setFrameSync"
            Parameter { name: "arg"; type: "bool" }
        }
        Method { name: "startImageLoading" }
    }
    Component {
        file: "private/qquickspritesequence_p.h"
        name: "QQuickSpriteSequence"
        defaultProperty: "sprites"
        prototype: "QQuickItem"
        exports: [
            "QtQuick/SpriteSequence 2.0",
            "QtQuick/SpriteSequence 2.1",
            "QtQuick/SpriteSequence 2.11",
            "QtQuick/SpriteSequence 2.4",
            "QtQuick/SpriteSequence 2.7"
        ]
        exportMetaObjectRevisions: [0, 1, 11, 4, 7]
        Property { name: "running"; type: "bool" }
        Property { name: "interpolate"; type: "bool" }
        Property { name: "goalSprite"; type: "string" }
        Property { name: "currentSprite"; type: "string"; isReadonly: true }
        Property { name: "sprites"; type: "QQuickSprite"; isList: true; isReadonly: true }
        Signal {
            name: "runningChanged"
            Parameter { name: "arg"; type: "bool" }
        }
        Signal {
            name: "interpolateChanged"
            Parameter { name: "arg"; type: "bool" }
        }
        Signal {
            name: "goalSpriteChanged"
            Parameter { name: "arg"; type: "string" }
        }
        Signal {
            name: "currentSpriteChanged"
            Parameter { name: "arg"; type: "string" }
        }
        Method {
            name: "jumpTo"
            Parameter { name: "sprite"; type: "string" }
        }
        Method {
            name: "setGoalSprite"
            Parameter { name: "sprite"; type: "string" }
        }
        Method {
            name: "setRunning"
            Parameter { name: "arg"; type: "bool" }
        }
        Method {
            name: "setInterpolate"
            Parameter { name: "arg"; type: "bool" }
        }
        Method { name: "createEngine" }
    }
    Component {
        file: "private/qquickstate_p.h"
        name: "QQuickState"
        defaultProperty: "changes"
        prototype: "QObject"
        exports: ["QtQuick/State 2.0"]
        exportMetaObjectRevisions: [0]
        Property { name: "name"; type: "string" }
        Property { name: "when"; type: "bool" }
        Property { name: "extend"; type: "string" }
        Property { name: "changes"; type: "QQuickStateOperation"; isList: true; isReadonly: true }
        Signal { name: "completed" }
    }
    Component {
        file: "private/qquickstatechangescript_p.h"
        name: "QQuickStateChangeScript"
        prototype: "QQuickStateOperation"
        exports: ["QtQuick/StateChangeScript 2.0"]
        exportMetaObjectRevisions: [0]
        Property { name: "script"; type: "QQmlScriptString" }
        Property { name: "name"; type: "string" }
    }
    Component {
        file: "private/qquickstategroup_p.h"
        name: "QQuickStateGroup"
        prototype: "QObject"
        exports: ["QtQuick/StateGroup 2.0"]
        exportMetaObjectRevisions: [0]
        Property { name: "state"; type: "string" }
        Property { name: "states"; type: "QQuickState"; isList: true; isReadonly: true }
        Property { name: "transitions"; type: "QQuickTransition"; isList: true; isReadonly: true }
        Signal {
            name: "stateChanged"
            Parameter { type: "string" }
        }
    }
    Component { file: "private/qquickstate_p.h"; name: "QQuickStateOperation"; prototype: "QObject" }
    Component {
        name: "QQuickStochasticState"
        prototype: "QObject"
        Property { name: "duration"; type: "int" }
        Property { name: "durationVariation"; type: "int" }
        Property { name: "randomStart"; type: "bool" }
        Property { name: "to"; type: "QVariantMap" }
        Property { name: "name"; type: "string" }
        Signal {
            name: "durationChanged"
            Parameter { name: "arg"; type: "int" }
        }
        Signal {
            name: "nameChanged"
            Parameter { name: "arg"; type: "string" }
        }
        Signal {
            name: "toChanged"
            Parameter { name: "arg"; type: "QVariantMap" }
        }
        Signal {
            name: "durationVariationChanged"
            Parameter { name: "arg"; type: "int" }
        }
        Signal { name: "entered" }
        Signal {
            name: "randomStartChanged"
            Parameter { name: "arg"; type: "bool" }
        }
        Method {
            name: "setDuration"
            Parameter { name: "arg"; type: "int" }
        }
        Method {
            name: "setName"
            Parameter { name: "arg"; type: "string" }
        }
        Method {
            name: "setTo"
            Parameter { name: "arg"; type: "QVariantMap" }
        }
        Method {
            name: "setDurationVariation"
            Parameter { name: "arg"; type: "int" }
        }
        Method {
            name: "setRandomStart"
            Parameter { name: "arg"; type: "bool" }
        }
    }
    Component {
        file: "private/qquicksystempalette_p.h"
        name: "QQuickSystemPalette"
        prototype: "QObject"
        exports: ["QtQuick/SystemPalette 2.0"]
        exportMetaObjectRevisions: [0]
        Enum {
            name: "ColorGroup"
            values: ["Active", "Inactive", "Disabled"]
        }
        Property { name: "colorGroup"; type: "QQuickSystemPalette::ColorGroup" }
        Property { name: "window"; type: "QColor"; isReadonly: true }
        Property { name: "windowText"; type: "QColor"; isReadonly: true }
        Property { name: "base"; type: "QColor"; isReadonly: true }
        Property { name: "text"; type: "QColor"; isReadonly: true }
        Property { name: "alternateBase"; type: "QColor"; isReadonly: true }
        Property { name: "button"; type: "QColor"; isReadonly: true }
        Property { name: "buttonText"; type: "QColor"; isReadonly: true }
        Property { name: "light"; type: "QColor"; isReadonly: true }
        Property { name: "midlight"; type: "QColor"; isReadonly: true }
        Property { name: "dark"; type: "QColor"; isReadonly: true }
        Property { name: "mid"; type: "QColor"; isReadonly: true }
        Property { name: "shadow"; type: "QColor"; isReadonly: true }
        Property { name: "highlight"; type: "QColor"; isReadonly: true }
        Property { name: "highlightedText"; type: "QColor"; isReadonly: true }
        Signal { name: "paletteChanged" }
    }
    Component {
        file: "private/qquicktableview_p.h"
        name: "QQuickTableView"
        defaultProperty: "flickableData"
        prototype: "QQuickFlickable"
        exports: ["QtQuick/TableView 2.12", "QtQuick/TableView 2.14"]
        exportMetaObjectRevisions: [12, 14]
        attachedType: "QQuickTableViewAttached"
        Property { name: "rows"; type: "int"; isReadonly: true }
        Property { name: "columns"; type: "int"; isReadonly: true }
        Property { name: "rowSpacing"; type: "double" }
        Property { name: "columnSpacing"; type: "double" }
        Property { name: "rowHeightProvider"; type: "QJSValue" }
        Property { name: "columnWidthProvider"; type: "QJSValue" }
        Property { name: "model"; type: "QVariant" }
        Property { name: "delegate"; type: "QQmlComponent"; isPointer: true }
        Property { name: "reuseItems"; type: "bool" }
        Property { name: "contentWidth"; type: "double" }
        Property { name: "contentHeight"; type: "double" }
        Property { name: "syncView"; revision: 14; type: "QQuickTableView"; isPointer: true }
        Property { name: "syncDirection"; revision: 14; type: "Qt::Orientations" }
        Signal { name: "syncViewChanged"; revision: 14 }
        Signal { name: "syncDirectionChanged"; revision: 14 }
        Method { name: "_q_componentFinalized" }
        Method { name: "forceLayout" }
    }
    Component {
        name: "QQuickTableViewAttached"
        prototype: "QObject"
        Property { name: "view"; type: "QQuickTableView"; isReadonly: true; isPointer: true }
        Signal { name: "pooled" }
        Signal { name: "reused" }
    }
    Component {
        file: "private/qquicktaphandler_p.h"
        name: "QQuickTapHandler"
        prototype: "QQuickSinglePointHandler"
        exports: ["QtQuick/TapHandler 2.12", "QtQuick/TapHandler 2.15"]
        exportMetaObjectRevisions: [12, 15]
        Enum {
            name: "GesturePolicy"
            values: ["DragThreshold", "WithinBounds", "ReleaseWithinBounds"]
        }
        Property { name: "pressed"; type: "bool"; isReadonly: true }
        Property { name: "tapCount"; type: "int"; isReadonly: true }
        Property { name: "timeHeld"; type: "double"; isReadonly: true }
        Property { name: "longPressThreshold"; type: "double" }
        Property { name: "gesturePolicy"; type: "GesturePolicy" }
        Signal {
            name: "tapped"
            Parameter { name: "eventPoint"; type: "QQuickEventPoint"; isPointer: true }
        }
        Signal {
            name: "singleTapped"
            Parameter { name: "eventPoint"; type: "QQuickEventPoint"; isPointer: true }
        }
        Signal {
            name: "doubleTapped"
            Parameter { name: "eventPoint"; type: "QQuickEventPoint"; isPointer: true }
        }
        Signal { name: "longPressed" }
    }
    Component {
        file: "private/qquicktext_p.h"
        name: "QQuickText"
        prototype: "QQuickImplicitSizeItem"
        exports: [
            "QtQuick/Text 2.0",
            "QtQuick/Text 2.1",
            "QtQuick/Text 2.10",
            "QtQuick/Text 2.11",
            "QtQuick/Text 2.2",
            "QtQuick/Text 2.3",
            "QtQuick/Text 2.4",
            "QtQuick/Text 2.6",
            "QtQuick/Text 2.7",
            "QtQuick/Text 2.9"
        ]
        exportMetaObjectRevisions: [0, 1, 10, 11, 2, 3, 4, 6, 7, 9]
        Enum {
            name: "HAlignment"
            values: [
                "AlignLeft",
                "AlignRight",
                "AlignHCenter",
                "AlignJustify"
            ]
        }
        Enum {
            name: "VAlignment"
            values: ["AlignTop", "AlignBottom", "AlignVCenter"]
        }
        Enum {
            name: "TextStyle"
            values: ["Normal", "Outline", "Raised", "Sunken"]
        }
        Enum {
            name: "TextFormat"
            values: [
                "PlainText",
                "RichText",
                "MarkdownText",
                "AutoText",
                "StyledText"
            ]
        }
        Enum {
            name: "TextElideMode"
            values: ["ElideLeft", "ElideRight", "ElideMiddle", "ElideNone"]
        }
        Enum {
            name: "WrapMode"
            values: [
                "NoWrap",
                "WordWrap",
                "WrapAnywhere",
                "WrapAtWordBoundaryOrAnywhere",
                "Wrap"
            ]
        }
        Enum {
            name: "RenderType"
            values: ["QtRendering", "NativeRendering"]
        }
        Enum {
            name: "LineHeightMode"
            values: ["ProportionalHeight", "FixedHeight"]
        }
        Enum {
            name: "FontSizeMode"
            values: ["FixedSize", "HorizontalFit", "VerticalFit", "Fit"]
        }
        Property { name: "text"; type: "string" }
        Property { name: "font"; type: "QFont" }
        Property { name: "color"; type: "QColor" }
        Property { name: "linkColor"; type: "QColor" }
        Property { name: "style"; type: "TextStyle" }
        Property { name: "styleColor"; type: "QColor" }
        Property { name: "horizontalAlignment"; type: "HAlignment" }
        Property { name: "effectiveHorizontalAlignment"; type: "HAlignment"; isReadonly: true }
        Property { name: "verticalAlignment"; type: "VAlignment" }
        Property { name: "wrapMode"; type: "WrapMode" }
        Property { name: "lineCount"; type: "int"; isReadonly: true }
        Property { name: "truncated"; type: "bool"; isReadonly: true }
        Property { name: "maximumLineCount"; type: "int" }
        Property { name: "textFormat"; type: "TextFormat" }
        Property { name: "elide"; type: "TextElideMode" }
        Property { name: "contentWidth"; type: "double"; isReadonly: true }
        Property { name: "contentHeight"; type: "double"; isReadonly: true }
        Property { name: "paintedWidth"; type: "double"; isReadonly: true }
        Property { name: "paintedHeight"; type: "double"; isReadonly: true }
        Property { name: "lineHeight"; type: "double" }
        Property { name: "lineHeightMode"; type: "LineHeightMode" }
        Property { name: "baseUrl"; type: "QUrl" }
        Property { name: "minimumPixelSize"; type: "int" }
        Property { name: "minimumPointSize"; type: "int" }
        Property { name: "fontSizeMode"; type: "FontSizeMode" }
        Property { name: "renderType"; type: "RenderType" }
        Property { name: "hoveredLink"; revision: 2; type: "string"; isReadonly: true }
        Property { name: "padding"; revision: 6; type: "double" }
        Property { name: "topPadding"; revision: 6; type: "double" }
        Property { name: "leftPadding"; revision: 6; type: "double" }
        Property { name: "rightPadding"; revision: 6; type: "double" }
        Property { name: "bottomPadding"; revision: 6; type: "double" }
        Property { name: "fontInfo"; revision: 9; type: "QJSValue"; isReadonly: true }
        Property { name: "advance"; revision: 10; type: "QSizeF"; isReadonly: true }
        Signal {
            name: "textChanged"
            Parameter { name: "text"; type: "string" }
        }
        Signal {
            name: "linkActivated"
            Parameter { name: "link"; type: "string" }
        }
        Signal {
            name: "linkHovered"
            revision: 2
            Parameter { name: "link"; type: "string" }
        }
        Signal {
            name: "fontChanged"
            Parameter { name: "font"; type: "QFont" }
        }
        Signal {
            name: "styleChanged"
            Parameter { name: "style"; type: "QQuickText::TextStyle" }
        }
        Signal {
            name: "horizontalAlignmentChanged"
            Parameter { name: "alignment"; type: "QQuickText::HAlignment" }
        }
        Signal {
            name: "verticalAlignmentChanged"
            Parameter { name: "alignment"; type: "QQuickText::VAlignment" }
        }
        Signal {
            name: "textFormatChanged"
            Parameter { name: "textFormat"; type: "QQuickText::TextFormat" }
        }
        Signal {
            name: "elideModeChanged"
            Parameter { name: "mode"; type: "QQuickText::TextElideMode" }
        }
        Signal { name: "contentSizeChanged" }
        Signal {
            name: "contentWidthChanged"
            Parameter { name: "contentWidth"; type: "double" }
        }
        Signal {
            name: "contentHeightChanged"
            Parameter { name: "contentHeight"; type: "double" }
        }
        Signal {
            name: "lineHeightChanged"
            Parameter { name: "lineHeight"; type: "double" }
        }
        Signal {
            name: "lineHeightModeChanged"
            Parameter { name: "mode"; type: "LineHeightMode" }
        }
        Signal {
            name: "lineLaidOut"
            Parameter { name: "line"; type: "QQuickTextLine"; isPointer: true }
        }
        Signal { name: "paddingChanged"; revision: 6 }
        Signal { name: "topPaddingChanged"; revision: 6 }
        Signal { name: "leftPaddingChanged"; revision: 6 }
        Signal { name: "rightPaddingChanged"; revision: 6 }
        Signal { name: "bottomPaddingChanged"; revision: 6 }
        Signal { name: "fontInfoChanged"; revision: 9 }
        Method { name: "q_updateLayout" }
        Method { name: "triggerPreprocess" }
        Method { name: "imageDownloadFinished" }
        Method { name: "doLayout" }
        Method { name: "forceLayout"; revision: 9 }
        Method {
            name: "linkAt"
            revision: 3
            type: "string"
            Parameter { name: "x"; type: "double" }
            Parameter { name: "y"; type: "double" }
        }
    }
    Component { file: "qquicktextdocument.h"; name: "QQuickTextDocument"; prototype: "QObject" }
    Component {
        file: "private/qquicktextedit_p.h"
        name: "QQuickTextEdit"
        prototype: "QQuickImplicitSizeItem"
        exports: [
            "QtQuick/TextEdit 2.0",
            "QtQuick/TextEdit 2.1",
            "QtQuick/TextEdit 2.10",
            "QtQuick/TextEdit 2.11",
            "QtQuick/TextEdit 2.2",
            "QtQuick/TextEdit 2.3",
            "QtQuick/TextEdit 2.4",
            "QtQuick/TextEdit 2.6",
            "QtQuick/TextEdit 2.7"
        ]
        exportMetaObjectRevisions: [0, 1, 10, 11, 2, 3, 4, 6, 7]
        Enum {
            name: "HAlignment"
            values: [
                "AlignLeft",
                "AlignRight",
                "AlignHCenter",
                "AlignJustify"
            ]
        }
        Enum {
            name: "VAlignment"
            values: ["AlignTop", "AlignBottom", "AlignVCenter"]
        }
        Enum {
            name: "TextFormat"
            values: ["PlainText", "RichText", "AutoText", "MarkdownText"]
        }
        Enum {
            name: "WrapMode"
            values: [
                "NoWrap",
                "WordWrap",
                "WrapAnywhere",
                "WrapAtWordBoundaryOrAnywhere",
                "Wrap"
            ]
        }
        Enum {
            name: "SelectionMode"
            values: ["SelectCharacters", "SelectWords"]
        }
        Enum {
            name: "RenderType"
            values: ["QtRendering", "NativeRendering"]
        }
        Property { name: "text"; type: "string" }
        Property { name: "color"; type: "QColor" }
        Property { name: "selectionColor"; type: "QColor" }
        Property { name: "selectedTextColor"; type: "QColor" }
        Property { name: "font"; type: "QFont" }
        Property { name: "horizontalAlignment"; type: "HAlignment" }
        Property { name: "effectiveHorizontalAlignment"; type: "HAlignment"; isReadonly: true }
        Property { name: "verticalAlignment"; type: "VAlignment" }
        Property { name: "wrapMode"; type: "WrapMode" }
        Property { name: "lineCount"; type: "int"; isReadonly: true }
        Property { name: "length"; type: "int"; isReadonly: true }
        Property { name: "contentWidth"; type: "double"; isReadonly: true }
        Property { name: "contentHeight"; type: "double"; isReadonly: true }
        Property { name: "paintedWidth"; type: "double"; isReadonly: true }
        Property { name: "paintedHeight"; type: "double"; isReadonly: true }
        Property { name: "textFormat"; type: "TextFormat" }
        Property { name: "readOnly"; type: "bool" }
        Property { name: "cursorVisible"; type: "bool" }
        Property { name: "cursorPosition"; type: "int" }
        Property { name: "cursorRectangle"; type: "QRectF"; isReadonly: true }
        Property { name: "cursorDelegate"; type: "QQmlComponent"; isPointer: true }
        Property { name: "overwriteMode"; type: "bool" }
        Property { name: "selectionStart"; type: "int"; isReadonly: true }
        Property { name: "selectionEnd"; type: "int"; isReadonly: true }
        Property { name: "selectedText"; type: "string"; isReadonly: true }
        Property { name: "activeFocusOnPress"; type: "bool" }
        Property { name: "persistentSelection"; type: "bool" }
        Property { name: "textMargin"; type: "double" }
        Property { name: "inputMethodHints"; type: "Qt::InputMethodHints" }
        Property { name: "selectByKeyboard"; revision: 1; type: "bool" }
        Property { name: "selectByMouse"; type: "bool" }
        Property { name: "mouseSelectionMode"; type: "SelectionMode" }
        Property { name: "canPaste"; type: "bool"; isReadonly: true }
        Property { name: "canUndo"; type: "bool"; isReadonly: true }
        Property { name: "canRedo"; type: "bool"; isReadonly: true }
        Property { name: "inputMethodComposing"; type: "bool"; isReadonly: true }
        Property { name: "baseUrl"; type: "QUrl" }
        Property { name: "renderType"; type: "RenderType" }
        Property {
            name: "textDocument"
            revision: 1
            type: "QQuickTextDocument"
            isReadonly: true
            isPointer: true
        }
        Property { name: "hoveredLink"; revision: 2; type: "string"; isReadonly: true }
        Property { name: "padding"; revision: 6; type: "double" }
        Property { name: "topPadding"; revision: 6; type: "double" }
        Property { name: "leftPadding"; revision: 6; type: "double" }
        Property { name: "rightPadding"; revision: 6; type: "double" }
        Property { name: "bottomPadding"; revision: 6; type: "double" }
        Property { name: "preeditText"; revision: 7; type: "string"; isReadonly: true }
        Property { name: "tabStopDistance"; revision: 10; type: "double" }
        Signal { name: "preeditTextChanged"; revision: 7 }
        Signal { name: "contentSizeChanged" }
        Signal {
            name: "colorChanged"
            Parameter { name: "color"; type: "QColor" }
        }
        Signal {
            name: "selectionColorChanged"
            Parameter { name: "color"; type: "QColor" }
        }
        Signal {
            name: "selectedTextColorChanged"
            Parameter { name: "color"; type: "QColor" }
        }
        Signal {
            name: "fontChanged"
            Parameter { name: "font"; type: "QFont" }
        }
        Signal {
            name: "horizontalAlignmentChanged"
            Parameter { name: "alignment"; type: "QQuickTextEdit::HAlignment" }
        }
        Signal {
            name: "verticalAlignmentChanged"
            Parameter { name: "alignment"; type: "QQuickTextEdit::VAlignment" }
        }
        Signal {
            name: "textFormatChanged"
            Parameter { name: "textFormat"; type: "QQuickTextEdit::TextFormat" }
        }
        Signal {
            name: "readOnlyChanged"
            Parameter { name: "isReadOnly"; type: "bool" }
        }
        Signal {
            name: "cursorVisibleChanged"
            Parameter { name: "isCursorVisible"; type: "bool" }
        }
        Signal {
            name: "overwriteModeChanged"
            Parameter { name: "overwriteMode"; type: "bool" }
        }
        Signal {
            name: "activeFocusOnPressChanged"
            Parameter { name: "activeFocusOnPressed"; type: "bool" }
        }
        Signal {
            name: "persistentSelectionChanged"
            Parameter { name: "isPersistentSelection"; type: "bool" }
        }
        Signal {
            name: "textMarginChanged"
            Parameter { name: "textMargin"; type: "double" }
        }
        Signal {
            name: "selectByKeyboardChanged"
            revision: 1
            Parameter { name: "selectByKeyboard"; type: "bool" }
        }
        Signal {
            name: "selectByMouseChanged"
            Parameter { name: "selectByMouse"; type: "bool" }
        }
        Signal {
            name: "mouseSelectionModeChanged"
            Parameter { name: "mode"; type: "QQuickTextEdit::SelectionMode" }
        }
        Signal {
            name: "linkActivated"
            Parameter { name: "link"; type: "string" }
        }
        Signal {
            name: "linkHovered"
            revision: 2
            Parameter { name: "link"; type: "string" }
        }
        Signal { name: "editingFinished"; revision: 6 }
        Signal { name: "paddingChanged"; revision: 6 }
        Signal { name: "topPaddingChanged"; revision: 6 }
        Signal { name: "leftPaddingChanged"; revision: 6 }
        Signal { name: "rightPaddingChanged"; revision: 6 }
        Signal { name: "bottomPaddingChanged"; revision: 6 }
        Signal {
            name: "tabStopDistanceChanged"
            revision: 10
            Parameter { name: "distance"; type: "double" }
        }
        Method { name: "selectAll" }
        Method { name: "selectWord" }
        Method {
            name: "select"
            Parameter { name: "start"; type: "int" }
            Parameter { name: "end"; type: "int" }
        }
        Method { name: "deselect" }
        Method {
            name: "isRightToLeft"
            type: "bool"
            Parameter { name: "start"; type: "int" }
            Parameter { name: "end"; type: "int" }
        }
        Method { name: "cut" }
        Method { name: "copy" }
        Method { name: "paste" }
        Method { name: "undo" }
        Method { name: "redo" }
        Method {
            name: "insert"
            Parameter { name: "position"; type: "int" }
            Parameter { name: "text"; type: "string" }
        }
        Method {
            name: "remove"
            Parameter { name: "start"; type: "int" }
            Parameter { name: "end"; type: "int" }
        }
        Method {
            name: "append"
            revision: 2
            Parameter { name: "text"; type: "string" }
        }
        Method { name: "clear"; revision: 7 }
        Method { name: "q_textChanged" }
        Method {
            name: "q_contentsChange"
            Parameter { type: "int" }
            Parameter { type: "int" }
            Parameter { type: "int" }
        }
        Method { name: "updateSelection" }
        Method { name: "moveCursorDelegate" }
        Method { name: "createCursor" }
        Method { name: "q_canPasteChanged" }
        Method { name: "updateWholeDocument" }
        Method {
            name: "invalidateBlock"
            Parameter { name: "block"; type: "QTextBlock" }
        }
        Method { name: "updateCursor" }
        Method {
            name: "q_linkHovered"
            Parameter { name: "link"; type: "string" }
        }
        Method {
            name: "q_markerHovered"
            Parameter { name: "hovered"; type: "bool" }
        }
        Method { name: "q_updateAlignment" }
        Method { name: "updateSize" }
        Method { name: "triggerPreprocess" }
        Method {
            name: "inputMethodQuery"
            revision: 4
            type: "QVariant"
            Parameter { name: "query"; type: "Qt::InputMethodQuery" }
            Parameter { name: "argument"; type: "QVariant" }
        }
        Method {
            name: "positionToRectangle"
            type: "QRectF"
            Parameter { type: "int" }
        }
        Method {
            name: "positionAt"
            type: "int"
            Parameter { name: "x"; type: "double" }
            Parameter { name: "y"; type: "double" }
        }
        Method {
            name: "moveCursorSelection"
            Parameter { name: "pos"; type: "int" }
        }
        Method {
            name: "moveCursorSelection"
            Parameter { name: "pos"; type: "int" }
            Parameter { name: "mode"; type: "SelectionMode" }
        }
        Method {
            name: "getText"
            type: "string"
            Parameter { name: "start"; type: "int" }
            Parameter { name: "end"; type: "int" }
        }
        Method {
            name: "getFormattedText"
            type: "string"
            Parameter { name: "start"; type: "int" }
            Parameter { name: "end"; type: "int" }
        }
        Method {
            name: "linkAt"
            revision: 3
            type: "string"
            Parameter { name: "x"; type: "double" }
            Parameter { name: "y"; type: "double" }
        }
    }
    Component {
        file: "private/qquicktextinput_p.h"
        name: "QQuickTextInput"
        prototype: "QQuickImplicitSizeItem"
        exports: [
            "QtQuick/TextInput 2.0",
            "QtQuick/TextInput 2.1",
            "QtQuick/TextInput 2.11",
            "QtQuick/TextInput 2.2",
            "QtQuick/TextInput 2.4",
            "QtQuick/TextInput 2.6",
            "QtQuick/TextInput 2.7",
            "QtQuick/TextInput 2.9"
        ]
        exportMetaObjectRevisions: [0, 1, 11, 2, 4, 6, 7, 9]
        Enum {
            name: "EchoMode"
            values: ["Normal", "NoEcho", "Password", "PasswordEchoOnEdit"]
        }
        Enum {
            name: "HAlignment"
            values: ["AlignLeft", "AlignRight", "AlignHCenter"]
        }
        Enum {
            name: "VAlignment"
            values: ["AlignTop", "AlignBottom", "AlignVCenter"]
        }
        Enum {
            name: "WrapMode"
            values: [
                "NoWrap",
                "WordWrap",
                "WrapAnywhere",
                "WrapAtWordBoundaryOrAnywhere",
                "Wrap"
            ]
        }
        Enum {
            name: "SelectionMode"
            values: ["SelectCharacters", "SelectWords"]
        }
        Enum {
            name: "CursorPosition"
            values: ["CursorBetweenCharacters", "CursorOnCharacter"]
        }
        Enum {
            name: "RenderType"
            values: ["QtRendering", "NativeRendering"]
        }
        Property { name: "text"; type: "string" }
        Property { name: "length"; type: "int"; isReadonly: true }
        Property { name: "color"; type: "QColor" }
        Property { name: "selectionColor"; type: "QColor" }
        Property { name: "selectedTextColor"; type: "QColor" }
        Property { name: "font"; type: "QFont" }
        Property { name: "horizontalAlignment"; type: "HAlignment" }
        Property { name: "effectiveHorizontalAlignment"; type: "HAlignment"; isReadonly: true }
        Property { name: "verticalAlignment"; type: "VAlignment" }
        Property { name: "wrapMode"; type: "WrapMode" }
        Property { name: "readOnly"; type: "bool" }
        Property { name: "cursorVisible"; type: "bool" }
        Property { name: "cursorPosition"; type: "int" }
        Property { name: "cursorRectangle"; type: "QRectF"; isReadonly: true }
        Property { name: "cursorDelegate"; type: "QQmlComponent"; isPointer: true }
        Property { name: "overwriteMode"; type: "bool" }
        Property { name: "selectionStart"; type: "int"; isReadonly: true }
        Property { name: "selectionEnd"; type: "int"; isReadonly: true }
        Property { name: "selectedText"; type: "string"; isReadonly: true }
        Property { name: "maximumLength"; type: "int" }
        Property { name: "validator"; type: "QValidator"; isPointer: true }
        Property { name: "inputMask"; type: "string" }
        Property { name: "inputMethodHints"; type: "Qt::InputMethodHints" }
        Property { name: "acceptableInput"; type: "bool"; isReadonly: true }
        Property { name: "echoMode"; type: "EchoMode" }
        Property { name: "activeFocusOnPress"; type: "bool" }
        Property { name: "passwordCharacter"; type: "string" }
        Property { name: "passwordMaskDelay"; revision: 4; type: "int" }
        Property { name: "displayText"; type: "string"; isReadonly: true }
        Property { name: "preeditText"; revision: 7; type: "string"; isReadonly: true }
        Property { name: "autoScroll"; type: "bool" }
        Property { name: "selectByMouse"; type: "bool" }
        Property { name: "mouseSelectionMode"; type: "SelectionMode" }
        Property { name: "persistentSelection"; type: "bool" }
        Property { name: "canPaste"; type: "bool"; isReadonly: true }
        Property { name: "canUndo"; type: "bool"; isReadonly: true }
        Property { name: "canRedo"; type: "bool"; isReadonly: true }
        Property { name: "inputMethodComposing"; type: "bool"; isReadonly: true }
        Property { name: "contentWidth"; type: "double"; isReadonly: true }
        Property { name: "contentHeight"; type: "double"; isReadonly: true }
        Property { name: "renderType"; type: "RenderType" }
        Property { name: "padding"; revision: 6; type: "double" }
        Property { name: "topPadding"; revision: 6; type: "double" }
        Property { name: "leftPadding"; revision: 6; type: "double" }
        Property { name: "rightPadding"; revision: 6; type: "double" }
        Property { name: "bottomPadding"; revision: 6; type: "double" }
        Signal { name: "accepted" }
        Signal { name: "editingFinished"; revision: 2 }
        Signal { name: "textEdited"; revision: 9 }
        Signal {
            name: "fontChanged"
            Parameter { name: "font"; type: "QFont" }
        }
        Signal {
            name: "horizontalAlignmentChanged"
            Parameter { name: "alignment"; type: "QQuickTextInput::HAlignment" }
        }
        Signal {
            name: "verticalAlignmentChanged"
            Parameter { name: "alignment"; type: "QQuickTextInput::VAlignment" }
        }
        Signal {
            name: "readOnlyChanged"
            Parameter { name: "isReadOnly"; type: "bool" }
        }
        Signal {
            name: "cursorVisibleChanged"
            Parameter { name: "isCursorVisible"; type: "bool" }
        }
        Signal {
            name: "overwriteModeChanged"
            Parameter { name: "overwriteMode"; type: "bool" }
        }
        Signal {
            name: "maximumLengthChanged"
            Parameter { name: "maximumLength"; type: "int" }
        }
        Signal {
            name: "inputMaskChanged"
            Parameter { name: "inputMask"; type: "string" }
        }
        Signal {
            name: "echoModeChanged"
            Parameter { name: "echoMode"; type: "QQuickTextInput::EchoMode" }
        }
        Signal {
            name: "passwordMaskDelayChanged"
            revision: 4
            Parameter { name: "delay"; type: "int" }
        }
        Signal { name: "preeditTextChanged"; revision: 7 }
        Signal {
            name: "activeFocusOnPressChanged"
            Parameter { name: "activeFocusOnPress"; type: "bool" }
        }
        Signal {
            name: "autoScrollChanged"
            Parameter { name: "autoScroll"; type: "bool" }
        }
        Signal {
            name: "selectByMouseChanged"
            Parameter { name: "selectByMouse"; type: "bool" }
        }
        Signal {
            name: "mouseSelectionModeChanged"
            Parameter { name: "mode"; type: "QQuickTextInput::SelectionMode" }
        }
        Signal { name: "contentSizeChanged" }
        Signal { name: "paddingChanged"; revision: 6 }
        Signal { name: "topPaddingChanged"; revision: 6 }
        Signal { name: "leftPaddingChanged"; revision: 6 }
        Signal { name: "rightPaddingChanged"; revision: 6 }
        Signal { name: "bottomPaddingChanged"; revision: 6 }
        Method { name: "selectAll" }
        Method { name: "selectWord" }
        Method {
            name: "select"
            Parameter { name: "start"; type: "int" }
            Parameter { name: "end"; type: "int" }
        }
        Method { name: "deselect" }
        Method {
            name: "isRightToLeft"
            type: "bool"
            Parameter { name: "start"; type: "int" }
            Parameter { name: "end"; type: "int" }
        }
        Method { name: "cut" }
        Method { name: "copy" }
        Method { name: "paste" }
        Method { name: "undo" }
        Method { name: "redo" }
        Method {
            name: "insert"
            Parameter { name: "position"; type: "int" }
            Parameter { name: "text"; type: "string" }
        }
        Method {
            name: "remove"
            Parameter { name: "start"; type: "int" }
            Parameter { name: "end"; type: "int" }
        }
        Method {
            name: "ensureVisible"
            revision: 4
            Parameter { name: "position"; type: "int" }
        }
        Method { name: "clear"; revision: 7 }
        Method { name: "selectionChanged" }
        Method { name: "createCursor" }
        Method {
            name: "updateCursorRectangle"
            Parameter { name: "scroll"; type: "bool" }
        }
        Method { name: "updateCursorRectangle" }
        Method { name: "q_canPasteChanged" }
        Method { name: "q_updateAlignment" }
        Method { name: "triggerPreprocess" }
        Method { name: "q_validatorChanged" }
        Method {
            name: "positionAt"
            Parameter { name: "args"; type: "QQmlV4Function"; isPointer: true }
        }
        Method {
            name: "positionToRectangle"
            type: "QRectF"
            Parameter { name: "pos"; type: "int" }
        }
        Method {
            name: "moveCursorSelection"
            Parameter { name: "pos"; type: "int" }
        }
        Method {
            name: "moveCursorSelection"
            Parameter { name: "pos"; type: "int" }
            Parameter { name: "mode"; type: "SelectionMode" }
        }
        Method {
            name: "inputMethodQuery"
            revision: 4
            type: "QVariant"
            Parameter { name: "query"; type: "Qt::InputMethodQuery" }
            Parameter { name: "argument"; type: "QVariant" }
        }
        Method {
            name: "getText"
            type: "string"
            Parameter { name: "start"; type: "int" }
            Parameter { name: "end"; type: "int" }
        }
    }
    Component {
        file: "private/qquicktext_p.h"
        name: "QQuickTextLine"
        prototype: "QObject"
        Property { name: "number"; type: "int"; isReadonly: true }
        Property { name: "width"; type: "double" }
        Property { name: "height"; type: "double" }
        Property { name: "x"; type: "double" }
        Property { name: "y"; type: "double" }
        Property { name: "implicitWidth"; revision: 15; type: "double"; isReadonly: true }
        Property { name: "isLast"; revision: 15; type: "bool"; isReadonly: true }
    }
    Component {
        file: "private/qquicktextmetrics_p.h"
        name: "QQuickTextMetrics"
        prototype: "QObject"
        exports: ["QtQuick/TextMetrics 2.4"]
        exportMetaObjectRevisions: [4]
        Property { name: "font"; type: "QFont" }
        Property { name: "text"; type: "string" }
        Property { name: "advanceWidth"; type: "double"; isReadonly: true }
        Property { name: "boundingRect"; type: "QRectF"; isReadonly: true }
        Property { name: "width"; type: "double"; isReadonly: true }
        Property { name: "height"; type: "double"; isReadonly: true }
        Property { name: "tightBoundingRect"; type: "QRectF"; isReadonly: true }
        Property { name: "elidedText"; type: "string"; isReadonly: true }
        Property { name: "elide"; type: "Qt::TextElideMode" }
        Property { name: "elideWidth"; type: "double" }
        Signal { name: "metricsChanged" }
    }
    Component {
        file: "private/qquickmultipointtoucharea_p.h"
        name: "QQuickTouchPoint"
        prototype: "QObject"
        exports: ["QtQuick/TouchPoint 2.0", "QtQuick/TouchPoint 2.9"]
        exportMetaObjectRevisions: [0, 9]
        Property { name: "pointId"; type: "int"; isReadonly: true }
        Property { name: "uniqueId"; revision: 9; type: "QPointingDeviceUniqueId"; isReadonly: true }
        Property { name: "pressed"; type: "bool"; isReadonly: true }
        Property { name: "x"; type: "double"; isReadonly: true }
        Property { name: "y"; type: "double"; isReadonly: true }
        Property { name: "ellipseDiameters"; revision: 9; type: "QSizeF"; isReadonly: true }
        Property { name: "pressure"; type: "double"; isReadonly: true }
        Property { name: "rotation"; revision: 9; type: "double"; isReadonly: true }
        Property { name: "velocity"; type: "QVector2D"; isReadonly: true }
        Property { name: "area"; type: "QRectF"; isReadonly: true }
        Property { name: "startX"; type: "double"; isReadonly: true }
        Property { name: "startY"; type: "double"; isReadonly: true }
        Property { name: "previousX"; type: "double"; isReadonly: true }
        Property { name: "previousY"; type: "double"; isReadonly: true }
        Property { name: "sceneX"; type: "double"; isReadonly: true }
        Property { name: "sceneY"; type: "double"; isReadonly: true }
        Signal { name: "uniqueIdChanged"; revision: 9 }
        Signal { name: "ellipseDiametersChanged"; revision: 9 }
        Signal { name: "rotationChanged"; revision: 9 }
    }
    Component {
        file: "qquickitem.h"
        name: "QQuickTransform"
        prototype: "QObject"
        Method { name: "update" }
    }
    Component {
        file: "private/qquicktransition_p.h"
        name: "QQuickTransition"
        defaultProperty: "animations"
        prototype: "QObject"
        exports: ["QtQuick/Transition 2.0"]
        exportMetaObjectRevisions: [0]
        Property { name: "from"; type: "string" }
        Property { name: "to"; type: "string" }
        Property { name: "reversible"; type: "bool" }
        Property { name: "running"; type: "bool"; isReadonly: true }
        Property { name: "animations"; type: "QQuickAbstractAnimation"; isList: true; isReadonly: true }
        Property { name: "enabled"; type: "bool" }
    }
    Component {
        file: "private/qquicktranslate_p.h"
        name: "QQuickTranslate"
        prototype: "QQuickTransform"
        exports: ["QtQuick/Translate 2.0"]
        exportMetaObjectRevisions: [0]
        Property { name: "x"; type: "double" }
        Property { name: "y"; type: "double" }
    }
    Component {
        file: "private/qquickanimator_p.h"
        name: "QQuickUniformAnimator"
        prototype: "QQuickAnimator"
        exports: [
            "QtQuick/UniformAnimator 2.12",
            "QtQuick/UniformAnimator 2.2"
        ]
        exportMetaObjectRevisions: [12, 2]
        Property { name: "uniform"; type: "string" }
        Signal {
            name: "uniformChanged"
            Parameter { type: "string" }
        }
    }
    Component {
        file: "private/qquickanimation_p.h"
        name: "QQuickVector3dAnimation"
        prototype: "QQuickPropertyAnimation"
        exports: [
            "QtQuick/Vector3dAnimation 2.0",
            "QtQuick/Vector3dAnimation 2.12"
        ]
        exportMetaObjectRevisions: [0, 12]
        Property { name: "from"; type: "QVector3D" }
        Property { name: "to"; type: "QVector3D" }
    }
    Component {
        file: "private/qquicklistview_p.h"
        name: "QQuickViewSection"
        prototype: "QObject"
        exports: ["QtQuick/ViewSection 2.0"]
        exportMetaObjectRevisions: [0]
        Enum {
            name: "SectionCriteria"
            values: ["FullString", "FirstCharacter"]
        }
        Enum {
            name: "LabelPositioning"
            values: ["InlineLabels", "CurrentLabelAtStart", "NextLabelAtEnd"]
        }
        Property { name: "property"; type: "string" }
        Property { name: "criteria"; type: "SectionCriteria" }
        Property { name: "delegate"; type: "QQmlComponent"; isPointer: true }
        Property { name: "labelPositioning"; type: "int" }
        Signal { name: "sectionsChanged" }
    }
    Component {
        file: "private/qquickitemviewtransition_p.h"
        name: "QQuickViewTransitionAttached"
        prototype: "QObject"
        exports: ["QtQuick/ViewTransition 2.0"]
        isCreatable: false
        exportMetaObjectRevisions: [0]
        attachedType: "QQuickViewTransitionAttached"
        Property { name: "index"; type: "int"; isReadonly: true }
        Property { name: "item"; type: "QQuickItem"; isReadonly: true; isPointer: true }
        Property { name: "destination"; type: "QPointF"; isReadonly: true }
        Property { name: "targetIndexes"; type: "QList<int>"; isReadonly: true }
        Property { name: "targetItems"; type: "QObject"; isList: true; isReadonly: true }
    }
    Component {
        file: "private/qquickevents_p_p.h"
        name: "QQuickWheelEvent"
        prototype: "QObject"
        Property { name: "x"; type: "double"; isReadonly: true }
        Property { name: "y"; type: "double"; isReadonly: true }
        Property { name: "angleDelta"; type: "QPoint"; isReadonly: true }
        Property { name: "pixelDelta"; type: "QPoint"; isReadonly: true }
        Property { name: "buttons"; type: "int"; isReadonly: true }
        Property { name: "modifiers"; type: "int"; isReadonly: true }
        Property { name: "inverted"; type: "bool"; isReadonly: true }
        Property { name: "accepted"; type: "bool" }
    }
    Component {
        file: "private/qquickwheelhandler_p.h"
        name: "QQuickWheelHandler"
        prototype: "QQuickSinglePointHandler"
        exports: ["QtQuick/WheelHandler 2.14", "QtQuick/WheelHandler 2.15"]
        exportMetaObjectRevisions: [14, 15]
        Property { name: "orientation"; type: "Qt::Orientation" }
        Property { name: "invertible"; type: "bool" }
        Property { name: "activeTimeout"; type: "double" }
        Property { name: "rotation"; type: "double" }
        Property { name: "rotationScale"; type: "double" }
        Property { name: "property"; type: "string" }
        Property { name: "targetScaleMultiplier"; type: "double" }
        Property { name: "targetTransformAroundCursor"; type: "bool" }
        Signal {
            name: "wheel"
            Parameter { name: "event"; type: "QQuickPointerScrollEvent"; isPointer: true }
        }
    }
    Component {
        file: "private/qquickanimator_p.h"
        name: "QQuickXAnimator"
        prototype: "QQuickAnimator"
        exports: ["QtQuick/XAnimator 2.12", "QtQuick/XAnimator 2.2"]
        exportMetaObjectRevisions: [12, 2]
    }
    Component {
        file: "private/qquickanimator_p.h"
        name: "QQuickYAnimator"
        prototype: "QQuickAnimator"
        exports: ["QtQuick/YAnimator 2.12", "QtQuick/YAnimator 2.2"]
        exportMetaObjectRevisions: [12, 2]
    }
    Component {
        file: "private/qquickforeignutils_p.h"
        name: "QRegExpValidator"
        prototype: "QValidator"
        exports: ["QtQuick/RegExpValidator 2.0"]
        exportMetaObjectRevisions: [0]
        Property { name: "regExp"; type: "QRegExp" }
        Signal {
            name: "regExpChanged"
            Parameter { name: "regExp"; type: "QRegExp" }
        }
    }
    Component {
        file: "private/qquickforeignutils_p.h"
        name: "QRegularExpressionValidator"
        prototype: "QValidator"
        exports: ["QtQuick/RegularExpressionValidator 2.14"]
        exportMetaObjectRevisions: [14]
        Property { name: "regularExpression"; type: "QRegularExpression" }
        Signal {
            name: "regularExpressionChanged"
            Parameter { name: "re"; type: "QRegularExpression" }
        }
        Method {
            name: "setRegularExpression"
            Parameter { name: "re"; type: "QRegularExpression" }
        }
    }
    Component {
        file: "private/qquickforeignutils_p.h"
        name: "QValidator"
        prototype: "QObject"
        Enum {
            name: "State"
            values: ["Invalid", "Intermediate", "Acceptable"]
        }
        Signal { name: "changed" }
    }
}
