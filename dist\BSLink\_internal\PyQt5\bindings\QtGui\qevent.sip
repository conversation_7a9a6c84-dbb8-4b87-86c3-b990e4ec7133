// qevent.sip generated by MetaSIP
//
// This file is part of the QtGui Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QInputEvent : public QEvent /NoDefaultCtors/
{
%TypeHeaderCode
#include <qevent.h>
%End

%ConvertToSubClassCode
    switch (sipCpp->type())
    {
    case QEvent::ActionAdded:
    case QEvent::ActionChanged:
    case QEvent::ActionRemoved:
        sipType = sipType_QActionEvent;
        break;
    
    case QEvent::Close:
        sipType = sipType_QCloseEvent;
        break;
    
    case QEvent::ContextMenu:
        sipType = sipType_QContextMenuEvent;
        break;
    
    case QEvent::DragEnter:
        sipType = sipType_QDragEnterEvent;
        break;
    
    case QEvent::DragLeave:
        sipType = sipType_QDragLeaveEvent;
        break;
    
    case QEvent::DragMove:
        sipType = sipType_QDragMoveEvent;
        break;
    
    case QEvent::Drop:
        sipType = sipType_QDropEvent;
        break;
    
    case QEvent::Enter:
        sipType = sipType_QEnterEvent;
        break;
    
    case QEvent::FileOpen:
        sipType = sipType_QFileOpenEvent;
        break;
    
    case QEvent::FocusIn:
    case QEvent::FocusOut:
        sipType = sipType_QFocusEvent;
        break;
    
    case QEvent::Hide:
        sipType = sipType_QHideEvent;
        break;
    
    case QEvent::HoverEnter:
    case QEvent::HoverLeave:
    case QEvent::HoverMove:
        sipType = sipType_QHoverEvent;
        break;
    
    case QEvent::IconDrag:
        sipType = sipType_QIconDragEvent;
        break;
    
    case QEvent::InputMethod:
        sipType = sipType_QInputMethodEvent;
        break;
    
    case QEvent::KeyPress:
    case QEvent::KeyRelease:
    case QEvent::ShortcutOverride:
        sipType = sipType_QKeyEvent;
        break;
    
    case QEvent::MouseButtonDblClick:
    case QEvent::MouseButtonPress:
    case QEvent::MouseButtonRelease:
    case QEvent::MouseMove:
        sipType = sipType_QMouseEvent;
        break;
    
    case QEvent::Move:
        sipType = sipType_QMoveEvent;
        break;
    
    case QEvent::Paint:
        sipType = sipType_QPaintEvent;
        break;
    
    case QEvent::Resize:
        sipType = sipType_QResizeEvent;
        break;
    
    case QEvent::Shortcut:
        sipType = sipType_QShortcutEvent;
        break;
    
    case QEvent::Show:
        sipType = sipType_QShowEvent;
        break;
    
    case QEvent::StatusTip:
        sipType = sipType_QStatusTipEvent;
        break;
    
    case QEvent::TabletMove:
    case QEvent::TabletPress:
    case QEvent::TabletRelease:
    case QEvent::TabletEnterProximity:
    case QEvent::TabletLeaveProximity:
        sipType = sipType_QTabletEvent;
        break;
    
    case QEvent::ToolTip:
    case QEvent::WhatsThis:
        sipType = sipType_QHelpEvent;
        break;
    
    case QEvent::WhatsThisClicked:
        sipType = sipType_QWhatsThisClickedEvent;
        break;
    
    case QEvent::Wheel:
        sipType = sipType_QWheelEvent;
        break;
    
    case QEvent::WindowStateChange:
        sipType = sipType_QWindowStateChangeEvent;
        break;
    
    case QEvent::TouchBegin:
    case QEvent::TouchUpdate:
    case QEvent::TouchEnd:
    case QEvent::TouchCancel:
        sipType = sipType_QTouchEvent;
        break;
    
    case QEvent::InputMethodQuery:
        sipType = sipType_QInputMethodQueryEvent;
        break;
    
    case QEvent::Expose:
        sipType = sipType_QExposeEvent;
        break;
    
    case QEvent::ScrollPrepare:
        sipType = sipType_QScrollPrepareEvent;
        break;
    
    case QEvent::Scroll:
        sipType = sipType_QScrollEvent;
        break;
    
    #if QT_VERSION >= 0x050200
    case QEvent::NativeGesture:
        sipType = sipType_QNativeGestureEvent;
        break;
    #endif
    
    #if QT_VERSION >= 0x050500
    case QEvent::PlatformSurface:
        sipType = sipType_QPlatformSurfaceEvent;
        break;
    #endif
    
    default:
        sipType = 0;
    }
%End

public:
    virtual ~QInputEvent();
    Qt::KeyboardModifiers modifiers() const;
    ulong timestamp() const;
    void setTimestamp(ulong atimestamp);
};

class QMouseEvent : public QInputEvent
{
%TypeHeaderCode
#include <qevent.h>
%End

public:
    QMouseEvent(QEvent::Type type, const QPointF &pos, Qt::MouseButton button, Qt::MouseButtons buttons, Qt::KeyboardModifiers modifiers);
    QMouseEvent(QEvent::Type type, const QPointF &pos, const QPointF &globalPos, Qt::MouseButton button, Qt::MouseButtons buttons, Qt::KeyboardModifiers modifiers);
    QMouseEvent(QEvent::Type type, const QPointF &pos, const QPointF &windowPos, const QPointF &globalPos, Qt::MouseButton button, Qt::MouseButtons buttons, Qt::KeyboardModifiers modifiers);
%If (Qt_5_6_0 -)
    QMouseEvent(QEvent::Type type, const QPointF &localPos, const QPointF &windowPos, const QPointF &screenPos, Qt::MouseButton button, Qt::MouseButtons buttons, Qt::KeyboardModifiers modifiers, Qt::MouseEventSource source);
%End
    virtual ~QMouseEvent();
    QPoint pos() const;
    QPoint globalPos() const;
    int x() const;
    int y() const;
    int globalX() const;
    int globalY() const;
    Qt::MouseButton button() const;
    Qt::MouseButtons buttons() const;
    const QPointF &localPos() const;
    const QPointF &windowPos() const;
    const QPointF &screenPos() const;
%If (Qt_5_3_0 -)
    Qt::MouseEventSource source() const;
%End
%If (Qt_5_3_0 -)
    Qt::MouseEventFlags flags() const;
%End
};

class QHoverEvent : public QInputEvent
{
%TypeHeaderCode
#include <qevent.h>
%End

public:
    QHoverEvent(QEvent::Type type, const QPointF &pos, const QPointF &oldPos, Qt::KeyboardModifiers modifiers = Qt::NoModifier);
    virtual ~QHoverEvent();
    QPoint pos() const;
    QPoint oldPos() const;
    const QPointF &posF() const;
    const QPointF &oldPosF() const;
};

class QWheelEvent : public QInputEvent
{
%TypeHeaderCode
#include <qevent.h>
%End

public:
    QWheelEvent(const QPointF &pos, const QPointF &globalPos, QPoint pixelDelta, QPoint angleDelta, int qt4Delta, Qt::Orientation qt4Orientation, Qt::MouseButtons buttons, Qt::KeyboardModifiers modifiers);
%If (Qt_5_2_0 -)
    QWheelEvent(const QPointF &pos, const QPointF &globalPos, QPoint pixelDelta, QPoint angleDelta, int qt4Delta, Qt::Orientation qt4Orientation, Qt::MouseButtons buttons, Qt::KeyboardModifiers modifiers, Qt::ScrollPhase phase);
%End
%If (Qt_5_5_0 -)
    QWheelEvent(const QPointF &pos, const QPointF &globalPos, QPoint pixelDelta, QPoint angleDelta, int qt4Delta, Qt::Orientation qt4Orientation, Qt::MouseButtons buttons, Qt::KeyboardModifiers modifiers, Qt::ScrollPhase phase, Qt::MouseEventSource source);
%End
%If (Qt_5_7_0 -)
    QWheelEvent(const QPointF &pos, const QPointF &globalPos, QPoint pixelDelta, QPoint angleDelta, int qt4Delta, Qt::Orientation qt4Orientation, Qt::MouseButtons buttons, Qt::KeyboardModifiers modifiers, Qt::ScrollPhase phase, Qt::MouseEventSource source, bool inverted);
%End
%If (Qt_5_12_0 -)
    QWheelEvent(QPointF pos, QPointF globalPos, QPoint pixelDelta, QPoint angleDelta, Qt::MouseButtons buttons, Qt::KeyboardModifiers modifiers, Qt::ScrollPhase phase, bool inverted, Qt::MouseEventSource source = Qt::MouseEventNotSynthesized);
%End
    virtual ~QWheelEvent();
    QPoint pos() const;
    QPoint globalPos() const;
    int x() const;
    int y() const;
    int globalX() const;
    int globalY() const;
    Qt::MouseButtons buttons() const;
    QPoint pixelDelta() const;
    QPoint angleDelta() const;
    const QPointF &posF() const;
    const QPointF &globalPosF() const;
%If (Qt_5_2_0 -)
    Qt::ScrollPhase phase() const;
%End
%If (Qt_5_5_0 -)
    Qt::MouseEventSource source() const;
%End
%If (Qt_5_7_0 -)
    bool inverted() const;
%End
%If (Qt_5_14_0 -)
    QPointF position() const;
%End
%If (Qt_5_14_0 -)
    QPointF globalPosition() const;
%End
};

class QTabletEvent : public QInputEvent
{
%TypeHeaderCode
#include <qevent.h>
%End

public:
    enum TabletDevice
    {
        NoDevice,
        Puck,
        Stylus,
        Airbrush,
        FourDMouse,
        XFreeEraser,
        RotationStylus,
    };

    enum PointerType
    {
        UnknownPointer,
        Pen,
        Cursor,
        Eraser,
    };

%If (Qt_5_4_0 -)
    QTabletEvent(QEvent::Type t, const QPointF &pos, const QPointF &globalPos, int device, int pointerType, qreal pressure, int xTilt, int yTilt, qreal tangentialPressure, qreal rotation, int z, Qt::KeyboardModifiers keyState, qint64 uniqueID, Qt::MouseButton button, Qt::MouseButtons buttons);
%End
    QTabletEvent(QEvent::Type t, const QPointF &pos, const QPointF &globalPos, int device, int pointerType, qreal pressure, int xTilt, int yTilt, qreal tangentialPressure, qreal rotation, int z, Qt::KeyboardModifiers keyState, qint64 uniqueID);
    virtual ~QTabletEvent();
    QPoint pos() const;
    QPoint globalPos() const;
    int x() const;
    int y() const;
    int globalX() const;
    int globalY() const;
    qreal hiResGlobalX() const;
    qreal hiResGlobalY() const;
    QTabletEvent::TabletDevice device() const;
    QTabletEvent::PointerType pointerType() const;
    qint64 uniqueId() const;
    qreal pressure() const;
    int z() const;
    qreal tangentialPressure() const;
    qreal rotation() const;
    int xTilt() const;
    int yTilt() const;
    const QPointF &posF() const;
    const QPointF &globalPosF() const;
%If (Qt_5_4_0 -)
    Qt::MouseButton button() const;
%End
%If (Qt_5_4_0 -)
    Qt::MouseButtons buttons() const;
%End
%If (Qt_5_15_0 -)
    QTabletEvent::TabletDevice deviceType() const;
%End
};

class QKeyEvent : public QInputEvent
{
%TypeHeaderCode
#include <qevent.h>
%End

public:
    QKeyEvent(QEvent::Type type, int key, Qt::KeyboardModifiers modifiers, quint32 nativeScanCode, quint32 nativeVirtualKey, quint32 nativeModifiers, const QString &text = QString(), bool autorep = false, ushort count = 1);
    QKeyEvent(QEvent::Type type, int key, Qt::KeyboardModifiers modifiers, const QString &text = QString(), bool autorep = false, ushort count = 1);
    virtual ~QKeyEvent();
    int key() const;
    Qt::KeyboardModifiers modifiers() const;
    QString text() const;
    bool isAutoRepeat() const;
    int count() const /__len__/;
    bool matches(QKeySequence::StandardKey key) const;
    quint32 nativeModifiers() const;
    quint32 nativeScanCode() const;
    quint32 nativeVirtualKey() const;
};

class QFocusEvent : public QEvent
{
%TypeHeaderCode
#include <qevent.h>
%End

public:
    QFocusEvent(QEvent::Type type, Qt::FocusReason reason = Qt::OtherFocusReason);
    virtual ~QFocusEvent();
    bool gotFocus() const;
    bool lostFocus() const;
    Qt::FocusReason reason() const;
};

class QPaintEvent : public QEvent
{
%TypeHeaderCode
#include <qevent.h>
%End

public:
    explicit QPaintEvent(const QRegion &paintRegion);
    explicit QPaintEvent(const QRect &paintRect);
    virtual ~QPaintEvent();
    const QRect &rect() const;
    const QRegion &region() const;
};

class QMoveEvent : public QEvent
{
%TypeHeaderCode
#include <qevent.h>
%End

public:
    QMoveEvent(const QPoint &pos, const QPoint &oldPos);
    virtual ~QMoveEvent();
    const QPoint &pos() const;
    const QPoint &oldPos() const;
};

class QResizeEvent : public QEvent
{
%TypeHeaderCode
#include <qevent.h>
%End

public:
    QResizeEvent(const QSize &size, const QSize &oldSize);
    virtual ~QResizeEvent();
    const QSize &size() const;
    const QSize &oldSize() const;
};

class QCloseEvent : public QEvent
{
%TypeHeaderCode
#include <qevent.h>
%End

public:
    QCloseEvent();
    virtual ~QCloseEvent();
};

class QIconDragEvent : public QEvent
{
%TypeHeaderCode
#include <qevent.h>
%End

public:
    QIconDragEvent();
    virtual ~QIconDragEvent();
};

class QShowEvent : public QEvent
{
%TypeHeaderCode
#include <qevent.h>
%End

public:
    QShowEvent();
    virtual ~QShowEvent();
};

class QHideEvent : public QEvent
{
%TypeHeaderCode
#include <qevent.h>
%End

public:
    QHideEvent();
    virtual ~QHideEvent();
};

class QContextMenuEvent : public QInputEvent
{
%TypeHeaderCode
#include <qevent.h>
%End

public:
    enum Reason
    {
        Mouse,
        Keyboard,
        Other,
    };

    QContextMenuEvent(QContextMenuEvent::Reason reason, const QPoint &pos, const QPoint &globalPos, Qt::KeyboardModifiers modifiers);
    QContextMenuEvent(QContextMenuEvent::Reason reason, const QPoint &pos, const QPoint &globalPos);
    QContextMenuEvent(QContextMenuEvent::Reason reason, const QPoint &pos);
    virtual ~QContextMenuEvent();
    int x() const;
    int y() const;
    int globalX() const;
    int globalY() const;
    const QPoint &pos() const;
    const QPoint &globalPos() const;
    QContextMenuEvent::Reason reason() const;
};

class QInputMethodEvent : public QEvent
{
%TypeHeaderCode
#include <qevent.h>
%End

public:
    enum AttributeType
    {
        TextFormat,
        Cursor,
        Language,
        Ruby,
        Selection,
    };

    class Attribute
    {
%TypeHeaderCode
#include <qevent.h>
%End

    public:
        Attribute(QInputMethodEvent::AttributeType t, int s, int l, QVariant val);
%If (Qt_5_8_0 -)
        Attribute(QInputMethodEvent::AttributeType typ, int s, int l);
%End
        QInputMethodEvent::AttributeType type;
        int start;
        int length;
        QVariant value;
    };

    QInputMethodEvent();
    QInputMethodEvent(const QString &preeditText, const QList<QInputMethodEvent::Attribute> &attributes);
    QInputMethodEvent(const QInputMethodEvent &other);
%If (Qt_5_6_0 -)
    virtual ~QInputMethodEvent();
%End
    void setCommitString(const QString &commitString, int from = 0, int length = 0);
    const QList<QInputMethodEvent::Attribute> &attributes() const;
    const QString &preeditString() const;
    const QString &commitString() const;
    int replacementStart() const;
    int replacementLength() const;
};

class QInputMethodQueryEvent : public QEvent
{
%TypeHeaderCode
#include <qevent.h>
%End

public:
    explicit QInputMethodQueryEvent(Qt::InputMethodQueries queries);
    virtual ~QInputMethodQueryEvent();
    Qt::InputMethodQueries queries() const;
    void setValue(Qt::InputMethodQuery query, const QVariant &value);
    QVariant value(Qt::InputMethodQuery query) const;
};

class QDropEvent : public QEvent
{
%TypeHeaderCode
#include <qevent.h>
%End

public:
    QDropEvent(const QPointF &pos, Qt::DropActions actions, const QMimeData *data, Qt::MouseButtons buttons, Qt::KeyboardModifiers modifiers, QEvent::Type type = QEvent::Drop);
    virtual ~QDropEvent();
    QPoint pos() const;
    const QPointF &posF() const;
    Qt::MouseButtons mouseButtons() const;
    Qt::KeyboardModifiers keyboardModifiers() const;
    Qt::DropActions possibleActions() const;
    Qt::DropAction proposedAction() const;
    void acceptProposedAction();
    Qt::DropAction dropAction() const;
    void setDropAction(Qt::DropAction action);
    QObject *source() const;
    const QMimeData *mimeData() const;
};

class QDragMoveEvent : public QDropEvent
{
%TypeHeaderCode
#include <qevent.h>
%End

public:
    QDragMoveEvent(const QPoint &pos, Qt::DropActions actions, const QMimeData *data, Qt::MouseButtons buttons, Qt::KeyboardModifiers modifiers, QEvent::Type type = QEvent::DragMove);
    virtual ~QDragMoveEvent();
    QRect answerRect() const;
    void accept();
    void ignore();
    void accept(const QRect &r);
    void ignore(const QRect &r);
};

class QDragEnterEvent : public QDragMoveEvent
{
%TypeHeaderCode
#include <qevent.h>
%End

public:
    QDragEnterEvent(const QPoint &pos, Qt::DropActions actions, const QMimeData *data, Qt::MouseButtons buttons, Qt::KeyboardModifiers modifiers);
    virtual ~QDragEnterEvent();
};

class QDragLeaveEvent : public QEvent
{
%TypeHeaderCode
#include <qevent.h>
%End

public:
    QDragLeaveEvent();
    virtual ~QDragLeaveEvent();
};

class QHelpEvent : public QEvent
{
%TypeHeaderCode
#include <qevent.h>
%End

public:
    QHelpEvent(QEvent::Type type, const QPoint &pos, const QPoint &globalPos);
    virtual ~QHelpEvent();
    int x() const;
    int y() const;
    int globalX() const;
    int globalY() const;
    const QPoint &pos() const;
    const QPoint &globalPos() const;
};

class QStatusTipEvent : public QEvent
{
%TypeHeaderCode
#include <qevent.h>
%End

public:
    explicit QStatusTipEvent(const QString &tip);
    virtual ~QStatusTipEvent();
    QString tip() const;
};

class QWhatsThisClickedEvent : public QEvent
{
%TypeHeaderCode
#include <qevent.h>
%End

public:
    explicit QWhatsThisClickedEvent(const QString &href);
    virtual ~QWhatsThisClickedEvent();
    QString href() const;
};

class QActionEvent : public QEvent
{
%TypeHintCode
from PyQt5.QtWidgets import QAction
%End

%TypeHeaderCode
#include <qevent.h>
%End

public:
    QActionEvent(int type, QAction *action, QAction *before = 0);
    virtual ~QActionEvent();
    QAction *action() const;
    QAction *before() const;
};

class QFileOpenEvent : public QEvent /NoDefaultCtors/
{
%TypeHeaderCode
#include <qevent.h>
%End

public:
    virtual ~QFileOpenEvent();
    QString file() const;
    QUrl url() const;
    bool openFile(QFile &file, QIODevice::OpenMode flags) const;
};

class QShortcutEvent : public QEvent
{
%TypeHeaderCode
#include <qevent.h>
%End

public:
    QShortcutEvent(const QKeySequence &key, int id, bool ambiguous = false);
    virtual ~QShortcutEvent();
    bool isAmbiguous() const;
    const QKeySequence &key() const;
    int shortcutId() const;
};

class QWindowStateChangeEvent : public QEvent /NoDefaultCtors/
{
%TypeHeaderCode
#include <qevent.h>
%End

public:
    virtual ~QWindowStateChangeEvent();
    Qt::WindowStates oldState() const;
};

class QTouchEvent : public QInputEvent
{
%TypeHeaderCode
#include <qevent.h>
%End

public:
    class TouchPoint /NoDefaultCtors/
    {
%TypeHeaderCode
#include <qevent.h>
%End

    public:
        int id() const;
        Qt::TouchPointState state() const;
        QPointF pos() const;
        QPointF startPos() const;
        QPointF lastPos() const;
        QPointF scenePos() const;
        QPointF startScenePos() const;
        QPointF lastScenePos() const;
        QPointF screenPos() const;
        QPointF startScreenPos() const;
        QPointF lastScreenPos() const;
        QPointF normalizedPos() const;
        QPointF startNormalizedPos() const;
        QPointF lastNormalizedPos() const;
        QRectF rect() const;
        QRectF sceneRect() const;
        QRectF screenRect() const;
        qreal pressure() const;

        enum InfoFlag
        {
            Pen,
%If (Qt_5_8_0 -)
            Token,
%End
        };

        typedef QFlags<QTouchEvent::TouchPoint::InfoFlag> InfoFlags;
        QVector2D velocity() const;
        QTouchEvent::TouchPoint::InfoFlags flags() const;
        QVector<QPointF> rawScreenPositions() const;
%If (Qt_5_8_0 -)
        QPointingDeviceUniqueId uniqueId() const;
%End
%If (Qt_5_8_0 -)
        qreal rotation() const;
%End
%If (Qt_5_9_0 -)
        QSizeF ellipseDiameters() const;
%End
    };

    QTouchEvent(QEvent::Type eventType, QTouchDevice *device = 0, Qt::KeyboardModifiers modifiers = Qt::NoModifier, Qt::TouchPointStates touchPointStates = Qt::TouchPointStates(), const QList<QTouchEvent::TouchPoint> &touchPoints = QList<QTouchEvent::TouchPoint>());
    virtual ~QTouchEvent();
    QObject *target() const;
    Qt::TouchPointStates touchPointStates() const;
    const QList<QTouchEvent::TouchPoint> &touchPoints() const;
    QWindow *window() const;
    QTouchDevice *device() const;
    void setDevice(QTouchDevice *adevice);
};

QFlags<QTouchEvent::TouchPoint::InfoFlag> operator|(QTouchEvent::TouchPoint::InfoFlag f1, QFlags<QTouchEvent::TouchPoint::InfoFlag> f2);
QFlags<QTouchEvent::TouchPoint::InfoFlag> operator|(QTouchEvent::TouchPoint::InfoFlag f1, QTouchEvent::TouchPoint::InfoFlag f2);

class QExposeEvent : public QEvent
{
%TypeHeaderCode
#include <qevent.h>
%End

public:
    explicit QExposeEvent(const QRegion &rgn);
    virtual ~QExposeEvent();
    const QRegion &region() const;
};

class QScrollPrepareEvent : public QEvent
{
%TypeHeaderCode
#include <qevent.h>
%End

public:
    explicit QScrollPrepareEvent(const QPointF &startPos);
    virtual ~QScrollPrepareEvent();
    QPointF startPos() const;
    QSizeF viewportSize() const;
    QRectF contentPosRange() const;
    QPointF contentPos() const;
    void setViewportSize(const QSizeF &size);
    void setContentPosRange(const QRectF &rect);
    void setContentPos(const QPointF &pos);
};

class QScrollEvent : public QEvent
{
%TypeHeaderCode
#include <qevent.h>
%End

public:
    enum ScrollState
    {
        ScrollStarted,
        ScrollUpdated,
        ScrollFinished,
    };

    QScrollEvent(const QPointF &contentPos, const QPointF &overshoot, QScrollEvent::ScrollState scrollState);
    virtual ~QScrollEvent();
    QPointF contentPos() const;
    QPointF overshootDistance() const;
    QScrollEvent::ScrollState scrollState() const;
};

bool operator==(QKeyEvent *e, QKeySequence::StandardKey key);
bool operator==(QKeySequence::StandardKey key, QKeyEvent *e);

class QEnterEvent : public QEvent
{
%TypeHeaderCode
#include <qevent.h>
%End

public:
    QEnterEvent(const QPointF &localPos, const QPointF &windowPos, const QPointF &screenPos);
    virtual ~QEnterEvent();
    QPoint pos() const;
    QPoint globalPos() const;
    int x() const;
    int y() const;
    int globalX() const;
    int globalY() const;
    const QPointF &localPos() const;
    const QPointF &windowPos() const;
    const QPointF &screenPos() const;
};

class QAction /External/;
%If (Qt_5_2_0 -)

class QNativeGestureEvent : public QInputEvent
{
%TypeHeaderCode
#include <qevent.h>
%End

public:
    QNativeGestureEvent(Qt::NativeGestureType type, const QPointF &localPos, const QPointF &windowPos, const QPointF &screenPos, qreal value, ulong sequenceId, quint64 intArgument);
%If (Qt_5_10_0 -)
    QNativeGestureEvent(Qt::NativeGestureType type, const QTouchDevice *dev, const QPointF &localPos, const QPointF &windowPos, const QPointF &screenPos, qreal value, ulong sequenceId, quint64 intArgument);
%End
%If (Qt_5_10_0 -)
    virtual ~QNativeGestureEvent();
%End
    Qt::NativeGestureType gestureType() const;
    qreal value() const;
    const QPoint pos() const;
    const QPoint globalPos() const;
    const QPointF &localPos() const;
    const QPointF &windowPos() const;
    const QPointF &screenPos() const;
%If (Qt_5_10_0 -)
    const QTouchDevice *device() const;
%End
};

%End
%If (Qt_5_5_0 -)

class QPlatformSurfaceEvent : public QEvent
{
%TypeHeaderCode
#include <qevent.h>
%End

public:
    enum SurfaceEventType
    {
        SurfaceCreated,
        SurfaceAboutToBeDestroyed,
    };

    explicit QPlatformSurfaceEvent(QPlatformSurfaceEvent::SurfaceEventType surfaceEventType);
    virtual ~QPlatformSurfaceEvent();
    QPlatformSurfaceEvent::SurfaceEventType surfaceEventType() const;
};

%End
%If (Qt_5_8_0 -)

class QPointingDeviceUniqueId
{
%TypeHeaderCode
#include <qevent.h>
%End

public:
    QPointingDeviceUniqueId();
    static QPointingDeviceUniqueId fromNumericId(qint64 id);
    bool isValid() const;
    qint64 numericId() const;
    long __hash__() const;
%MethodCode
        sipRes = qHash(*sipCpp);
%End
};

%End
%If (Qt_5_8_0 -)
bool operator==(QPointingDeviceUniqueId lhs, QPointingDeviceUniqueId rhs);
%End
%If (Qt_5_8_0 -)
bool operator!=(QPointingDeviceUniqueId lhs, QPointingDeviceUniqueId rhs);
%End
