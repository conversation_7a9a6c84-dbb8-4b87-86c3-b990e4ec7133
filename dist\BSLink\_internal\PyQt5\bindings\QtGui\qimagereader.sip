// qimagereader.sip generated by MetaSIP
//
// This file is part of the QtGui Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QImageReader
{
%TypeHeaderCode
#include <qimagereader.h>
%End

public:
    enum ImageReaderError
    {
        <PERSON><PERSON><PERSON><PERSON>,
        FileNotFoundError,
        <PERSON><PERSON><PERSON><PERSON><PERSON>,
        UnsupportedFormatError,
        InvalidDataError,
    };

    QImageReader();
    QImageReader(QIODevice *device, const QByteArray &format = QByteArray());
    QImageReader(const QString &fileName, const QByteArray &format = QByteArray());
    ~QImageReader();
    void setFormat(const QByteArray &format);
    QByteArray format() const;
    void setDevice(QIODevice *device);
    QIODevice *device() const;
    void setFileName(const QString &fileName);
    QString fileName() const;
    QSize size() const;
    void setClipRect(const QRect &rect);
    QRect clipRect() const;
    void setScaledSize(const QSize &size);
    QSize scaledSize() const;
    void setScaledClipRect(const QRect &rect);
    QRect scaledClipRect() const;
    bool canRead() const;
    QImage read() /ReleaseGIL/;
    bool read(QImage *image) /ReleaseGIL/;
    bool jumpToNextImage();
    bool jumpToImage(int imageNumber);
    int loopCount() const;
    int imageCount() const;
    int nextImageDelay() const;
    int currentImageNumber() const;
    QRect currentImageRect() const;
    QImageReader::ImageReaderError error() const;
    QString errorString() const;
    static QByteArray imageFormat(const QString &fileName);
    static QByteArray imageFormat(QIODevice *device);
    static QList<QByteArray> supportedImageFormats();
    QStringList textKeys() const;
    QString text(const QString &key) const;
    void setBackgroundColor(const QColor &color);
    QColor backgroundColor() const;
    bool supportsAnimation() const;
    void setQuality(int quality);
    int quality() const;
    bool supportsOption(QImageIOHandler::ImageOption option) const;
    void setAutoDetectImageFormat(bool enabled);
    bool autoDetectImageFormat() const;
    QImage::Format imageFormat() const;
    void setDecideFormatFromContent(bool ignored);
    bool decideFormatFromContent() const;
%If (Qt_5_1_0 -)
    static QList<QByteArray> supportedMimeTypes();
%End
%If (Qt_5_4_0 -)
    QByteArray subType() const;
%End
%If (Qt_5_4_0 -)
    QList<QByteArray> supportedSubTypes() const;
%End
%If (Qt_5_5_0 -)
    QImageIOHandler::Transformations transformation() const;
%End
%If (Qt_5_5_0 -)
    void setAutoTransform(bool enabled);
%End
%If (Qt_5_5_0 -)
    bool autoTransform() const;
%End
%If (Qt_5_6_0 -)
    void setGamma(float gamma);
%End
%If (Qt_5_6_0 -)
    float gamma() const;
%End
%If (Qt_5_12_0 -)
    static QList<QByteArray> imageFormatsForMimeType(const QByteArray &mimeType);
%End

private:
    QImageReader(const QImageReader &);
};
