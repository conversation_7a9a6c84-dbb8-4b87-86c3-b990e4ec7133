// qoperatingsystemversion.sip generated by MetaSIP
//
// This file is part of the QtCore Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICEN<PERSON> included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%If (Qt_5_9_0 -)

class QOperatingSystemVersion
{
%TypeHeaderCode
#include <qoperatingsystemversion.h>
%End

public:
    enum OSType
    {
        Unknown,
        Windows,
        MacOS,
        IOS,
        TvOS,
        WatchOS,
        Android,
    };

    static const QOperatingSystemVersion Windows7;
    static const QOperatingSystemVersion Windows8;
    static const QOperatingSystemVersion Windows8_1;
    static const QOperatingSystemVersion Windows10;
    static const QOperatingSystemVersion OSXMavericks;
    static const QOperatingSystemVersion OSXYosemite;
    static const QOperatingSystemVersion OSXElCapitan;
    static const QOperatingSystemVersion MacOSSierra;
%If (Qt_5_9_1 -)
    static const QOperatingSystemVersion MacOSHighSierra;
%End
%If (Qt_5_11_2 -)
    static const QOperatingSystemVersion MacOSMojave;
%End
%If (Qt_5_14_0 -)
    static const QOperatingSystemVersion MacOSCatalina;
%End
%If (Qt_5_15_1 -)
    static const QOperatingSystemVersion MacOSBigSur;
%End
    static const QOperatingSystemVersion AndroidJellyBean;
    static const QOperatingSystemVersion AndroidJellyBean_MR1;
    static const QOperatingSystemVersion AndroidJellyBean_MR2;
    static const QOperatingSystemVersion AndroidKitKat;
    static const QOperatingSystemVersion AndroidLollipop;
    static const QOperatingSystemVersion AndroidLollipop_MR1;
    static const QOperatingSystemVersion AndroidMarshmallow;
    static const QOperatingSystemVersion AndroidNougat;
    static const QOperatingSystemVersion AndroidNougat_MR1;
%If (Qt_5_9_2 -)
    static const QOperatingSystemVersion AndroidOreo;
%End
%If (- Qt_5_9_2)
    QOperatingSystemVersion(const QOperatingSystemVersion &other);
%End
    QOperatingSystemVersion(QOperatingSystemVersion::OSType osType, int vmajor, int vminor = -1, int vmicro = -1);
    static QOperatingSystemVersion current();
%If (Qt_5_10_0 -)
    static QOperatingSystemVersion::OSType currentType();
%End
    int majorVersion() const;
    int minorVersion() const;
    int microVersion() const;
    int segmentCount() const;
    QOperatingSystemVersion::OSType type() const;
    QString name() const;

private:
    QOperatingSystemVersion();
};

%End
%If (Qt_5_9_0 -)
bool operator>(const QOperatingSystemVersion &lhs, const QOperatingSystemVersion &rhs);
%End
%If (Qt_5_9_0 -)
bool operator>=(const QOperatingSystemVersion &lhs, const QOperatingSystemVersion &rhs);
%End
%If (Qt_5_9_0 -)
bool operator<(const QOperatingSystemVersion &lhs, const QOperatingSystemVersion &rhs);
%End
%If (Qt_5_9_0 -)
bool operator<=(const QOperatingSystemVersion &lhs, const QOperatingSystemVersion &rhs);
%End
