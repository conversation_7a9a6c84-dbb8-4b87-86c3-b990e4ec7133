// qlowenergycontroller.sip generated by MetaSIP
//
// This file is part of the QtBluetooth Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICEN<PERSON> included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%If (Qt_5_4_0 -)

class QLowEnergyController : public QObject
{
%TypeHeaderCode
#include <qlowenergycontroller.h>
%End

public:
    enum Error
    {
        No<PERSON><PERSON><PERSON>,
        <PERSON><PERSON><PERSON><PERSON>,
        UnknownRemoteDeviceError,
        NetworkError,
        InvalidBluetoothAdapterError,
%If (Qt_5_5_0 -)
        ConnectionError,
%End
%If (Qt_5_7_0 -)
        AdvertisingError,
%End
%If (Qt_5_10_0 -)
        RemoteHostClosedError,
%End
%If (Qt_5_14_0 -)
        AuthorizationError,
%End
    };

    enum ControllerState
    {
        UnconnectedState,
        ConnectingState,
        ConnectedState,
        DiscoveringState,
        DiscoveredState,
        ClosingState,
%If (Qt_5_7_0 -)
        AdvertisingState,
%End
    };

    enum RemoteAddressType
    {
        PublicAddress,
        RandomAddress,
    };

%If (Qt_5_5_0 -)
    QLowEnergyController(const QBluetoothDeviceInfo &remoteDevice, QObject *parent /TransferThis/ = 0);
%End
    QLowEnergyController(const QBluetoothAddress &remoteDevice, QObject *parent /TransferThis/ = 0);
    QLowEnergyController(const QBluetoothAddress &remoteDevice, const QBluetoothAddress &localDevice, QObject *parent /TransferThis/ = 0);
    virtual ~QLowEnergyController();
    QBluetoothAddress localAddress() const;
    QBluetoothAddress remoteAddress() const;
    QLowEnergyController::ControllerState state() const;
    QLowEnergyController::RemoteAddressType remoteAddressType() const;
    void setRemoteAddressType(QLowEnergyController::RemoteAddressType type);
    void connectToDevice();
    void disconnectFromDevice();
    void discoverServices();
    QList<QBluetoothUuid> services() const;
    QLowEnergyService *createServiceObject(const QBluetoothUuid &service, QObject *parent /TransferThis/ = 0) /Factory/;
    QLowEnergyController::Error error() const;
    QString errorString() const;
%If (Qt_5_5_0 -)
    QString remoteName() const;
%End

signals:
    void connected();
    void disconnected();
    void stateChanged(QLowEnergyController::ControllerState state);
    void error(QLowEnergyController::Error newError);
    void serviceDiscovered(const QBluetoothUuid &newService);
    void discoveryFinished();

public:
%If (Qt_5_7_0 -)

    enum Role
    {
        CentralRole,
        PeripheralRole,
    };

%End
%If (Qt_5_7_0 -)
    static QLowEnergyController *createCentral(const QBluetoothDeviceInfo &remoteDevice, QObject *parent /TransferThis/ = 0) /Factory/;
%End
%If (Qt_5_14_0 -)
    static QLowEnergyController *createCentral(const QBluetoothAddress &remoteDevice, const QBluetoothAddress &localDevice, QObject *parent /TransferThis/ = 0) /Factory/;
%End
%If (Qt_5_7_0 -)
    static QLowEnergyController *createPeripheral(QObject *parent /TransferThis/ = 0) /Factory/;
%End
%If (Qt_5_7_0 -)
    void startAdvertising(const QLowEnergyAdvertisingParameters &parameters, const QLowEnergyAdvertisingData &advertisingData, const QLowEnergyAdvertisingData &scanResponseData = QLowEnergyAdvertisingData());
%End
%If (Qt_5_7_0 -)
    void stopAdvertising();
%End
%If (Qt_5_7_0 -)
    QLowEnergyService *addService(const QLowEnergyServiceData &service, QObject *parent /TransferThis/ = 0) /Factory/;
%End
%If (Qt_5_7_0 -)
    void requestConnectionUpdate(const QLowEnergyConnectionParameters &parameters);
%End
%If (Qt_5_7_0 -)
    QLowEnergyController::Role role() const;
%End

signals:
%If (Qt_5_7_0 -)
    void connectionUpdated(const QLowEnergyConnectionParameters &parameters);
%End

public:
%If (Qt_5_8_0 -)
    QBluetoothUuid remoteDeviceUuid() const;
%End

private:
%If (Qt_5_7_0 -)
    explicit QLowEnergyController(QObject *parent = 0);
%End
};

%End
