// qgeorouterequest.sip generated by MetaSIP
//
// This file is part of the QtLocation Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%If (Qt_5_5_0 -)

class QGeoRouteRequest
{
%TypeHeaderCode
#include <qgeorouterequest.h>
%End

public:
    enum TravelMode
    {
        CarTravel,
        PedestrianTravel,
        BicycleTravel,
        PublicTransitTravel,
        TruckTravel,
    };

    typedef QFlags<QGeoRouteRequest::TravelMode> TravelModes;

    enum FeatureType
    {
        NoFeature,
        TollFeature,
        HighwayFeature,
        PublicTransitFeature,
        FerryFeature,
        TunnelFeature,
        DirtRoadFeature,
        ParksFeature,
        MotorPoolLaneFeature,
%If (Qt_5_10_0 -)
        TrafficFeature,
%End
    };

    typedef QFlags<QGeoRouteRequest::FeatureType> FeatureTypes;

    enum FeatureWeight
    {
        NeutralFeatureWeight,
        PreferFeatureWeight,
        RequireFeatureWeight,
        AvoidFeatureWeight,
        DisallowFeatureWeight,
    };

    typedef QFlags<QGeoRouteRequest::FeatureWeight> FeatureWeights;

    enum RouteOptimization
    {
        ShortestRoute,
        FastestRoute,
        MostEconomicRoute,
        MostScenicRoute,
    };

    typedef QFlags<QGeoRouteRequest::RouteOptimization> RouteOptimizations;

    enum SegmentDetail
    {
        NoSegmentData,
        BasicSegmentData,
    };

    typedef QFlags<QGeoRouteRequest::SegmentDetail> SegmentDetails;

    enum ManeuverDetail
    {
        NoManeuvers,
        BasicManeuvers,
    };

    typedef QFlags<QGeoRouteRequest::ManeuverDetail> ManeuverDetails;
    explicit QGeoRouteRequest(const QList<QGeoCoordinate> &waypoints = QList<QGeoCoordinate>());
    QGeoRouteRequest(const QGeoCoordinate &origin, const QGeoCoordinate &destination);
    QGeoRouteRequest(const QGeoRouteRequest &other);
    ~QGeoRouteRequest();
    bool operator==(const QGeoRouteRequest &other) const;
    bool operator!=(const QGeoRouteRequest &other) const;
    void setWaypoints(const QList<QGeoCoordinate> &waypoints);
    QList<QGeoCoordinate> waypoints() const;
    void setExcludeAreas(const QList<QGeoRectangle> &areas);
    QList<QGeoRectangle> excludeAreas() const;
    void setNumberAlternativeRoutes(int alternatives);
    int numberAlternativeRoutes() const;
    void setTravelModes(QGeoRouteRequest::TravelModes travelModes);
    QGeoRouteRequest::TravelModes travelModes() const;
    void setFeatureWeight(QGeoRouteRequest::FeatureType featureType, QGeoRouteRequest::FeatureWeight featureWeight);
    QGeoRouteRequest::FeatureWeight featureWeight(QGeoRouteRequest::FeatureType featureType) const;
    QList<QGeoRouteRequest::FeatureType> featureTypes() const;
    void setRouteOptimization(QGeoRouteRequest::RouteOptimizations optimization);
    QGeoRouteRequest::RouteOptimizations routeOptimization() const;
    void setSegmentDetail(QGeoRouteRequest::SegmentDetail segmentDetail);
    QGeoRouteRequest::SegmentDetail segmentDetail() const;
    void setManeuverDetail(QGeoRouteRequest::ManeuverDetail maneuverDetail);
    QGeoRouteRequest::ManeuverDetail maneuverDetail() const;
%If (Qt_5_11_0 -)
    void setWaypointsMetadata(const QList<QVariantMap> &waypointMetadata);
%End
%If (Qt_5_11_0 -)
    QList<QVariantMap> waypointsMetadata() const;
%End
%If (Qt_5_11_0 -)
    void setExtraParameters(const QVariantMap &extraParameters);
%End
%If (Qt_5_11_0 -)
    QVariantMap extraParameters() const;
%End
%If (Qt_5_13_0 -)
    void setDepartureTime(const QDateTime &departureTime);
%End
%If (Qt_5_13_0 -)
    QDateTime departureTime() const;
%End
};

%End
%If (Qt_5_5_0 -)
QFlags<QGeoRouteRequest::TravelMode> operator|(QGeoRouteRequest::TravelMode f1, QFlags<QGeoRouteRequest::TravelMode> f2);
%End
%If (Qt_5_5_0 -)
QFlags<QGeoRouteRequest::FeatureType> operator|(QGeoRouteRequest::FeatureType f1, QFlags<QGeoRouteRequest::FeatureType> f2);
%End
%If (Qt_5_5_0 -)
QFlags<QGeoRouteRequest::FeatureWeight> operator|(QGeoRouteRequest::FeatureWeight f1, QFlags<QGeoRouteRequest::FeatureWeight> f2);
%End
%If (Qt_5_5_0 -)
QFlags<QGeoRouteRequest::RouteOptimization> operator|(QGeoRouteRequest::RouteOptimization f1, QFlags<QGeoRouteRequest::RouteOptimization> f2);
%End
%If (Qt_5_5_0 -)
QFlags<QGeoRouteRequest::SegmentDetail> operator|(QGeoRouteRequest::SegmentDetail f1, QFlags<QGeoRouteRequest::SegmentDetail> f2);
%End
%If (Qt_5_5_0 -)
QFlags<QGeoRouteRequest::ManeuverDetail> operator|(QGeoRouteRequest::ManeuverDetail f1, QFlags<QGeoRouteRequest::ManeuverDetail> f2);
%End
